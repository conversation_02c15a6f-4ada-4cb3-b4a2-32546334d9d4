import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/store';
import { useToast } from '@/components/ui/use-toast';
import apiService from '@/services/apiService';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Calendar,
  User,
  MapPin,
  FileText,
  Clock,
  Building,
  Phone,
  Users,
  Briefcase,
  Image as ImageIcon,
  Edit3
} from 'lucide-react';
import { format } from 'date-fns';
import SignatureCanvas from 'react-signature-canvas';
import ImageComponent from '@/components/common/ImageComponent';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import API from '@/services/axiosAPI';
import { API_BASE_URL } from '@/constants/index';
// API endpoints

const GET_USER_ROLE_BY_MODE = `${API_BASE_URL}/users/get_users`;
const FILE_URL = `${API_BASE_URL}/files`;
const CLOSEOUT_PERMIT_ACTION = (id: string) => `${API_BASE_URL}/permit-report-closeout/${id}`;
const CHANGE_PERMIT_STATUS = (id: string) => `${API_BASE_URL}/permit-reports/${id}`;

// Interface for permit update payload
interface PermitUpdatePayload {
  status: string;
  closeoutStatus: {
    signature: string;
    comments: string;
  };
  acknowledgerId: string;
}

interface PermitDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  permitId: string | null;
}

interface PermitDetails {
  id: string;
  maskId: string;
  status: string;
  permitWorkType: string;
  permitType: string[];
  permitStartDate: string;
  permitEndDate: string;
  workDescription: string;
  nameOfSiteSupervisor: string;
  noOfWorkers: number;
  supervisorContactNo: string;
  applicantContactNo: string;
  workOrderNo: string;
  supportingDocuments: string[];
  applicantId?: string;
  permitRiskControl: Array<{
    permitType: string;
    description: string;
    currentType: string;
    value: string;
    remarks: string;
    evidence: string[];
  }>;
  locationOne?: { name: string };
  locationTwo?: { name: string };
  locationThree?: { name: string };
  locationFour?: { name: string };
  locationFive?: { name: string };
  locationSix?: { name: string };
  applicant?: { firstName: string; lastName?: string };
  reviewer?: { firstName: string; lastName?: string };
  assessor?: { firstName: string; lastName?: string };
  approver?: { firstName: string; lastName?: string };
  applicantStatus?: {
    status: boolean;
    signature: string;
    signedDate: string;
    comments: string;
  };
  reviewerStatus?: {
    status: boolean;
    signature: string;
    signedDate: string;
    comments: string;
  };
  assessorStatus?: {
    status: boolean;
    signature: string;
    signedDate: string;
    comments: string;
  };
  approverStatus?: {
    status: boolean;
    signature: string;
    signedDate: string;
    comments: string;
  };
  closeoutStatus?: {
    status: string;
    signature: string;
    signedDate: string;
    comments: string;
    by: string;
  };
  acknowledgementStatus?: {
    status: string;
    signature: string;
    signedDate: string;
    comments: string;
    by: string;
  };
}

const PermitDetailsModal: React.FC<PermitDetailsModalProps> = ({
  open,
  onOpenChange,
  permitId
}) => {
  const [permitDetails, setPermitDetails] = useState<PermitDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useSelector((state: RootState) => state.auth);

  // Closeout functionality state
  const [closeoutChecked, setCloseoutChecked] = useState(false);
  const [signs, setSign] = useState('');
  const [signModal, setSignModal] = useState(false);
  const [knowledger, setKnowledger] = useState<Array<{ label: string; value: string }>>([]);
  const [knowledgerId, setKnowledgerId] = useState('');
  const signRef = useRef<SignatureCanvas>(null);

  // Fetch permit details
  useEffect(() => {
    if (open && permitId) {
      fetchPermitDetails();
    }
  }, [open, permitId]);

  const fetchPermitDetails = async () => {
    if (!permitId) return;
    
    setLoading(true);
    try {
      const uriString = {
        include: [
          { relation: "locationOne" },
          { relation: "locationTwo" },
          { relation: "locationThree" },
          { relation: "locationFour" },
          { relation: "locationFive" },
          { relation: "locationSix" },
          { relation: "applicant" },
          { relation: "reviewer" },
          { relation: "assessor" },
          { relation: "approver" },
        ]
      };
      
      const url = `/permit-reports/${permitId}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      const response = await apiService.get(url);
      setPermitDetails(response);
    } catch (error) {
      console.error('Error fetching permit details:', error);
      toast({
        title: "Error",
        description: "Failed to fetch permit details. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Helper function to format location
  const formatLocation = (details: PermitDetails) => {
    const locations = [
      details.locationOne?.name,
      details.locationTwo?.name,
      details.locationThree?.name,
      details.locationFour?.name,
      details.locationFive?.name,
      details.locationSix?.name
    ].filter(Boolean);
    return locations.join(' > ') || 'N/A';
  };

  // Helper function to format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'dd-MM-yyyy hh:mm a');
    } catch {
      return 'N/A';
    }
  };

  // Helper function to get status color
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'approved':
        return 'bg-green-500';
      case 'pending':
        return 'bg-yellow-500';
      case 'rejected':
      case 'expired':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Fetch acknowledgers for closeout
  const getCrewList = useCallback(async (type: string) => {
    try {
      const response = await apiService.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: "",
        locationTwoId: "",
        locationThreeId: "",
        locationFourId: "",
        mode: type,
      });

      if (response) {
        const data: Array<{ label: string; value: string }> = response.map((item: any) => ({
          label: item.firstName,
          value: item.id,
        }));
        setKnowledger(data);
      }
    } catch (error) {
      console.error("Error fetching crew list:", error);
    }
  }, []);

  // useEffect to fetch acknowledgers when modal opens
  useEffect(() => {
    if (open && permitDetails) {
      getCrewList('eptwAcknowledger');
    }
  }, [open, permitDetails, getCrewList]);

  // Convert data URI to file for signature upload
  const dataURItoFile = (dataURI: any, filename: any) => {
    var byteString = atob(dataURI.split(",")[1]);
    // separate out the mime component
    var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
    // write the bytes of the string to an ArrayBuffer
    var ab = new ArrayBuffer(byteString.length);
    var dw = new DataView(ab);
    for (var i = 0; i < byteString.length; i++) {
      dw.setUint8(i, byteString.charCodeAt(i));
    }

    // write the ArrayBuffer to a blob, and you're done
    return new File([ab], filename, { type: mimeString });
  };

  // Upload signature function
  const uploadSignature = async () => {
    const filename = `${new Date().getTime()}_captin_sign.png`;
    const formData1 = new FormData();
    const signatureFile = dataURItoFile(signs, filename);
    formData1.append('file', signatureFile);

    try {
      const response = await apiService.post(FILE_URL, formData1, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });

      if (response && response.status === 200) {
        // Return the uploaded file name
        return response.data.files[0].originalname;
      } else {
        throw new Error("File upload failed.");
      }
    } catch (error) {
      console.error("File upload error:", error);
      throw error;
    }
  };

  // Handle closeout submission
  const handleSubmit = async () => {
    // Validation
    if (!closeoutChecked) {
      toast({
        title: "Validation Error",
        description: "Please confirm the closeout checkbox before proceeding.",
        variant: "destructive"
      });
      return;
    }

    if (!signs) {
      toast({
        title: "Validation Error",
        description: "Please provide your signature to close out the permit.",
        variant: "destructive"
      });
      return;
    }

    if (!knowledgerId) {
      toast({
        title: "Validation Error",
        description: "Please select an Acknowledger before submitting.",
        variant: "destructive"
      });
      return;
    }

    try {
      const signatureFileName = await uploadSignature();

      const reqData: PermitUpdatePayload = {
        status: 'Closed',
        closeoutStatus: {
          signature: signatureFileName,
          comments: ''
        },
        acknowledgerId: knowledgerId
      };

      const response = await API.patch(CHANGE_PERMIT_STATUS(permitDetails!.id), reqData);

      if (response.status === 204) {
        toast({
          title: "Success",
          description: "Permit Closed Successfully",
        });

        // Refresh permit details
        fetchPermitDetails();

        // Reset closeout form
        setCloseoutChecked(false);
        setSign('');
        setKnowledgerId('');
      } else {
        toast({
          title: "Error",
          description: "Unexpected response from server",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Closeout failed:", error);
      toast({
        title: "Error",
        description: "Something went wrong while closing the permit",
        variant: "destructive"
      });
    }
  };

  if (!permitDetails && !loading) {
    return null;
  }

  return (
    <>
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Permit Details - {permitDetails?.maskId || 'Loading...'}
          </DialogTitle>
          <DialogDescription>
            Complete information about this permit
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading permit details...</span>
          </div>
        ) : permitDetails ? (
          <div className="space-y-8">
            {/* Header with Status */}
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {permitDetails.permitWorkType || 'N/A'}
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  {formatDate(permitDetails.permitStartDate)} - {formatDate(permitDetails.permitEndDate)}
                </p>
              </div>
              <Badge className={`${getStatusColor(permitDetails.status)} text-white px-3 py-1`}>
                {permitDetails.status}
              </Badge>
            </div>

            {/* Basic Information Card */}
            <Card className="shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <FileText className="h-5 w-5 text-blue-600" />
                  Work Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Work Description */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm font-semibold text-gray-700 mb-2">Work Description</p>
                  <p className="text-sm text-gray-900 leading-relaxed">
                    {permitDetails.workDescription || 'N/A'}
                  </p>
                </div>

                {/* Grid Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <p className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                      <User className="h-4 w-4 text-blue-500" />
                      Site Supervisor
                    </p>
                    <p className="text-sm text-gray-900 bg-white p-3 rounded border">
                      {permitDetails.nameOfSiteSupervisor || 'N/A'}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                      <Users className="h-4 w-4 text-green-500" />
                      Number of Workers
                    </p>
                    <p className="text-sm text-gray-900 bg-white p-3 rounded border">
                      {permitDetails.noOfWorkers || 'N/A'}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                      <Briefcase className="h-4 w-4 text-purple-500" />
                      Work Order No.
                    </p>
                    <p className="text-sm text-gray-900 bg-white p-3 rounded border">
                      {permitDetails.workOrderNo || 'N/A'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact & Location Information Card */}
            <Card className="shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Phone className="h-5 w-5 text-green-600" />
                  Contact & Location Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Contact Numbers */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <p className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                      <Phone className="h-4 w-4 text-blue-500" />
                      Supervisor Contact
                    </p>
                    <p className="text-sm text-gray-900 bg-white p-3 rounded border">
                      {permitDetails.supervisorContactNo || 'N/A'}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                      <Phone className="h-4 w-4 text-green-500" />
                      Applicant Contact
                    </p>
                    <p className="text-sm text-gray-900 bg-white p-3 rounded border">
                      {permitDetails.applicantContactNo || 'N/A'}
                    </p>
                  </div>
                </div>

                {/* Location */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-red-500" />
                    Work Location
                  </p>
                  <p className="text-sm text-gray-900 leading-relaxed">
                    {formatLocation(permitDetails)}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Supporting Documents */}
            {permitDetails.supportingDocuments && permitDetails.supportingDocuments.length > 0 && (
              <Card className="shadow-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <ImageIcon className="h-5 w-5 text-orange-600" />
                    Supporting Documents
                    <Badge variant="secondary" className="ml-2">
                      {permitDetails.supportingDocuments.length}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {permitDetails.supportingDocuments.map((doc, index) => (
                      <div key={index} className="group">
                        <div className="border-2 border-gray-200 rounded-lg p-3 bg-white shadow-sm hover:shadow-md transition-all duration-200 hover:border-blue-300">
                          <ImageComponent fileName={doc} size="100" name={true} />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Risk Controls */}
            {permitDetails.permitRiskControl && permitDetails.permitRiskControl.length > 0 && (
              <div className="space-y-6">
                {Object.entries(
                  permitDetails.permitRiskControl.reduce((acc: Record<string, Array<any>>, control, index) => {
                    const permitType = control.permitType || 'General';
                    if (!acc[permitType]) acc[permitType] = [];
                    acc[permitType].push({ ...control, controlIndex: index });
                    return acc;
                  }, {})
                ).map(([permitType, controls]) => (
                  <Card key={permitType} className="shadow-sm">
                    <CardHeader className="pb-4 bg-gradient-to-r from-purple-50 to-pink-50">
                      <CardTitle className="text-lg font-semibold text-purple-800 flex items-center gap-2">
                        <Building className="h-5 w-5" />
                        {permitType}
                        <Badge variant="secondary" className="ml-2">
                          {controls.length} Controls
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-6">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {controls.map((control: any) => (
                          <div key={control.controlIndex} className="border-2 border-gray-200 rounded-lg p-5 bg-gradient-to-br from-white to-gray-50 hover:shadow-md transition-all duration-200">
                            {/* Control Header */}
                            <div className="mb-4 pb-3 border-b border-gray-200">
                              <div className="flex items-start gap-3">
                                <div className="bg-blue-100 text-blue-800 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
                                  {control.controlIndex + 1}
                                </div>
                                <div className="flex-1">
                                  <p className="font-medium text-gray-900 leading-relaxed">
                                    {control.description}
                                  </p>
                                </div>
                              </div>
                            </div>

                            {/* Control Details */}
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Type</p>
                                  <p className="text-sm text-gray-900 bg-white p-2 rounded border">
                                    {control.currentType || 'N/A'}
                                  </p>
                                </div>
                                <div>
                                  <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Value</p>
                                  <p className="text-sm text-gray-900 bg-white p-2 rounded border">
                                    {control.value || 'N/A'}
                                  </p>
                                </div>
                              </div>

                              <div>
                                <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Remarks</p>
                                <p className="text-sm text-gray-900 bg-white p-3 rounded border leading-relaxed">
                                  {control.remarks || 'N/A'}
                                </p>
                              </div>

                              {/* Evidence Section */}
                              {control.evidence && control.evidence.length > 0 && (
                                <div className="pt-3 border-t border-gray-200">
                                  <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3 flex items-center gap-2">
                                    <ImageIcon className="h-4 w-4" />
                                    Evidence Files
                                    <Badge variant="outline" className="text-xs">
                                      {control.evidence.length}
                                    </Badge>
                                  </p>
                                  <div className="grid grid-cols-2 gap-3">
                                    {control.evidence.map((evidence: string, evidenceIndex: number) => (
                                      <div key={evidenceIndex} className="group">
                                        <div className="border-2 border-gray-200 rounded-lg p-2 bg-white shadow-sm hover:shadow-md transition-all duration-200 hover:border-blue-300">
                                          <ImageComponent fileName={evidence} size="80" name={false} />
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Status Details */}
            <Card className="shadow-sm">
              <CardHeader className="pb-4 bg-gradient-to-r from-green-50 to-blue-50">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <User className="h-5 w-5 text-green-600" />
                  Approval Status & Signatures
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="space-y-8">
                  {/* Applicant Status */}
                  <div className="border-2 border-blue-200 rounded-lg p-6 bg-gradient-to-br from-blue-50 to-white">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="bg-blue-100 p-2 rounded-full">
                        <User className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg text-blue-900">
                          Applicant
                        </h4>
                        <p className="text-sm text-blue-700">
                          {permitDetails.applicant?.firstName || 'N/A'}
                        </p>
                      </div>
                      <div className="ml-auto">
                        <Badge className={`${permitDetails.applicantStatus?.status ? 'bg-green-500' : 'bg-yellow-500'} text-white px-3 py-1`}>
                          {permitDetails.applicantStatus?.status ? 'Approved' : 'Pending'}
                        </Badge>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="bg-white p-4 rounded-lg border">
                        <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Declaration</p>
                        <p className="text-sm text-gray-700 leading-relaxed italic">
                          "I confirm that all required fields are accurately completed, and I acknowledge responsibility for adhering to the specified safety controls for this work activity."
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-white p-4 rounded-lg border">
                          <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Signature</p>
                          <div className="flex items-center justify-center min-h-[60px]">
                            {permitDetails.applicantStatus?.signature ? (
                              <ImageComponent fileName={permitDetails.applicantStatus.signature} size="50" name={false} />
                            ) : (
                              <span className="text-gray-400 text-sm">No signature</span>
                            )}
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div className="bg-white p-3 rounded-lg border">
                            <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Signed Date</p>
                            <p className="text-sm text-gray-900">
                              {formatDate(permitDetails.applicantStatus?.signedDate || '')}
                            </p>
                          </div>
                          <div className="bg-white p-3 rounded-lg border">
                            <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Comments</p>
                            <p className="text-sm text-gray-900">
                              {permitDetails.applicantStatus?.comments || 'N/A'}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Reviewer Status (if exists and user is external) */}
                  {permitDetails.reviewer && user?.type !== 'Internal' && (
                    <div className="border-2 border-purple-200 rounded-lg p-6 bg-gradient-to-br from-purple-50 to-white">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="bg-purple-100 p-2 rounded-full">
                          <User className="h-5 w-5 text-purple-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-lg text-purple-900">
                            Reviewer
                          </h4>
                          <p className="text-sm text-purple-700">
                            {permitDetails.reviewer.firstName || 'N/A'}
                          </p>
                        </div>
                        <div className="ml-auto">
                          <Badge className={`${permitDetails.reviewerStatus?.status ? 'bg-green-500' : 'bg-yellow-500'} text-white px-3 py-1`}>
                            {permitDetails.reviewerStatus?.status ? 'Approved' : 'Pending'}
                          </Badge>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="bg-white p-4 rounded-lg border">
                          <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Declaration</p>
                          <p className="text-sm text-gray-700 leading-relaxed italic">
                            "I have reviewed the application details and verify that the listed controls and prerequisites are suitable and sufficient for safe task execution."
                          </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-white p-4 rounded-lg border">
                            <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Signature</p>
                            <div className="flex items-center justify-center min-h-[60px]">
                              {permitDetails.reviewerStatus?.signature ? (
                                <ImageComponent fileName={permitDetails.reviewerStatus.signature} size="50" name={false} />
                              ) : (
                                <span className="text-gray-400 text-sm">No signature</span>
                              )}
                            </div>
                          </div>

                          <div className="space-y-3">
                            <div className="bg-white p-3 rounded-lg border">
                              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Signed Date</p>
                              <p className="text-sm text-gray-900">
                                {formatDate(permitDetails.reviewerStatus?.signedDate || '')}
                              </p>
                            </div>
                            <div className="bg-white p-3 rounded-lg border">
                              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Comments</p>
                              <p className="text-sm text-gray-900">
                                {permitDetails.reviewerStatus?.comments || 'N/A'}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Assessor Status (if exists) */}
                  {permitDetails.assessor && (
                    <div className="border-2 border-green-200 rounded-lg p-6 bg-gradient-to-br from-green-50 to-white">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="bg-green-100 p-2 rounded-full">
                          <User className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-lg text-green-900">
                            Assessor
                          </h4>
                          <p className="text-sm text-green-700">
                            {permitDetails.assessor.firstName || 'N/A'}
                          </p>
                        </div>
                        <div className="ml-auto">
                          <Badge className={`${permitDetails.assessorStatus?.status ? 'bg-green-500' : 'bg-yellow-500'} text-white px-3 py-1`}>
                            {permitDetails.assessorStatus?.status ? 'Approved' : 'Pending'}
                          </Badge>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="bg-white p-4 rounded-lg border">
                          <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Declaration</p>
                          <p className="text-sm text-gray-700 leading-relaxed italic">
                            "I affirm that I have carefully assessed the risk levels, controls, and work conditions and that all necessary precautions are documented."
                          </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-white p-4 rounded-lg border">
                            <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Signature</p>
                            <div className="flex items-center justify-center min-h-[60px]">
                              {permitDetails.assessorStatus?.signature ? (
                                <ImageComponent fileName={permitDetails.assessorStatus.signature} size="50" name={false} />
                              ) : (
                                <span className="text-gray-400 text-sm">No signature</span>
                              )}
                            </div>
                          </div>

                          <div className="space-y-3">
                            <div className="bg-white p-3 rounded-lg border">
                              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Signed Date</p>
                              <p className="text-sm text-gray-900">
                                {formatDate(permitDetails.assessorStatus?.signedDate || '')}
                              </p>
                            </div>
                            <div className="bg-white p-3 rounded-lg border">
                              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Comments</p>
                              <p className="text-sm text-gray-900">
                                {permitDetails.assessorStatus?.comments || 'N/A'}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Approver Status (if exists) */}
                  {permitDetails.approver && (
                    <div className="border-2 border-orange-200 rounded-lg p-6 bg-gradient-to-br from-orange-50 to-white">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="bg-orange-100 p-2 rounded-full">
                          <User className="h-5 w-5 text-orange-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-lg text-orange-900">
                            Approver
                          </h4>
                          <p className="text-sm text-orange-700">
                            {permitDetails.approver.firstName || 'N/A'}
                          </p>
                        </div>
                        <div className="ml-auto">
                          <Badge className={`${permitDetails.approverStatus?.status ? 'bg-green-500' : 'bg-yellow-500'} text-white px-3 py-1`}>
                            {permitDetails.approverStatus?.status ? 'Approved' : 'Pending'}
                          </Badge>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="bg-white p-4 rounded-lg border">
                          <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Declaration</p>
                          <p className="text-sm text-gray-700 leading-relaxed italic">
                            "I approve this permit with the assurance that all safety measures and controls have been verified and are in place to safely conduct this work."
                          </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-white p-4 rounded-lg border">
                            <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Signature</p>
                            <div className="flex items-center justify-center min-h-[60px]">
                              {permitDetails.approverStatus?.signature ? (
                                <ImageComponent fileName={permitDetails.approverStatus.signature} size="50" name={false} />
                              ) : (
                                <span className="text-gray-400 text-sm">No signature</span>
                              )}
                            </div>
                          </div>

                          <div className="space-y-3">
                            <div className="bg-white p-3 rounded-lg border">
                              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Signed Date</p>
                              <p className="text-sm text-gray-900">
                                {formatDate(permitDetails.approverStatus?.signedDate || '')}
                              </p>
                            </div>
                            <div className="bg-white p-3 rounded-lg border">
                              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Comments</p>
                              <p className="text-sm text-gray-900">
                                {permitDetails.approverStatus?.comments || 'N/A'}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Closeout Functionality */}
                  {permitDetails.status === "Active" && user?.id === permitDetails.applicantId && (
                    <Card className="border-2 border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                      <CardContent className="p-6">
                        <div className="space-y-4">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="closeout-confirm"
                              checked={closeoutChecked}
                              onCheckedChange={(checked) => setCloseoutChecked(checked === true)}
                            />
                            <label
                              htmlFor="closeout-confirm"
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              The task(s) has been completed. The work area(s) have been left in a tidy and safe condition.
                            </label>
                          </div>

                          {closeoutChecked && (
                            <div className="space-y-4">
                              {/* Signature Section */}
                              <Card className="shadow-sm">
                                <CardContent className="p-4">
                                  <div className="text-center">
                                    <div
                                      className="border-2 border-dashed border-gray-300 rounded-lg p-8 cursor-pointer hover:border-orange-400 transition-colors"
                                      onClick={() => setSignModal(true)}
                                    >
                                      <Edit3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                                      {signs ? (
                                        <img src={signs} alt="Signature" className="h-20 mx-auto mb-2" />
                                      ) : (
                                        <p className="text-gray-600">Click to sign</p>
                                      )}
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>

                              {/* Acknowledger Selection */}
                              <Card className="shadow-sm">
                                <CardContent className="p-4">
                                  <div className="space-y-2">
                                    <label className="text-sm font-medium">Identify Acknowledger to closeOut</label>
                                    <Select value={knowledgerId} onValueChange={setKnowledgerId}>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select Acknowledger" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {knowledger.map((option) => (
                                          <SelectItem key={option.value} value={option.value}>
                                            {option.label}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </CardContent>
                              </Card>

                              {/* Submit Button */}
                              <div className="text-right">
                                <Button
                                  onClick={handleSubmit}
                                  className="bg-green-600 hover:bg-green-700"
                                  disabled={!signs || !knowledgerId}
                                >
                                  Closeout Permit
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Closeout Status (if exists) */}
                  {permitDetails.closeoutStatus && (
                    <div className="border-2 border-indigo-200 rounded-lg p-6 bg-gradient-to-br from-indigo-50 to-white">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="bg-indigo-100 p-2 rounded-full">
                          <User className="h-5 w-5 text-indigo-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-lg text-indigo-900">
                            Closeout
                          </h4>
                          <p className="text-sm text-indigo-700">
                            {permitDetails.closeoutStatus.by || 'N/A'}
                          </p>
                        </div>
                        <div className="ml-auto">
                          <Badge className="bg-indigo-500 text-white px-3 py-1">
                            {permitDetails.closeoutStatus.status}
                          </Badge>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="bg-white p-4 rounded-lg border">
                          <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Declaration</p>
                          <p className="text-sm text-gray-700 leading-relaxed italic">
                            "The task(s) have been completed. The work area(s) have been left in a tidy and safe condition."
                          </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-white p-4 rounded-lg border">
                            <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Signature</p>
                            <div className="flex items-center justify-center min-h-[60px]">
                              {permitDetails.closeoutStatus?.signature ? (
                                <ImageComponent fileName={permitDetails.closeoutStatus.signature} size="50" name={false} />
                              ) : (
                                <span className="text-gray-400 text-sm">No signature</span>
                              )}
                            </div>
                          </div>

                          <div className="space-y-3">
                            <div className="bg-white p-3 rounded-lg border">
                              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Signed Date</p>
                              <p className="text-sm text-gray-900">
                                {formatDate(permitDetails.closeoutStatus.signedDate || '')}
                              </p>
                            </div>
                            <div className="bg-white p-3 rounded-lg border">
                              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Comments</p>
                              <p className="text-sm text-gray-900">
                                {permitDetails.closeoutStatus.comments || 'N/A'}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Acknowledgement Status (if exists) */}
                  {permitDetails.acknowledgementStatus && (
                    <div className="border-2 border-pink-200 rounded-lg p-6 bg-gradient-to-br from-pink-50 to-white">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="bg-pink-100 p-2 rounded-full">
                          <User className="h-5 w-5 text-pink-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-lg text-pink-900">
                            Acknowledger
                          </h4>
                          <p className="text-sm text-pink-700">
                            {permitDetails.acknowledgementStatus.by || 'N/A'}
                          </p>
                        </div>
                        <div className="ml-auto">
                          <Badge className="bg-pink-500 text-white px-3 py-1">
                            {permitDetails.acknowledgementStatus.status}
                          </Badge>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="bg-white p-4 rounded-lg border">
                          <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Declaration</p>
                          <p className="text-sm text-gray-700 leading-relaxed italic">
                            "I acknowledge that, to the best of my knowledge, the work area(s) have been left in a tidy and safe condition by the applicant."
                          </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-white p-4 rounded-lg border">
                            <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Signature</p>
                            <div className="flex items-center justify-center min-h-[60px]">
                              {permitDetails.acknowledgementStatus?.signature ? (
                                <ImageComponent fileName={permitDetails.acknowledgementStatus.signature} size="50" name={false} />
                              ) : (
                                <span className="text-gray-400 text-sm">No signature</span>
                              )}
                            </div>
                          </div>

                          <div className="space-y-3">
                            <div className="bg-white p-3 rounded-lg border">
                              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Signed Date</p>
                              <p className="text-sm text-gray-900">
                                {formatDate(permitDetails.acknowledgementStatus.signedDate || '')}
                              </p>
                            </div>
                            <div className="bg-white p-3 rounded-lg border">
                              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Comments</p>
                              <p className="text-sm text-gray-900">
                                {permitDetails.acknowledgementStatus.comments || 'N/A'}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        ) : null}
      </DialogContent>
    </Dialog>

    {/* Signature Modal */}
    <Dialog open={signModal} onOpenChange={setSignModal}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Digital Signature</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="border rounded-lg p-4 bg-gray-50">
            <SignatureCanvas
              ref={signRef}
              penColor="#1F3BB3"
              backgroundColor="white"
              canvasProps={{
                width: 400,
                height: 150,
                className: "w-full border rounded",
              }}
            />
          </div>
          <p className="text-sm text-gray-600 text-center">
            Sign above using your mouse or touch screen
          </p>

          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => signRef.current?.clear()}
            >
              Clear
            </Button>
            <Button
              variant="outline"
              onClick={() => setSignModal(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (signRef.current) {
                  const canvas = signRef.current.getCanvas();
                  setSign(canvas.toDataURL("image/png"));
                  setSignModal(false);
                }
              }}
            >
              Confirm
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
    </>
  );
};

export default PermitDetailsModal;
