import { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import apiService from '@/services/apiService';
import { API_BASE_URL } from '@/constants/index';

// API endpoints
const ADMINDROPDOWNS = `${API_BASE_URL}/dropdowns`;

interface EditChecklistModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  accessToken: string | null;
  checklist: any;
}

interface ChecklistData {
  name: string;
  customId: string;
  category: string;
}

interface OptionType {
  label: string;
  value: string;
}

const EditChecklistModal = ({ isOpen, onClose, onSuccess, accessToken, checklist }: EditChecklistModalProps) => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showValidationErrors, setShowValidationErrors] = useState(false);
  const [categories, setCategories] = useState<OptionType[]>([]);

  const [checklistData, setChecklistData] = useState<ChecklistData>({
    name: '',
    customId: '',
    category: ''
  });

  // Fetch categories from dropdown API
  const fetchDropdownData = useCallback(async (maskId: string, setState: React.Dispatch<React.SetStateAction<OptionType[]>>) => {
    try {
      const uriString = {
        where: { maskId },
        include: [{ relation: "dropdownItems" }],
      };
      const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await apiService.get(url);
      const data = response[0]?.dropdownItems.map((item: any) => ({
        label: item.name,
        value: item.name,
      })) || [];
      setState(data);
    } catch (error) {
      console.error(`Error fetching ${maskId} list:`, error);
    }
  }, [accessToken]);

  // Initialize form data when checklist prop changes
  useEffect(() => {
    if (checklist && isOpen) {
      setChecklistData({
        name: checklist.name || '',
        customId: checklist.customId || '',
        category: checklist.category || ''
      });
      setShowValidationErrors(false);
    }
  }, [checklist, isOpen]);

  // Fetch categories when modal opens
  useEffect(() => {
    if (isOpen && accessToken) {
      fetchDropdownData('ins_category', setCategories);
    }
  }, [isOpen, accessToken, fetchDropdownData]);

  const handleClose = () => {
    setChecklistData({
      name: '',
      customId: '',
      category: ''
    });
    setShowValidationErrors(false);
    onClose();
  };

  const validateForm = () => {
    return checklistData.name.trim() !== '' &&
           checklistData.category.trim() !== '';
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      setShowValidationErrors(true);
      return;
    }

    if (!accessToken || !checklist?.id) {
      toast({
        title: "Error",
        description: "Missing authentication or checklist information",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await apiService.patch(`/checklists/${checklist.id}`, checklistData);

      onSuccess();
      handleClose();
      toast({
        title: "Success",
        description: "Checklist updated successfully",
      });
    } catch (error) {
      console.error('Error updating checklist:', error);
      toast({
        title: "Error",
        description: "Failed to update checklist. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Checklist</DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Checklist Name */}
          <div className="space-y-2">
            <Label className={showValidationErrors && !checklistData.name ? "text-red-500" : ""}>
              Checklist Name *
            </Label>
            <Input
              value={checklistData.name}
              onChange={(e) => setChecklistData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter checklist name"
              className={showValidationErrors && !checklistData.name ? "border-red-500" : ""}
            />
            {showValidationErrors && !checklistData.name && (
              <p className="text-red-500 text-sm">Checklist name is required</p>
            )}
          </div>

          {/* Custom ID */}
          <div className="space-y-2">
            <Label>Custom ID</Label>
            <Input
              value={checklistData.customId}
              onChange={(e) => setChecklistData(prev => ({ ...prev, customId: e.target.value }))}
              placeholder="Enter custom ID"
            />
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label className={showValidationErrors && !checklistData.category ? "text-red-500" : ""}>
              Category *
            </Label>
            <Select
              value={checklistData.category}
              onValueChange={(value) => setChecklistData(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger className={showValidationErrors && !checklistData.category ? "border-red-500" : ""}>
                <SelectValue placeholder="Select Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {showValidationErrors && !checklistData.category && (
              <p className="text-red-500 text-sm">Category is required</p>
            )}
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? 'Updating...' : 'Update Checklist'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditChecklistModal;
