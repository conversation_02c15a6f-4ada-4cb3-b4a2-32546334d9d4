import React from 'react';
import { format } from 'date-fns';
import { FileText, Calendar, User, Tag } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import ImageComponent from '@/components/common/ImageComponent';

interface DocumentComponent {
  id: string;
  type: string;
  content: any;
  position: number;
}

interface Document {
  id: string;
  name: string;
  type: string;
  size?: string;
  uploadedBy?: string;
  uploadedDate?: string;
  category?: string;
  tags?: string[];
  description?: string;
  maskId?: string;
  scopeApplicability?: string;
  purpose?: string;
  keywords?: string;
  docId?: string;
  created?: string;
  updated?: string;
  creatorTargetDate?: string;
  reviewerTargetDate?: string;
  approverTargetDate?: string;
  initiatorId?: string;
  creatorId?: string;
  reviewerId?: string;
  approverId?: string;
  documentCategoryId?: string;
  initiator?: any;
  creator?: any;
  reviewer?: any;
  approver?: any;
  documentCategory?: any;
  files?: any;
  status?: string;
  docStatus?: string;
  value?: DocumentComponent[];
}

interface DocumentPDFViewerProps {
  document: Document;
}

const DocumentPDFViewer: React.FC<DocumentPDFViewerProps> = ({ document }) => {
  // Get status color
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'under review':
      case 'review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'pending':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const renderDocumentComponent = (component: DocumentComponent) => {
    const { content } = component;

    switch (component.type) {
      case 'document-header':
        return (
          <div className="mb-8 text-center border-b border-gray-200 pb-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {content?.text || 'Document Title'}
            </h1>
            <div className="text-sm text-gray-500">
              Document ID: {document.maskId || document.docId || 'N/A'}
            </div>
          </div>
        );

      case 'section-header':
        return (
          <div className="mb-6 mt-8">
            <h2 className="text-2xl font-semibold text-gray-800 border-b border-gray-300 pb-2">
              {content?.text || 'Section Header'}
            </h2>
          </div>
        );

      case 'paragraph':
        return (
          <div className="mb-4">
            <p className="text-base text-gray-700 leading-relaxed text-justify">
              {content?.text || 'This is a paragraph of text content.'}
            </p>
          </div>
        );

      case 'bullet-list':
        return (
          <div className="mb-4">
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              {content?.items && Array.isArray(content.items) ? (
                content.items.map((item: string, idx: number) => (
                  <li key={idx} className="leading-relaxed">{item}</li>
                ))
              ) : (
                <li>List item</li>
              )}
            </ul>
          </div>
        );

      case 'numbered-list':
        return (
          <div className="mb-4">
            <ol className="list-decimal list-inside space-y-2 text-gray-700">
              {content?.items && Array.isArray(content.items) ? (
                content.items.map((item: string, idx: number) => (
                  <li key={idx} className="leading-relaxed">{item}</li>
                ))
              ) : (
                <li>List item</li>
              )}
            </ol>
          </div>
        );

      case 'quote':
        return (
          <div className="mb-6">
            <blockquote className="border-l-4 border-blue-500 pl-6 py-2 bg-blue-50 italic text-gray-700">
              {content?.text || 'This is a quote block.'}
            </blockquote>
          </div>
        );

      case 'separator':
        return (
          <div className="mb-6">
            <hr className="border-gray-300" />
          </div>
        );

      case 'image':
      case 'video':
      case 'file-attachment':
        return (
          <div className="mb-6">
            <div className="flex flex-col items-center p-4 bg-gray-50 rounded-lg border">
              <ImageComponent
                fileName={content?.filename}
                size="48"
                name={true}
              />
              {content?.caption && (
                <p className="text-sm text-gray-600 italic mt-2 text-center">
                  {content.caption}
                </p>
              )}
            </div>
          </div>
        );

      case 'link':
        return (
          <div className="mb-4">
            <a
              href={content?.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 underline"
            >
              {content?.text || content?.url || 'Link'}
            </a>
          </div>
        );

      default:
        return (
          <div className="mb-4 p-3 bg-gray-100 rounded border-l-4 border-gray-400">
            <div className="text-sm text-gray-600">
              {component.type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())} Component
            </div>
          </div>
        );
    }
  };

  return (
    <div className="h-full bg-gray-100 overflow-y-auto">
      {/* PDF-like Document Container */}
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white shadow-lg rounded-lg overflow-hidden min-h-[calc(100vh-100px)]">
          {/* Document Header */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 border-b border-gray-200">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  {document.name}
                </h1>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <FileText className="h-4 w-4" />
                    <span>{document.maskId || document.docId || 'N/A'}</span>
                  </div>
                  {document.created && (
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{format(new Date(document.created), 'MMM dd, yyyy')}</span>
                    </div>
                  )}
                  {document.creator?.firstName && (
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      <span>{document.creator.firstName}</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-2 mt-3">
                  <Badge className={`${getStatusColor(document.status || 'Draft')}`}>
                    {document.status || 'Draft'}
                  </Badge>
                  {document.category && (
                    <Badge variant="outline" className="text-xs">
                      <Tag className="h-3 w-3 mr-1" />
                      {document.category}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Document Content */}
          <div className="p-8">
            {/* Document Components */}
            {document.value && Array.isArray(document.value) && document.value.length > 0 ? (
              <div className="space-y-4">
                {document.value
                  .sort((a, b) => a.position - b.position)
                  .map((component, index) => (
                    <div key={component.id || index}>
                      {renderDocumentComponent(component)}
                    </div>
                  ))}
              </div>
            ) : (
              /* Show attached file for Existing documents */
              document.type === 'Existing' && document.files ? (
                <div className="text-center py-12">
                  <div className="p-6 bg-orange-50 rounded-lg border border-orange-200 inline-block">
                    <h3 className="font-semibold text-gray-900 mb-4">Attached Document</h3>
                    <ImageComponent
                      fileName={document.files}
                      size="64"
                      name={true}
                    />
                  </div>
                </div>
              ) : (
                /* Empty state */
                <div className="text-center py-16">
                  <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <FileText className="h-12 w-12 text-gray-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-700 mb-2">
                    No Content Available
                  </h3>
                  <p className="text-gray-500">
                    This document doesn't have any content components to display.
                  </p>
                </div>
              )
            )}

            {/* Document Footer */}
            <div className="mt-12 pt-6 border-t border-gray-200 text-center text-sm text-gray-500">
              <p>
                Document generated on {new Date().toLocaleDateString()} • 
                {document.category && ` Category: ${document.category} • `}
                Status: {document.status || 'Draft'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentPDFViewer;
