/**
 * Utility functions for file operations
 */

/**
 * Triggers a file download from a URL
 * @param url The URL of the file to download
 * @param filename Optional filename to use for the download
 */
export const downloadFile = (url: string, filename?: string) => {
  const link = document.createElement('a');
  link.href = url;
  if (filename) {
    link.download = filename;
  }
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
