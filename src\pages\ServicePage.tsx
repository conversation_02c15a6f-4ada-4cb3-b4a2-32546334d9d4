import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import PageHeader from "@/components/common/PageHeader";
import { Loader2 } from "lucide-react";
import { fetchServices, Service } from "@/services/api";
import { useToast } from "@/components/ui/use-toast";

const ServicePage = () => {
  const { serviceUrl } = useParams<{ serviceUrl: string }>();
  const [service, setService] = useState<Service | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { accessToken } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  useEffect(() => {
    const loadService = async () => {
      if (!accessToken) {
        setError("Authentication token not found");
        setIsLoading(false);
        return;
      }

      try {
        const services = await fetchServices(accessToken);
        // Find the service that matches the URL parameter
        const foundService = services.find(s => {
          // Remove leading slash if present in the URL
          const urlPath = s.url.startsWith('/') ? s.url.substring(1) : s.url;
          return urlPath === serviceUrl;
        });

        if (foundService) {
          setService(foundService);
        } else {
          setError(`Service not found: ${serviceUrl}`);
        }
        setIsLoading(false);
      } catch (err) {
        console.error("Error fetching service:", err);
        setError("Failed to load service. Please try again.");
        toast({
          title: "Error",
          description: "Failed to load service. Please try again.",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    loadService();
  }, [accessToken, serviceUrl, toast]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          <h2 className="mt-4 text-xl font-semibold">Loading service...</h2>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <div className="text-center max-w-md">
          <div className="bg-destructive/10 text-destructive p-4 rounded-md mb-4">
            <h2 className="font-semibold mb-2">Error Loading Service</h2>
            <p>{error}</p>
          </div>
          <button 
            className="btn btn-primary" 
            onClick={() => window.location.reload()}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <div className="text-center max-w-md">
          <div className="bg-warning/10 text-warning p-4 rounded-md mb-4">
            <h2 className="font-semibold mb-2">Service Not Found</h2>
            <p>The requested service could not be found.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <PageHeader 
        title={service.name} 
        description={service.description}
      />

      <div className="bg-card rounded-lg shadow-sm p-6 mt-6">
        <h2 className="text-xl font-semibold mb-4">Service Details</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div>
              <span className="font-medium">ID:</span> {service.id}
            </div>
            <div>
              <span className="font-medium">Mask Name:</span> {service.maskName}
            </div>
            <div>
              <span className="font-medium">Mobile Short Name:</span> {service.mobileShortName || 'N/A'}
            </div>
            <div>
              <span className="font-medium">Applicability:</span> {service.applicability}
            </div>
          </div>
          <div className="space-y-2">
            <div>
              <span className="font-medium">Status:</span> {service.status ? 'Active' : 'Inactive'}
            </div>
            <div>
              <span className="font-medium">Created:</span> {new Date(service.created).toLocaleString()}
            </div>
            <div>
              <span className="font-medium">Updated:</span> {new Date(service.updated).toLocaleString()}
            </div>
            <div>
              <span className="font-medium">URL:</span> {service.url}
            </div>
          </div>
        </div>
      </div>

      <div className="bg-card rounded-lg shadow-sm p-6 mt-6">
        <h2 className="text-xl font-semibold mb-4">Service Content</h2>
        <p className="text-muted-foreground">
          This is a placeholder for the {service.name} service content. 
          The actual implementation will depend on the specific requirements for this service.
        </p>
      </div>
    </>
  );
};

export default ServicePage;
