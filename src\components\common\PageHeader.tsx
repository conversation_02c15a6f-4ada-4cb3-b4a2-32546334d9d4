
import React from 'react';

interface PageHeaderProps {
  title: string;
  description?: string;
}

const PageHeader: React.FC<PageHeaderProps> = ({ title, description }) => {
  return (
    <div className="mb-6 pb-4 border-b border-border/50">
      <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-[#0A5A8F] to-[#D12027] bg-clip-text text-transparent animate-slide-up">{title}</h1>
      {description && (
        <p className="mt-2 text-muted-foreground animate-slide-in-right delay-150">{description}</p>
      )}
    </div>
  );
};

export default PageHeader;
