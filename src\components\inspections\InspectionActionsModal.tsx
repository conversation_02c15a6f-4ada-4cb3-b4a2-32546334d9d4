import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useToast } from '@/components/ui/use-toast';
import { CalendarIcon, Upload, X, CheckCircle, XCircle } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import apiService from '@/services/apiService';
import { API_BASE_URL } from '@/constants/index';
import ImageComponent from '@/components/common/ImageComponent';

// API endpoints
const FILE_URL = `${API_BASE_URL}/files`;
const GET_USER_ROLE_BY_MODE = `${API_BASE_URL}/users/get_users`;
const SUBMIT_INSPECTION_ACTION = (id: string) => `${API_BASE_URL}/inspection-task-submit/${id}`;

interface InspectionActionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  inspectionDetails: any;
  actionData: any;
}

interface AssigneeOption {
  label: string;
  value: string;
}

const InspectionActionsModal: React.FC<InspectionActionsModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  inspectionDetails,
  actionData
}) => {
  const { toast } = useToast();

  // State management (similar to test.js)
  const [actionType, setActionType] = useState<string>('');
  const [apiStatus, setApiStatus] = useState<string>(''); // For verify_task: 'Approve' or 'Return'
  const [comments, setComments] = useState<string>('');
  const [actionTaken, setActionTaken] = useState<string>('');
  const [actionToBeTaken, setActionToBeTaken] = useState<string>('');
  const [evidence, setEvidence] = useState<string[]>([]);
  const [dueDate, setDueDate] = useState<Date | null>(null);
  const [assessors, setAssessors] = useState<AssigneeOption[]>([]);
  const [assessorId, setAssessorId] = useState<string>('');
  const [showErrors, setShowErrors] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize action type and data
  useEffect(() => {
    if (actionData) {
      setActionType(actionData.actionType || '');

      // Pre-fill data based on action type
      if (actionData.actionType === 'reperform_task' || actionData.actionType === 'perform_task') {
        setActionToBeTaken(actionData.actionToBeTaken || actionData.description || '');
      }
      if (actionData.actionType === 'review') {
        // For review actions, we might want to pre-fill if editing
        setActionToBeTaken(actionData.actionToBeTaken || '');
      }
    }
  }, [actionData]);

  // Fetch users based on action type (similar to test.js)
  const fetchUsers = useCallback(async () => {
    if (!actionType) return;

    try {
      let mode = '';
      if (actionType === 'review') {
        mode = 'obsactionowner';
      } else if (actionType === 'perform_task' || actionType === 'reperform_task') {
        mode = 'ins_action_reviewer';
      }

      if (mode) {
        const response = await apiService.post(GET_USER_ROLE_BY_MODE, {
          locationOneId: inspectionDetails?.locationOneId || '',
          locationTwoId: inspectionDetails?.locationTwoId || '',
          locationThreeId: inspectionDetails?.locationThreeId || '',
          locationFourId: inspectionDetails?.locationFourId || '',
          mode: mode,
        });

        const data = response.map((item: any) => ({
          label: item.firstName,
          value: item.id
        }));
        setAssessors(data);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive"
      });
    }
  }, [actionType, inspectionDetails, toast]);

  useEffect(() => {
    if (isOpen && actionType) {
      fetchUsers();
    }
  }, [isOpen, actionType, fetchUsers]);

  // File upload handler
  const handleEvidenceUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await apiService.post(FILE_URL, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      if (response?.files?.[0]?.originalname) {
        const uploadedFile = response.files[0].originalname;
        setEvidence(prev => [...prev, uploadedFile]);

        toast({
          title: "Success",
          description: "File uploaded successfully",
        });
      }
    } catch (error) {
      console.error('File upload error:', error);
      toast({
        title: "Error",
        description: "Failed to upload file",
        variant: "destructive"
      });
    }
  };

  // Remove evidence file
  const handleRemoveEvidence = (index: number) => {
    setEvidence(prev => prev.filter((_, i) => i !== index));
  };

  // Validation logic (similar to test.js)
  const validateForm = () => {
    let hasError = false;

    if (actionType === 'perform_task' || actionType === 'reperform_task') {
      if (!actionTaken) hasError = true;
      if (!assessorId) hasError = true;
      if (evidence.length === 0) hasError = true;
    } else if (actionType === 'review') {
      if (!actionToBeTaken) hasError = true;
      if (!dueDate) hasError = true;
      if (!assessorId) hasError = true;
    } else if (actionType === 'verify_task') {
      if (!apiStatus) hasError = true;
      if (apiStatus === 'Return' && !comments) hasError = true;
    }

    return !hasError;
  };

  // Submit handler (similar to test.js)
  const handleSubmit = async () => {
    setShowErrors(true);

    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fill all required fields before submitting!",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      let formData: any = {};

      if (actionType === 'perform_task' || actionType === 'reperform_task') {
        formData = {
          actionTaken: actionTaken,
          reviewerId: assessorId,
          evidence: evidence
        };
      } else if (actionType === 'review') {
        formData = {
          actionToBeTaken: actionToBeTaken,
          dueDate: dueDate?.toISOString(),
          actionOwnerId: assessorId,
        };
      } else if (actionType === 'verify_task') {
        formData = {
          status: apiStatus === 'Approve' ? 'Completed' : 'Returned',
          comments: comments
        };
      }

      await apiService.patch(SUBMIT_INSPECTION_ACTION(actionData.id), formData);

      toast({
        title: "Success",
        description: "Action submitted successfully!",
      });
      onSuccess();
      onClose();
    } catch (error) {
      console.error("Error submitting action:", error);
      toast({
        title: "Error",
        description: "Failed to submit action. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get action type display name
  const getActionTypeDisplay = () => {
    switch (actionType) {
      case 'perform_task':
        return 'Take Action';
      case 'verify_task':
        return 'Verify Action';
      case 'reperform_task':
        return 'Retake Action';
      case 'review':
        return 'Review Action';
      default:
        return 'Action';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div>
              <h4 className="text-lg font-semibold">{getActionTypeDisplay()}</h4>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-sm text-muted-foreground">
                  #{inspectionDetails?.maskId || actionData?.maskId || 'N/A'}
                </span>
                <Badge variant="secondary">
                  {inspectionDetails?.status || 'Unknown'}
                </Badge>
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Inspection Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Inspection Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Inspector</p>
                  <p className="text-sm text-gray-900">{inspectionDetails?.inspector?.firstName || "N/A"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Checklist</p>
                  <p className="text-sm text-gray-900">{inspectionDetails?.checklist?.name || "N/A"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Scheduled Date</p>
                  <p className="text-sm text-gray-900">
                    {inspectionDetails?.scheduledDate
                      ? format(new Date(inspectionDetails.scheduledDate), 'dd-MM-yyyy')
                      : "N/A"}
                  </p>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Location</p>
                <p className="text-sm text-gray-900">
                  {[
                    inspectionDetails?.locationOne,
                    inspectionDetails?.locationTwo,
                    inspectionDetails?.locationThree,
                    inspectionDetails?.locationFour,
                    inspectionDetails?.locationFive,
                    inspectionDetails?.locationSix
                  ]
                    .filter(location => location?.name)
                    .map(location => location.name)
                    .join(' > ') || "N/A"}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Action-specific content */}
          {(actionType === 'perform_task' || actionType === 'reperform_task') && (
            <>
              {/* Show action details for both perform_task and reperform_task */}
              {actionData && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">
                      {inspectionDetails?.checklist?.name && actionData.description && "Action Details"}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Show inspection description if available */}
                    {inspectionDetails?.checklist?.name && actionData.description && (
                      <div>
                        <p className="text-lg font-semibold text-gray-900 mb-2">{actionData.description}</p>
                      </div>
                    )}

                    {/* Action to be Taken - Always show for both perform_task and reperform_task */}
                    <div>
                      <p className="text-base font-semibold text-gray-900 mb-2">Action to be Taken</p>
                      <p className="text-sm text-gray-700">{actionData.actionToBeTaken || actionData.description}</p>
                    </div>

                    {/* Show original images/uploads if available */}
                    {actionData.uploads && actionData.uploads.length > 0 && (
                      <div>
                        <p className="text-base font-semibold text-gray-900 mb-2">Images</p>
                        <div className="grid grid-cols-3 gap-2">
                          {actionData.uploads.map((file: string, i: number) => (
                            <div key={i} className="relative">
                              <ImageComponent fileName={file} size="100" name={false} />
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* For reperform_task, show previous action taken and evidence */}
                    {actionType === 'reperform_task' && (
                      <>
                        {actionData.actionTaken && (
                          <div>
                            <p className="text-base font-semibold text-gray-900 mb-2">Action Taken</p>
                            <p className="text-sm text-gray-700">{actionData.actionTaken}</p>
                          </div>
                        )}

                        {actionData.evidence && actionData.evidence.length > 0 && (
                          <div>
                            <p className="text-base font-semibold text-gray-900 mb-2">Evidence</p>
                            <div className="grid grid-cols-3 gap-2">
                              {actionData.evidence.map((file: string, i: number) => (
                                <div key={i} className="relative">
                                  <ImageComponent fileName={file} size="100" name={false} />
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {actionData.comments && (
                          <div>
                            <p className="text-base font-semibold text-gray-900 mb-2">Reviewer Comments</p>
                            <p className="text-sm text-gray-700">{actionData.comments}</p>
                          </div>
                        )}
                      </>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Action Taken Input */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Action Taken</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Label>Describe the action you have taken <span className="text-red-500">*</span></Label>
                    <Textarea
                      value={actionTaken}
                      onChange={(e) => setActionTaken(e.target.value)}
                      placeholder="Enter your action taken here..."
                      rows={4}
                      className={showErrors && !actionTaken ? 'border-red-500' : ''}
                    />
                    {showErrors && !actionTaken && (
                      <p className="text-sm text-red-600">Action taken is required.</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Evidence Upload */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Evidence</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                    <input
                      type="file"
                      accept="image/*,video/*,.pdf,.doc,.docx"
                      onChange={(e) => handleEvidenceUpload(e.target.files)}
                      className="hidden"
                      id="evidence-upload"
                    />
                    <label
                      htmlFor="evidence-upload"
                      className="cursor-pointer flex flex-col items-center gap-2"
                    >
                      <Upload className="h-8 w-8 text-gray-400" />
                      <span className="text-sm text-gray-600">
                        Attach supporting images / documents
                      </span>
                    </label>
                  </div>

                  {showErrors && evidence.length === 0 && (
                    <p className="text-sm text-red-600">At least one evidence file is required.</p>
                  )}

                  {/* Display uploaded evidence */}
                  {evidence.length > 0 && (
                    <div>
                      <Label className="mb-2 block">Uploaded Evidence</Label>
                      <div className="grid grid-cols-3 gap-2">
                        {evidence.map((file, idx) => (
                          <div key={idx} className="relative">
                            <ImageComponent fileName={file} size="100" name={true} />
                            <Button
                              variant="destructive"
                              size="sm"
                              className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                              onClick={() => handleRemoveEvidence(idx)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Reviewer Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">
                    {actionType === 'perform_task' ? 'Reviewer' : 'Reviewer'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Label>Select Reviewer <span className="text-red-500">*</span></Label>
                    <Select value={assessorId} onValueChange={setAssessorId}>
                      <SelectTrigger className={showErrors && !assessorId ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Select Reviewer" />
                      </SelectTrigger>
                      <SelectContent>
                        {assessors.map((assessor) => (
                          <SelectItem key={assessor.value} value={assessor.value}>
                            {assessor.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {showErrors && !assessorId && (
                      <p className="text-sm text-red-600">Reviewer is required.</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </>
          )}

          {/* Review Action UI */}
          {actionType === 'review' && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Action to be Taken</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Label>Describe the action to be taken <span className="text-red-500">*</span></Label>
                    <Textarea
                      value={actionToBeTaken}
                      onChange={(e) => setActionToBeTaken(e.target.value)}
                      placeholder="Enter the action to be taken here..."
                      rows={4}
                      className={showErrors && !actionToBeTaken ? 'border-red-500' : ''}
                    />
                    {showErrors && !actionToBeTaken && (
                      <p className="text-sm text-red-600">Action to be taken is required.</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Due Date</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Label>Select due date <span className="text-red-500">*</span></Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !dueDate && "text-muted-foreground",
                            showErrors && !dueDate && "border-red-500"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {dueDate ? format(dueDate, "PPP") : "Select a date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={dueDate || undefined}
                          onSelect={(date) => setDueDate(date || null)}
                          disabled={(date) => date < new Date()}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    {showErrors && !dueDate && (
                      <p className="text-sm text-red-600">Due date is required.</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Action Owner</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Label>Select Action Owner <span className="text-red-500">*</span></Label>
                    <Select value={assessorId} onValueChange={setAssessorId}>
                      <SelectTrigger className={showErrors && !assessorId ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Select Action Owner" />
                      </SelectTrigger>
                      <SelectContent>
                        {assessors.map((assessor) => (
                          <SelectItem key={assessor.value} value={assessor.value}>
                            {assessor.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {showErrors && !assessorId && (
                      <p className="text-sm text-red-600">Action owner is required.</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </>
          )}

          {/* Verify Task UI */}
          {actionType === 'verify_task' && (
            <>
              {/* Show action details */}
              {actionData && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Action Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Show inspection description if available */}
                    {inspectionDetails?.checklist?.name && actionData.description && (
                      <div>
                        <p className="text-lg font-semibold text-gray-900 mb-2">{actionData.description}</p>
                      </div>
                    )}

                    {/* Action to be Taken */}
                    <div>
                      <p className="text-base font-semibold text-gray-900 mb-2">Action to be Taken</p>
                      <p className="text-sm text-gray-700">{actionData.actionToBeTaken || actionData.description}</p>
                    </div>

                    {/* Show original images/uploads if available */}
                    {actionData.uploads && actionData.uploads.length > 0 && (
                      <div>
                        <p className="text-base font-semibold text-gray-900 mb-2">Images</p>
                        <div className="grid grid-cols-3 gap-2">
                          {actionData.uploads.map((file: string, i: number) => (
                            <div key={i} className="relative">
                              <ImageComponent fileName={file} size="100" name={false} />
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Action Taken by user with date */}
                    {actionData.actionTaken && (
                      <div>
                        <p className="text-base font-semibold text-gray-900 mb-2">
                          Action Taken by {(actionData.submittedByDetails?.firstName || actionData.submittedBy?.firstName || actionData.submittedBy || 'Unknown')} - {
                            actionData.created ? format(new Date(actionData.created), 'dd-MM-yyyy') : 'N/A'
                          }
                        </p>
                        <p className="text-sm text-gray-700">{actionData.actionTaken}</p>
                      </div>
                    )}

                    {/* Evidence submitted by action owner */}
                    {actionData.evidence && actionData.evidence.length > 0 && (
                      <div>
                        <p className="text-base font-semibold text-gray-900 mb-2">Evidence</p>
                        <div className="grid grid-cols-3 gap-2">
                          {actionData.evidence.map((file: string, i: number) => (
                            <div key={i} className="relative">
                              <ImageComponent fileName={file} size="100" name={false} />
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Approval/Return Decision */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Verification Decision</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-4 justify-center">
                    <Button
                      variant={apiStatus === 'Approve' ? "default" : "outline"}
                      onClick={() => setApiStatus('Approve')}
                      className={`flex items-center gap-2 px-6 py-3 ${
                        apiStatus === 'Approve'
                          ? 'bg-green-600 hover:bg-green-700 text-white'
                          : 'hover:bg-green-50 hover:border-green-300 border-2'
                      }`}
                    >
                      <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                        apiStatus === 'Approve' ? 'border-white bg-white' : 'border-gray-300'
                      }`}>
                        {apiStatus === 'Approve' && <CheckCircle className="w-4 h-4 text-green-600" />}
                      </div>
                      Approve
                    </Button>

                    <Button
                      variant={apiStatus === 'Return' ? "default" : "outline"}
                      onClick={() => setApiStatus('Return')}
                      className={`flex items-center gap-2 px-6 py-3 ${
                        apiStatus === 'Return'
                          ? 'bg-red-600 hover:bg-red-700 text-white'
                          : 'hover:bg-red-50 hover:border-red-300 border-2'
                      }`}
                    >
                      <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                        apiStatus === 'Return' ? 'border-white bg-white' : 'border-gray-300'
                      }`}>
                        {apiStatus === 'Return' && <XCircle className="w-4 h-4 text-red-600" />}
                      </div>
                      Return
                    </Button>
                  </div>

                  {showErrors && !apiStatus && (
                    <p className="text-sm text-red-600 text-center">Please select Approve or Return.</p>
                  )}
                </CardContent>
              </Card>

              {/* Comments Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Comments</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Label>
                      Comments {apiStatus === 'Return' && <span className="text-red-500">*</span>}
                    </Label>
                    <Textarea
                      value={comments}
                      onChange={(e) => setComments(e.target.value)}
                      placeholder="Enter your comments here..."
                      rows={4}
                      className={showErrors && apiStatus === 'Return' && !comments ? 'border-red-500' : ''}
                    />
                    {showErrors && apiStatus === 'Return' && !comments && (
                      <p className="text-sm text-red-600">Comments are required when returning.</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>

        {/* Footer Actions */}
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className={
              actionType === 'verify_task' && apiStatus === 'Return'
                ? 'bg-red-600 hover:bg-red-700'
                : actionType === 'verify_task' && apiStatus === 'Approve'
                ? 'bg-green-600 hover:bg-green-700'
                : ''
            }
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Submitting...
              </>
            ) : (
              actionType === 'verify_task'
                ? (apiStatus === 'Return' ? 'Return to Action Owner' : 'Approve')
                : 'Submit'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default InspectionActionsModal;
