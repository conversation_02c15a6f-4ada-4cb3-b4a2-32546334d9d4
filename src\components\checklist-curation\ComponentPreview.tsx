import React from "react";
import { DroppedItem, ContentComponent } from "@/types/draggable";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Image,
  Video,
  Youtube,
  Link,
  FileText,
  Mic,
  File,
  Code,
  Boxes,
  ListChecks,
  TextQuote,
  Star,
  Calendar,
  Clock,
  Timer,
  Phone,
  Hash,
  PenTool,
  CheckSquare,
  ChevronDown,
  CircleCheck,
  CheckCircle,
  ClipboardList,
  Upload,
  Paperclip,
  TextCursor,
  Type,
  Heading1,
  Heading2,
} from "lucide-react";

interface ComponentPreviewProps {
  item: DroppedItem;
}

const ComponentPreview: React.FC<ComponentPreviewProps> = ({ item }) => {
  const getComponentIcon = () => {
    const iconProps = { className: "h-4 w-4" };

    switch (item.type) {
      case "image":
        return <Image {...iconProps} />;
      case "video":
        return <Video {...iconProps} />;
      case "youtube":
        return <Youtube {...iconProps} />;
      case "weblink":
        return <Link {...iconProps} />;
      case "text":
        return <FileText {...iconProps} />;
      case "audio":
        return <Mic {...iconProps} />;
      case "attachment":
        return <File {...iconProps} />;
      case "embed":
        return <Code {...iconProps} />;
      case "scorm":
        return <Boxes {...iconProps} />;
      case "webgl":
        return <Boxes {...iconProps} />;
      case "mcq":
        return <ListChecks {...iconProps} />;
      case "textbox":
        return <TextQuote {...iconProps} />;
      case "option":
        return <CircleCheck {...iconProps} />;
      case "dropdown":
        return <ChevronDown {...iconProps} />;
      case "sign":
        return <PenTool {...iconProps} />;
      case "checkbox":
        return <CheckSquare {...iconProps} />;
      case "star":
        return <Star {...iconProps} />;
      case "date":
        return <Calendar {...iconProps} />;
      case "time":
        return <Clock {...iconProps} />;
      case "duration":
        return <Timer {...iconProps} />;
      case "phone":
        return <Phone {...iconProps} />;
      case "alphanumeric":
        return <Hash {...iconProps} />;
      case "checkpoint":
        return <CheckCircle {...iconProps} />;
      case "checkpoint-group":
        return <ClipboardList {...iconProps} />;
      default:
        return <File {...iconProps} />;
    }
  };

  const getComponentLabel = () => {
    switch (item.type) {
      case "image":
        return "Image";
      case "video":
        return "Video";
      case "youtube":
        return "YouTube";
      case "weblink":
        return "Web Link";
      case "text":
        return "Text";
      case "audio":
        return "Audio";
      case "attachment":
        return "Attachment";
      case "embed":
        return "Embed";
      case "scorm":
        return "SCORM";
      case "webgl":
        return "WebGL";
      case "mcq":
        return "Multiple Choice";
      case "textbox":
        return "Text Input";
      case "option":
        return "Option";
      case "dropdown":
        return "Dropdown";
      case "sign":
        return "Signature";
      case "checkbox":
        return "Checkbox";
      case "star":
        return "Star Rating";
      case "date":
        return "Date";
      case "time":
        return "Time";
      case "duration":
        return "Duration";
      case "phone":
        return "Phone Number";
      case "alphanumeric":
        return "Alphanumeric";
      case "checkpoint":
        return "Checkpoint";
      case "checkpoint-group":
        return "Checkpoint Group";
      default:
        return "Component";
    }
  };

  const renderPreview = () => {
    const component = item;
    const componentData = item.data as any; // Type cast to handle different component types

    // Handle Header Component
    if (component.type === 'header') {
      return (
        <div className="mb-6">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-600 rounded-full"></div>
            <h2 className="text-xl font-bold text-gray-800">{componentData.text || 'Header Text'}</h2>
          </div>
          <div className="h-px bg-gradient-to-r from-blue-500 via-blue-300 to-transparent"></div>
        </div>
      );
    }

    // Handle Section Header Component
    if (component.type === 'section-header') {
      return (
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-6 h-6 bg-indigo-500 rounded-md flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full"></div>
            </div>
            <h3 className="text-lg font-semibold text-indigo-700">{componentData.text || 'Section Header'}</h3>
            {componentData.required && <span className="text-red-500 text-sm">*</span>}
          </div>
          <div className="h-px bg-indigo-200 ml-8"></div>
        </div>
      );
    }

    // Handle Text Body Component
    if (component.type === 'text-body') {
      return (
        <Card className="mb-4 border-l-4 border-l-gray-400">
          <CardContent className="pt-4">
            <div className="prose prose-sm max-w-none">
              <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                {componentData.content || 'Text content will appear here...'}
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    // Handle Individual Checkpoint Component
    if (component.type === 'checkpoint') {
      return (
        <Card className="mb-4 border-l-4 border-l-green-400">
          <CardContent className="pt-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-3">
                  <h4 className="font-medium text-gray-800">{componentData.text || 'Checkpoint question'}</h4>
                  {componentData.required && <span className="text-red-500 text-sm">*</span>}
                </div>

                {/* Response Options */}
                <div className="flex gap-2 mb-3">
                  {['Yes', 'No', 'N/A'].map((opt) => (
                    <Button
                      key={opt}
                      variant="outline"
                      size="sm"
                      className={opt === 'Yes' ? 'hover:bg-green-600' : opt === 'No' ? 'hover:bg-red-600' : 'hover:bg-gray-600'}
                    >
                      {opt}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    // Handle Text Input Component
    if (component.type === 'text-input') {
      return (
        <Card className="mb-4 border-l-4 border-l-blue-400">
          <CardContent className="pt-4">
            <div className="flex items-start gap-3">
              <TextCursor className="w-5 h-5 text-blue-500 mt-1" />
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-medium text-gray-800">{componentData.label || 'Text Input'}</span>
                  {componentData.required && <span className="text-red-500 text-sm">*</span>}
                </div>
                <Input
                  placeholder={componentData.placeholder || "Enter text here..."}
                  disabled
                  className="bg-gray-50"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    // Handle Date Component
    if (component.type === 'date') {
      return (
        <Card className="mb-4 border-l-4 border-l-purple-400">
          <CardContent className="pt-4">
            <div className="flex items-start gap-3">
              <Calendar className="w-5 h-5 text-purple-500 mt-1" />
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-medium text-gray-800">{componentData.label || 'Date Selection'}</span>
                  {componentData.required && <span className="text-red-500 text-sm">*</span>}
                </div>
                <Input
                  type="date"
                  disabled
                  className="bg-gray-50"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    // Handle Sign Component
    if (component.type === 'sign') {
      return (
        <Card className="mb-4 border-l-4 border-l-orange-400">
          <CardContent className="pt-4">
            <div className="flex items-start gap-3">
              <PenTool className="w-5 h-5 text-orange-500 mt-1" />
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-medium text-gray-800">{componentData.label || 'Signature'}</span>
                  {componentData.required && <span className="text-red-500 text-sm">*</span>}
                </div>
                <div className="bg-orange-50 border border-orange-200 rounded-md p-4 text-center">
                  <PenTool className="h-8 w-8 mx-auto mb-2 text-orange-400" />
                  <span className="text-sm text-orange-600">Click to sign</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    // Handle Attachment Input Component
    if (component.type === 'attachment-input') {
      return (
        <Card className="mb-4 border-l-4 border-l-green-400">
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Paperclip className="h-5 w-5 text-green-500" />
              <span>{componentData.label || 'File Upload'}</span>
              {componentData.required && <span className="text-red-500 text-sm">*</span>}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
              <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <span className="text-sm text-gray-600">Upload files</span>
            </div>
          </CardContent>
        </Card>
      );
    }

    // Handle Checkpoint Group Component
    if (component.type === 'checkpoint-group') {
      return (
        <Card className="mb-4 border-l-4 border-l-blue-500">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <ClipboardList className="w-6 h-6 text-blue-500" />
                <CardTitle className="text-lg">{componentData.title || 'Checkpoint Group'}</CardTitle>
              </div>
              <Badge variant="secondary">Group</Badge>
            </div>
          </CardHeader>
          <CardContent>
            {componentData.checkpoints && componentData.checkpoints.length > 0 ? (
              <div className="space-y-4">
                {componentData.checkpoints.map((checkpoint: any, qIndex: number) => (
                  <div key={`q-${qIndex}`} className="border-l-4 border-blue-200 pl-4 py-2 bg-blue-50/30 rounded-r-md">
                    <h4 className="font-medium text-sm mb-2">{checkpoint.text || `Question ${qIndex + 1}`}</h4>
                    <div className="flex gap-2">
                      {['Yes', 'No', 'N/A'].map((opt) => (
                        <Button
                          key={opt}
                          variant="outline"
                          size="sm"
                          className="text-xs"
                        >
                          {opt}
                        </Button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">
                <ClipboardList className="h-6 w-6 text-muted-foreground mx-auto mb-2" />
                <span className="text-xs text-muted-foreground">No questions added</span>
              </div>
            )}
          </CardContent>
        </Card>
      );
    }

    return (
      <div className="text-sm text-muted-foreground p-4 border rounded-md">
        {getComponentLabel()} preview - Component type: {component.type}
      </div>
    );
  };

  // Determine if this is a communicate or feedback component
  const isCommunicateComponent = !item.type.startsWith('feedback-') &&
    !['mcq', 'textbox', 'option', 'dropdown', 'sign', 'checkbox', 'star', 'date', 'time', 'duration', 'phone', 'alphanumeric'].includes(item.type);

  return (
    <div className="border rounded-md p-3 bg-white dark:bg-slate-800 shadow-sm">
      <div className="flex items-center gap-2 mb-2">
        <div className="text-primary p-1 bg-primary/10 rounded-md">
          {getComponentIcon()}
        </div>
        <div className="text-sm font-medium">{getComponentLabel()}</div>
      </div>
      <div className="mt-2">
        {renderPreview()}
      </div>
    </div>
  );
};

export default ComponentPreview;
