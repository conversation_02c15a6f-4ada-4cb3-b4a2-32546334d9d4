import React, { useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { MODULES, COUNTRIES, REGIONS, SITES, LEVELS, RoleAssignment } from '@/types/roleAssignment';
import { User } from '@/types/user';

interface AssignPermissionsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (assignment: RoleAssignment) => void;
  userId: string;
  users: User[];
  editingAssignment: RoleAssignment | null;
}

const AssignPermissionsModal: React.FC<AssignPermissionsModalProps> = ({
  open,
  onOpenChange,
  onSubmit,
  userId,
  users,
  editingAssignment
}) => {
  // Location state
  const [country, setCountry] = useState<string>('');
  const [region, setRegion] = useState<string>('');
  const [site, setSite] = useState<string>('');
  const [level, setLevel] = useState<string>('');

  // Available options based on selections
  const [availableRegions, setAvailableRegions] = useState<string[]>([]);
  const [availableSites, setAvailableSites] = useState<string[]>([]);
  const [availableLevels, setAvailableLevels] = useState<string[]>([]);

  // Selected roles for each module
  const [selectedRoles, setSelectedRoles] = useState<Record<string, string[]>>({});

  // Get user name
  const userName = users.find(user => user.id === userId)?.name || '';

  // Initialize form when editing an assignment
  useEffect(() => {
    if (editingAssignment) {
      // Set location
      setCountry(editingAssignment.location.country);
      setRegion(editingAssignment.location.region);
      setSite(editingAssignment.location.site);
      setLevel(editingAssignment.location.level);

      // Set roles
      const moduleRoles: Record<string, string[]> = {};
      moduleRoles[editingAssignment.module] = editingAssignment.roles;
      setSelectedRoles(moduleRoles);
    } else {
      // Reset form for new assignment
      setCountry('');
      setRegion('');
      setSite('');
      setLevel('');
      setSelectedRoles({});
    }
  }, [editingAssignment, open]);

  // Update available regions when country changes
  useEffect(() => {
    if (country) {
      const regions = REGIONS[country] || [];
      setAvailableRegions(regions);

      // Reset dependent fields
      if (!editingAssignment) {
        setRegion('');
        setSite('');
        setLevel('');
      }
    } else {
      setAvailableRegions([]);
    }
  }, [country, editingAssignment]);

  // Update available sites when region changes
  useEffect(() => {
    if (region) {
      const sites = SITES[region] || [];
      setAvailableSites(sites);

      // Reset dependent fields
      if (!editingAssignment) {
        setSite('');
        setLevel('');
      }
    } else {
      setAvailableSites([]);
    }
  }, [region, editingAssignment]);

  // Update available levels when site changes
  useEffect(() => {
    if (site) {
      const levels = LEVELS[site] || LEVELS['default'] || [];
      setAvailableLevels(levels);

      // Reset dependent fields
      if (!editingAssignment) {
        setLevel('');
      }
    } else {
      setAvailableLevels([]);
    }
  }, [site, editingAssignment]);

  // Handle role selection
  const handleRoleToggle = (moduleId: string, role: string) => {
    setSelectedRoles(prev => {
      const moduleRoles = prev[moduleId] || [];
      const updatedRoles = moduleRoles.includes(role)
        ? moduleRoles.filter(r => r !== role)
        : [...moduleRoles, role];

      return {
        ...prev,
        [moduleId]: updatedRoles
      };
    });
  };

  // Check if a role is selected
  const isRoleSelected = (moduleId: string, role: string): boolean => {
    return (selectedRoles[moduleId] || []).includes(role);
  };

  // Handle form submission
  const handleSubmit = () => {
    // Validate location selection
    if (!country || !region || !site || !level) {
      // You could show an error message here
      return;
    }

    // If editing, only update the specific module being edited
    if (editingAssignment) {
      // Get the roles for the module being edited
      const moduleRoles = selectedRoles[editingAssignment.module] || [];

      const updatedAssignment: RoleAssignment = {
        ...editingAssignment,
        location: {
          country,
          region,
          site,
          level
        },
        // Keep the same module, just update the roles
        roles: moduleRoles,
      };

      onSubmit(updatedAssignment);
    } else {
      // For new assignments, collect all selected roles across modules
      const allModulesWithRoles = Object.entries(selectedRoles)
        .filter(([_, roles]) => roles.length > 0);

      if (allModulesWithRoles.length === 0) {
        // You could show an error message here
        return;
      }

      // Create a new assignment for each module with roles
      allModulesWithRoles.forEach(([moduleId, roles]) => {
        const newAssignment: RoleAssignment = {
          id: uuidv4(),
          userId,
          userName,
          location: {
            country,
            region,
            site,
            level
          },
          module: moduleId,
          roles,
          assignedAt: new Date()
        };

        onSubmit(newAssignment);
      });
    }

    onOpenChange(false);
  };

  // Handle reset button
  const handleReset = () => {
    setSelectedRoles({});
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl bg-gradient-to-r from-[#0A5A8F] to-[#D12027] bg-clip-text text-transparent">
            {editingAssignment
              ? `Edit ${MODULES.find(m => m.id === editingAssignment.module)?.name || ''} Permissions for ${userName}`
              : `Assign Permissions to ${userName}`
            }
          </DialogTitle>
          <p className="text-sm text-muted-foreground mt-1">
            {editingAssignment
              ? "Update location and roles for this module"
              : "Select location and roles to assign to this user"
            }
          </p>
        </DialogHeader>

        <div className="grid grid-cols-4 gap-4 py-4">
          <div>
            <Label htmlFor="country">Choose Country</Label>
            <Select
              value={country}
              onValueChange={setCountry}
            >
              <SelectTrigger id="country">
                <SelectValue placeholder="Select Country" />
              </SelectTrigger>
              <SelectContent>
                {COUNTRIES.map(c => (
                  <SelectItem key={c} value={c}>{c}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="region">Choose Region</Label>
            <Select
              value={region}
              onValueChange={setRegion}
              disabled={!country || availableRegions.length === 0}
            >
              <SelectTrigger id="region">
                <SelectValue placeholder="Select Region" />
              </SelectTrigger>
              <SelectContent>
                {availableRegions.map(r => (
                  <SelectItem key={r} value={r}>{r}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="site">Choose Site</Label>
            <Select
              value={site}
              onValueChange={setSite}
              disabled={!region || availableSites.length === 0}
            >
              <SelectTrigger id="site">
                <SelectValue placeholder="Select Site" />
              </SelectTrigger>
              <SelectContent>
                {availableSites.map(s => (
                  <SelectItem key={s} value={s}>{s}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="level">Choose Level</Label>
            <Select
              value={level}
              onValueChange={setLevel}
              disabled={!site || availableLevels.length === 0}
            >
              <SelectTrigger id="level">
                <SelectValue placeholder="Select Level" />
              </SelectTrigger>
              <SelectContent>
                {availableLevels.map(l => (
                  <SelectItem key={l} value={l}>{l}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-6 py-4">
          {/* When editing, show only the module being edited */}
          {editingAssignment
            ? MODULES
                .filter(module => module.id === editingAssignment.module)
                .map(module => (
                  <div key={module.id} className="space-y-2">
                    <h3 className="font-medium">{module.name}</h3>
                    <div className="grid grid-cols-3 gap-4">
                      {module.roles.map(role => (
                        <div key={role} className="flex items-center space-x-2">
                          <Checkbox
                            id={`${module.id}-${role}`}
                            checked={isRoleSelected(module.id, role)}
                            onCheckedChange={() => handleRoleToggle(module.id, role)}
                          />
                          <Label
                            htmlFor={`${module.id}-${role}`}
                            className="text-sm cursor-pointer"
                          >
                            {role}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))
            : MODULES.map(module => (
                <div key={module.id} className="space-y-2">
                  <h3 className="font-medium">{module.name}</h3>
                  <div className="grid grid-cols-3 gap-4">
                    {module.roles.map(role => (
                      <div key={role} className="flex items-center space-x-2">
                        <Checkbox
                          id={`${module.id}-${role}`}
                          checked={isRoleSelected(module.id, role)}
                          onCheckedChange={() => handleRoleToggle(module.id, role)}
                        />
                        <Label
                          htmlFor={`${module.id}-${role}`}
                          className="text-sm cursor-pointer"
                        >
                          {role}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              ))
          }
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="hover:bg-gray-100"
          >
            Cancel
          </Button>
          <Button
            variant="outline"
            onClick={handleReset}
            className="bg-amber-50 text-amber-700 hover:bg-amber-100 border-amber-200 hover:shadow-sm"
          >
            Reset
          </Button>
          <Button
            onClick={handleSubmit}
            variant="gradient"
            className="shadow-sm"
          >
            Assign
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AssignPermissionsModal;
