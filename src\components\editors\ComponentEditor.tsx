import React, { useState, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ContentComponent, AttachmentConfig } from "@/types/draggable";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/hooks/use-toast";
import { v4 as uuidv4 } from "uuid";
import { PlusCircle, Trash2, CheckCircle, Paperclip } from "lucide-react";

interface ComponentEditorProps {
  component: ContentComponent;
  onSave: (data: ContentComponent) => void;
  onCancel: () => void;
}

const ComponentEditor: React.FC<ComponentEditorProps> = ({
  component,
  onSave,
  onCancel,
}) => {
  const [editedComponent, setEditedComponent] = useState<ContentComponent>({ ...component });
  const fileInputRef = useRef<HTMLInputElement>(null);

  // File size limits in bytes
  const FILE_SIZE_LIMITS = {
    image: 20 * 1024 * 1024, // 20MB
    video: 150 * 1024 * 1024, // 150MB
    audio: 50 * 1024 * 1024, // 50MB
    attachment: 50 * 1024 * 1024, // 50MB
    scorm: 100 * 1024 * 1024, // 100MB for SCORM packages
    webgl: 100 * 1024 * 1024, // 100MB for WebGL files
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setEditedComponent((prev) => ({ ...prev, [name]: value }));
  };

  const handleBooleanChange = (name: string, value: boolean) => {
    setEditedComponent((prev) => ({ ...prev, [name]: value }));
  };

  // Helper function to get default attachment config
  const getDefaultAttachmentConfig = (): AttachmentConfig => ({
    image: {
      enabled: false,
      galleryUploads: false,
    },
    video: {
      enabled: false,
      galleryUploads: false,
    },
    documents: {
      enabled: false,
    },
  });

  // Helper function to handle attachment config changes
  const handleAttachmentConfigChange = (
    type: 'image' | 'video' | 'documents',
    field: 'enabled' | 'galleryUploads',
    value: boolean
  ) => {
    setEditedComponent((prev) => {
      const currentConfig = prev.attachmentConfig || getDefaultAttachmentConfig();

      const updatedConfig = {
        ...currentConfig,
        [type]: {
          ...currentConfig[type],
          [field]: value,
        },
      };

      // If disabling a type, also disable gallery uploads for that type
      if (field === 'enabled' && !value) {
        if (type === 'image' || type === 'video') {
          updatedConfig[type].galleryUploads = false;
        }
      }

      return {
        ...prev,
        attachmentConfig: updatedConfig,
      };
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Fix: Safely extract the component type as a string without accessing .type directly
    // Extract base component type by removing "feedback-" prefix if present
    const typeValue = component ? (typeof component === 'object' && 'type' in component ? component.type : '') : '';
    let componentType: string = typeValue;

    // Remove "feedback-" prefix for feedback component types
    if (componentType.startsWith('feedback-')) {
      componentType = componentType.substring(9); // "feedback-".length
    }

    // Convert to a valid key for FILE_SIZE_LIMITS
    const sizeKey = componentType.replace('-', '') as keyof typeof FILE_SIZE_LIMITS;
    const sizeLimit = FILE_SIZE_LIMITS[sizeKey] || 0;

    if (sizeLimit && file.size > sizeLimit) {
      const sizeLimitMB = sizeLimit / (1024 * 1024);
      toast({
        title: "File too large",
        description: `Maximum file size is ${sizeLimitMB}MB`,
        variant: "destructive",
      });
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      return;
    }

    // Create a URL for the file
    const fileUrl = URL.createObjectURL(file);

    // Update the component data based on the file type
    setEditedComponent((prev) => ({
      ...prev,
      src: fileUrl,
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
    }));
  };

  // Options management for MCQ, Dropdown, Checkbox, and Option components
  const addOption = () => {
    const newOption = { id: uuidv4(), text: '' };
    setEditedComponent((prev) => ({
      ...prev,
      options: [...((prev as any).options || []), newOption],
    }));
  };

  const removeOption = (id: string) => {
    setEditedComponent((prev) => ({
      ...prev,
      options: ((prev as any).options || []).filter((opt: any) => opt.id !== id),
    }));
  };

  const updateOption = (id: string, text: string) => {
    setEditedComponent((prev) => ({
      ...prev,
      options: ((prev as any).options || []).map((opt: any) =>
        opt.id === id ? { ...opt, text } : opt
      ),
    }));
  };

  // Render attachment configuration section
  const renderAttachmentConfig = () => {
    const config = editedComponent.attachmentConfig || getDefaultAttachmentConfig();

    return (
      <div className="space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border">
        <div className="flex items-center gap-2 mb-3">
          <Paperclip className="h-4 w-4 text-gray-600" />
          <Label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
            Attachment Options
          </Label>
        </div>

        {/* Image Attachments */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Image Attachments</Label>
            <Switch
              checked={config.image.enabled}
              onCheckedChange={(checked) =>
                handleAttachmentConfigChange('image', 'enabled', checked)
              }
            />
          </div>

          {config.image.enabled && (
            <div className="ml-4 pl-4 border-l-2 border-blue-200">
              <div className="flex items-center justify-between">
                <Label className="text-sm text-gray-600">Gallery Uploads</Label>
                <Switch
                  checked={config.image.galleryUploads}
                  onCheckedChange={(checked) =>
                    handleAttachmentConfigChange('image', 'galleryUploads', checked)
                  }
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Allow users to upload images from their device gallery
              </p>
            </div>
          )}
        </div>

        {/* Video Attachments */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Video Attachments</Label>
            <Switch
              checked={config.video.enabled}
              onCheckedChange={(checked) =>
                handleAttachmentConfigChange('video', 'enabled', checked)
              }
            />
          </div>

          {config.video.enabled && (
            <div className="ml-4 pl-4 border-l-2 border-green-200">
              <div className="flex items-center justify-between">
                <Label className="text-sm text-gray-600">Gallery Uploads</Label>
                <Switch
                  checked={config.video.galleryUploads}
                  onCheckedChange={(checked) =>
                    handleAttachmentConfigChange('video', 'galleryUploads', checked)
                  }
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Allow users to upload videos from their device gallery
              </p>
            </div>
          )}
        </div>

        {/* Document Attachments */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Document Attachments</Label>
            <Switch
              checked={config.documents.enabled}
              onCheckedChange={(checked) =>
                handleAttachmentConfigChange('documents', 'enabled', checked)
              }
            />
          </div>

          {config.documents.enabled && (
            <div className="ml-4 pl-4 border-l-2 border-purple-200">
              <p className="text-xs text-gray-500">
                Documents are uploaded from device by default
              </p>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderEditor = () => {
    // Fix: Safely extract the component type for the switch statement
    const componentType = component ? (typeof component === 'object' && 'type' in component ? component.type : '') : '';

    switch (componentType) {
      case 'header':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="text">Header Text</Label>
              <Input
                id="text"
                name="text"
                value={(editedComponent as any).text || ''}
                onChange={handleChange}
                placeholder="Enter header text"
              />
            </div>
            <div>
              <Label htmlFor="level">Header Level</Label>
              <select
                id="level"
                name="level"
                value={(editedComponent as any).level || 1}
                onChange={(e) => {
                  setEditedComponent((prev) => ({
                    ...prev,
                    level: parseInt(e.target.value) as 1 | 2 | 3 | 4 | 5 | 6
                  }));
                }}
                className="w-full p-2 border rounded-md"
              >
                <option value={1}>H1 - Largest</option>
                <option value={2}>H2 - Large</option>
                <option value={3}>H3 - Medium</option>
                <option value={4}>H4 - Small</option>
                <option value={5}>H5 - Smaller</option>
                <option value={6}>H6 - Smallest</option>
              </select>
            </div>

          </div>
        );

      case 'section-header':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="text">Section Header Text</Label>
              <Input
                id="text"
                name="text"
                value={(editedComponent as any).text || ''}
                onChange={handleChange}
                placeholder="Enter section header text"
              />
            </div>
            <div>
              <Label htmlFor="level">Header Level</Label>
              <select
                id="level"
                name="level"
                value={(editedComponent as any).level || 2}
                onChange={(e) => {
                  setEditedComponent((prev) => ({
                    ...prev,
                    level: parseInt(e.target.value) as 1 | 2 | 3 | 4 | 5 | 6
                  }));
                }}
                className="w-full p-2 border rounded-md"
              >
                <option value={1}>H1 - Largest</option>
                <option value={2}>H2 - Large</option>
                <option value={3}>H3 - Medium</option>
                <option value={4}>H4 - Small</option>
                <option value={5}>H5 - Smaller</option>
                <option value={6}>H6 - Smallest</option>
              </select>
            </div>

          </div>
        );

      case 'text-body':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="content">Text Content</Label>
              <Textarea
                id="content"
                name="content"
                value={(editedComponent as any).content || ''}
                onChange={handleChange}
                className="min-h-[150px]"
                placeholder="Enter text content for instructions, descriptions, etc."
              />
              <p className="text-xs text-muted-foreground mt-1">
                Use this for instructional text, descriptions, and general content. Line breaks will be preserved.
              </p>
            </div>

          </div>
        );

      case 'text':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="content">Content</Label>
              <Textarea
                id="content"
                name="content"
                value={(editedComponent as any).content || ''}
                onChange={handleChange}
                className="min-h-[200px]"
                placeholder="Enter rich text content"
              />
              <p className="text-xs text-muted-foreground mt-1">
                For a full implementation, a rich text editor like React Quill would be integrated here.
              </p>
            </div>
          </div>
        );



      // MCQ (Multiple Choice Question)
      case 'mcq':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="question">Question</Label>
              <Textarea
                id="question"
                name="question"
                value={(editedComponent as any).question || ''}
                onChange={handleChange}
                className="min-h-[100px]"
                placeholder="Enter your question here"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Rich text formatting would be available here in a full implementation.
              </p>
            </div>

            <div>
              <div className="flex items-center justify-between">
                <Label>Answers</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addOption}
                  className="flex items-center"
                >
                  <PlusCircle className="w-4 h-4 mr-1" /> Add Option
                </Button>
              </div>

              <div className="space-y-2 mt-2">
                {((editedComponent as any).options || []).map((option: any) => (
                  <div key={option.id} className="flex items-center space-x-2">
                    <Input
                      value={option.text}
                      onChange={(e) => updateOption(option.id, e.target.value)}
                      placeholder="Option text"
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => removeOption(option.id)}
                    >
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </div>
                ))}

                {((editedComponent as any).options || []).length === 0 && (
                  <p className="text-sm text-muted-foreground italic">No options added yet</p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="allowMultiple"
                checked={(editedComponent as any).allowMultiple || false}
                onCheckedChange={(checked) =>
                  handleBooleanChange("allowMultiple", checked)
                }
              />
              <Label htmlFor="allowMultiple">Allow selecting multiple answers</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required || false}
                onCheckedChange={(checked) =>
                  handleBooleanChange("required", checked)
                }
              />
              <Label htmlFor="required">Required field</Label>
            </div>
          </div>
        );



      // Media feedback components
      case 'feedback-video':
      case 'feedback-audio':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Field Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Enter field label"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required || false}
                onCheckedChange={(checked) =>
                  handleBooleanChange("required", checked)
                }
              />
              <Label htmlFor="required">Required field</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="allowFromLibrary"
                checked={(editedComponent as any).allowFromLibrary || false}
                onCheckedChange={(checked) =>
                  handleBooleanChange("allowFromLibrary", checked)
                }
              />
              <Label htmlFor="allowFromLibrary">Allow from library</Label>
            </div>




          </div>
        );



      // Dropdown
      case 'dropdown':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Field Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Enter field label"
              />
            </div>

            <div>
              <div className="flex items-center justify-between">
                <Label>Dropdown Options</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addOption}
                  className="flex items-center"
                >
                  <PlusCircle className="w-4 h-4 mr-1" /> Add Option
                </Button>
              </div>

              <div className="space-y-2 mt-2">
                {((editedComponent as any).options || []).map((option: any) => (
                  <div key={option.id} className="flex items-center space-x-2">
                    <Input
                      value={option.text}
                      onChange={(e) => updateOption(option.id, e.target.value)}
                      placeholder="Option text"
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => removeOption(option.id)}
                    >
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </div>
                ))}

                {((editedComponent as any).options || []).length === 0 && (
                  <p className="text-sm text-muted-foreground italic">No options added yet</p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required || false}
                onCheckedChange={(checked) =>
                  handleBooleanChange("required", checked)
                }
              />
              <Label htmlFor="required">Required field</Label>
            </div>
          </div>
        );

      // Sign
      case 'sign':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Field Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Enter field label"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required || false}
                onCheckedChange={(checked) =>
                  handleBooleanChange("required", checked)
                }
              />
              <Label htmlFor="required">Required field</Label>
            </div>


          </div>
        );



      // Star
      case 'star':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Field Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Enter field label"
              />
            </div>

            <div>
              <Label htmlFor="maxStars">Maximum Stars</Label>
              <Input
                id="maxStars"
                name="maxStars"
                type="number"
                min="1"
                max="10"
                value={(editedComponent as any).maxStars || 5}
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  if (!isNaN(value) && value > 0 && value <= 10) {
                    setEditedComponent((prev) => ({ ...prev, maxStars: value }));
                  }
                }}
                placeholder="5"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Number of stars to display (1-10)
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required || false}
                onCheckedChange={(checked) =>
                  handleBooleanChange("required", checked)
                }
              />
              <Label htmlFor="required">Required field</Label>
            </div>


          </div>
        );

      // Date
      case 'date':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Field Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Enter field label"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required || false}
                onCheckedChange={(checked) =>
                  handleBooleanChange("required", checked)
                }
              />
              <Label htmlFor="required">Required field</Label>
            </div>


          </div>
        );

      // Text Input
      case 'text-input':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Field Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Enter field label"
              />
            </div>

            <div>
              <Label htmlFor="placeholder">Placeholder Text</Label>
              <Input
                id="placeholder"
                name="placeholder"
                value={(editedComponent as any).placeholder || ''}
                onChange={handleChange}
                placeholder="Enter placeholder text"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required || false}
                onCheckedChange={(checked) =>
                  handleBooleanChange("required", checked)
                }
              />
              <Label htmlFor="required">Required field</Label>
            </div>
          </div>
        );

      // Attachment Input
      case 'attachment-input':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Field Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Enter field label"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required || false}
                onCheckedChange={(checked) =>
                  handleBooleanChange("required", checked)
                }
              />
              <Label htmlFor="required">Required field</Label>
            </div>
          </div>
        );









      // Checkpoint
      case 'checkpoint':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="text">Checkpoint Text</Label>
              <Input
                id="text"
                name="text"
                value={(editedComponent as any).text || ''}
                onChange={handleChange}
                placeholder="Enter checkpoint text"
              />
            </div>

            <div>
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                name="description"
                value={(editedComponent as any).description || ''}
                onChange={handleChange}
                placeholder="Enter additional description or instructions"
                className="min-h-[80px]"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required || false}
                onCheckedChange={(checked) =>
                  handleBooleanChange("required", checked)
                }
              />
              <Label htmlFor="required">Required checkpoint</Label>
            </div>


          </div>
        );

      // Checkpoint Group
      case 'checkpoint-group':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">Group Title</Label>
              <Input
                id="title"
                name="title"
                value={(editedComponent as any).title || ''}
                onChange={handleChange}
                placeholder="Enter checkpoint group title"
              />
            </div>

            <div>
              <Label htmlFor="description">Group Description (Optional)</Label>
              <Textarea
                id="description"
                name="description"
                value={(editedComponent as any).description || ''}
                onChange={handleChange}
                placeholder="Enter group description or instructions"
                className="min-h-[80px]"
              />
            </div>

            <div>
              <div className="flex items-center justify-between">
                <Label>Questions</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newCheckpoint = { id: uuidv4(), text: '' };
                    setEditedComponent((prev) => ({
                      ...prev,
                      checkpoints: [...((prev as any).checkpoints || []), newCheckpoint],
                    }));
                  }}
                  className="flex items-center"
                >
                  <PlusCircle className="w-4 h-4 mr-1" /> Add Question
                </Button>
              </div>

              <div className="space-y-3 mt-2 max-h-60 overflow-y-auto">
                {((editedComponent as any).checkpoints || []).map((checkpoint: any, index: number) => (
                  <div key={checkpoint.id} className="p-3 border rounded-md space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm">Question {index + 1}</Label>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setEditedComponent((prev) => ({
                            ...prev,
                            checkpoints: ((prev as any).checkpoints || []).filter((opt: any) => opt.id !== checkpoint.id),
                          }));
                        }}
                        className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                    <Input
                      placeholder="Enter your question"
                      value={checkpoint.text}
                      onChange={(e) => {
                        setEditedComponent((prev) => ({
                          ...prev,
                          checkpoints: ((prev as any).checkpoints || []).map((opt: any) =>
                            opt.id === checkpoint.id ? { ...opt, text: e.target.value } : opt
                          ),
                        }));
                      }}
                    />
                  </div>
                ))}
              </div>

              {((editedComponent as any).checkpoints || []).length === 0 && (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No questions added yet. Click "Add Question" to get started.
                </p>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required || false}
                onCheckedChange={(checked) =>
                  handleBooleanChange("required", checked)
                }
              />
              <Label htmlFor="required">Required checkpoint group</Label>
            </div>


          </div>
        );

      default:
        return (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Editor for component type "{component.type}" would be implemented here.
            </p>
          </div>
        );
    }
  };

  // Clean up object URLs when component unmounts
  React.useEffect(() => {
    return () => {
      if ((editedComponent as any).src && (editedComponent as any).src.startsWith('blob:')) {
        URL.revokeObjectURL((editedComponent as any).src);
      }
    };
  }, []);

  return (
    <div className="space-y-6 py-4">
      {renderEditor()}

      {/* Attachment Configuration Section - Only for attachment-input components */}
      {editedComponent.type === 'attachment-input' && renderAttachmentConfig()}

      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={() => onSave(editedComponent)}>
          Save Changes
        </Button>
      </div>
    </div>
  );
};

export default ComponentEditor;
