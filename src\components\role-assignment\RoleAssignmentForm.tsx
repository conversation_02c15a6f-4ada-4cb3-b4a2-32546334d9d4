import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { v4 as uuidv4 } from 'uuid';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import LocationSelector from './LocationSelector';
import ModuleRoleSelector from './ModuleRoleSelector';
import UserSelector from './UserSelector';
import { RoleAssignment } from '@/types/roleAssignment';
import { User } from '@/types/user';

// Form schema
const formSchema = z.object({
  userId: z.string().min(1, "User is required"),
  country: z.string().min(1, "Country is required"),
  region: z.string().min(1, "Region is required"),
  site: z.string().min(1, "Site is required"),
  level: z.string().min(1, "Level is required"),
  module: z.string().min(1, "Module is required"),
  roles: z.array(z.string()).min(1, "At least one role must be selected"),
});

type FormValues = z.infer<typeof formSchema>;

interface RoleAssignmentFormProps {
  users: User[];
  onSubmit: (assignment: RoleAssignment) => void;
  editingAssignment: RoleAssignment | null;
  onCancel: () => void;
}

const RoleAssignmentForm: React.FC<RoleAssignmentFormProps> = ({
  users,
  onSubmit,
  editingAssignment,
  onCancel
}) => {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      userId: '',
      country: '',
      region: '',
      site: '',
      level: '',
      module: '',
      roles: [],
    }
  });

  // Populate form when editing an assignment
  useEffect(() => {
    if (editingAssignment) {
      form.reset({
        userId: editingAssignment.userId,
        country: editingAssignment.location.country,
        region: editingAssignment.location.region,
        site: editingAssignment.location.site,
        level: editingAssignment.location.level,
        module: editingAssignment.module,
        roles: editingAssignment.roles,
      });
    }
  }, [editingAssignment, form]);

  // Check if location is complete
  const country = form.watch('country');
  const region = form.watch('region');
  const site = form.watch('site');
  const level = form.watch('level');
  const isLocationComplete = Boolean(country && region && site && level);

  const handleSubmit = (values: FormValues) => {
    const user = users.find(u => u.id === values.userId);
    
    if (!user) {
      form.setError('userId', { message: 'Invalid user selected' });
      return;
    }

    const assignment: RoleAssignment = {
      id: editingAssignment?.id || uuidv4(),
      userId: values.userId,
      userName: user.name,
      location: {
        country: values.country,
        region: values.region,
        site: values.site,
        level: values.level,
      },
      module: values.module,
      roles: values.roles,
      assignedAt: editingAssignment?.assignedAt || new Date(),
    };

    onSubmit(assignment);
    form.reset();
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>{editingAssignment ? 'Edit Role Assignment' : 'Assign New Roles'}</CardTitle>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <CardContent className="space-y-6">
            <UserSelector form={form} users={users} />
            <LocationSelector form={form} />
            <ModuleRoleSelector form={form} isLocationComplete={isLocationComplete} />
          </CardContent>
          <CardFooter className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit">
              {editingAssignment ? 'Update Assignment' : 'Assign Roles'}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
};

export default RoleAssignmentForm;
