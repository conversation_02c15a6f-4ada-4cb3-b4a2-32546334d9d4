import { LoginConfig } from '@/store/slices/authSlice';

/**
 * Fetches a presigned URL for a logo from the API
 * @param logoKey The key of the logo to fetch
 * @returns The presigned URL for the logo
 */
export const fetchLogoPresignedUrl = async (logoKey: string): Promise<string> => {
  try {
    const response = await fetch(`https://client-api.acuizen.com/files/${logoKey}/presigned-url`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch logo URL: ${response.status}`);
    }
    
    const data = await response.json();
    return data.url;
  } catch (error) {
    console.error('Error fetching logo presigned URL:', error);
    throw error;
  }
};

/**
 * Gets the logo URL from the login config or cache
 * @param config The login config
 * @param cacheKey The key to use for caching the logo URL
 * @returns The logo URL and loading state
 */
export const getLogoUrl = async (
  config: LoginConfig | null,
  cacheKey: string = 'logoUrl'
): Promise<{ logoUrl: string; isLoading: boolean }> => {
  if (!config || !config.LOGO) {
    // Try to get from cache
    const cachedUrl = localStorage.getItem(cacheKey);
    return { 
      logoUrl: cachedUrl || '', 
      isLoading: false 
    };
  }

  try {
    const url = await fetchLogoPresignedUrl(config.LOGO);
    // Cache the URL
    localStorage.setItem(cacheKey, url);
    return { logoUrl: url, isLoading: false };
  } catch (error) {
    console.error('Failed to fetch logo URL:', error);
    
    // Try to use cached logo URL as fallback
    const cachedUrl = localStorage.getItem(cacheKey);
    return { 
      logoUrl: cachedUrl || '', 
      isLoading: false 
    };
  }
};
