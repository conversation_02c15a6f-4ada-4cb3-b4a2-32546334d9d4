import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Edit, Save, X, Trash2, Upload } from 'lucide-react';
import ImageComponent from '@/components/common/ImageComponent';
import apiService from '@/services/apiService';
import { useToast } from '@/components/ui/use-toast';

interface TaskItem {
  name?: string;
  images?: string[];
}

interface SubActivityComponentProps {
  item: TaskItem[];
  onSave: (name: string, images: string[]) => void;
}

const SubActivityComponent: React.FC<SubActivityComponentProps> = ({ item, onSave }) => {
    const { toast } = useToast();
    const [isEditing, setIsEditing] = useState<boolean>(false);
    const [activityName, setActivityName] = useState<string>(item[0]?.name || '');
    const [uploadedImages, setUploadedImages] = useState<string[]>(item[0]?.images || []);
    const [isDragOver, setIsDragOver] = useState(false);

    const toggleEditMode = () => {
        setIsEditing(!isEditing);
    };

    const saveChanges = () => {
        onSave(activityName, uploadedImages);
        setIsEditing(false);
        toast({
            title: "Success",
            description: "Sub-activity updated successfully",
        });
    };

    const cancelChanges = () => {
        setActivityName(item[0]?.name || '');
        setUploadedImages(item[0]?.images || []);
        setIsEditing(false);
    };

    const handleActivityImage = async (files: File[]) => {
        if (files.length > 0) {
            const uploadedFileNames = [];

            // Upload all files
            for (const file of files) {
                const formData = new FormData();
                formData.append('file', file);

                try {
                    const response = await apiService.post('/files', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        }
                    });

                    if (response?.files?.[0]?.originalname) {
                        uploadedFileNames.push(response.files[0].originalname);
                    }
                } catch (error) {
                    console.error("File upload error: ", error);
                    toast({
                        title: "Error",
                        description: `Failed to upload ${file.name}`,
                        variant: "destructive",
                    });
                }
            }

            // Update state with all uploaded files
            if (uploadedFileNames.length > 0) {
                setUploadedImages((prevImages) => [...prevImages, ...uploadedFileNames]);
                toast({
                    title: "Success",
                    description: `${uploadedFileNames.length} file(s) uploaded successfully`,
                });
            }
        }
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);

        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
            const files = Array.from(e.dataTransfer.files);
            // Filter for image files only
            const imageFiles = files.filter(file =>
                file.type.startsWith('image/') &&
                ['image/jpeg', 'image/png', 'image/jpg'].includes(file.type)
            );

            if (imageFiles.length > 0) {
                handleActivityImage(imageFiles);
            }
        }
    };

    return (
        <Card className="mb-6 border border-gray-200 shadow-sm">
            <CardContent className="p-0">
                {/* Header */}
                <div className="bg-white border-b border-gray-200 p-6">
                    <div className="flex items-center justify-between">
                        {!isEditing ? (
                            <>
                                <div className="flex items-center gap-3">
                                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <Edit className="w-5 h-5 text-blue-600" />
                                    </div>
                                    <div>
                                        <h4 className="text-xl font-bold text-gray-800">{activityName}</h4>
                                        <p className="text-gray-600 text-sm">Sub-Activity Details</p>
                                    </div>
                                </div>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={toggleEditMode}
                                    className="h-10 w-10 p-0 text-gray-600 hover:text-blue-600 hover:bg-blue-50"
                                >
                                    <Edit className="h-5 w-5" />
                                </Button>
                            </>
                        ) : (
                            <div className="w-full">
                                <h4 className="text-xl font-bold text-gray-800 mb-2">Edit Sub-Activity</h4>
                                <p className="text-gray-600 text-sm">Update sub-activity information and images</p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Content */}
                <div className="p-6">
                    {isEditing && (
                        <div className="space-y-6">
                            <div>
                                <Label htmlFor="activity-name" className="text-sm font-medium text-gray-700 mb-2 block">
                                    Sub-Activity Name *
                                </Label>
                                <Input
                                    id="activity-name"
                                    value={activityName}
                                    onChange={(e) => setActivityName(e.target.value)}
                                    className="text-lg"
                                    placeholder="Enter sub-activity name..."
                                />
                            </div>

                            <div>
                                <Label htmlFor="image-uploads" className="text-sm font-medium text-gray-700 mb-2 block">
                                    Image Uploads (Optional)
                                </Label>
                                <div
                                    className={`border-2 border-dashed rounded-lg p-4 transition-colors cursor-pointer ${
                                        isDragOver
                                            ? 'border-blue-400 bg-blue-50'
                                            : 'border-gray-300 bg-gray-50 hover:border-gray-400'
                                    }`}
                                    onDragOver={handleDragOver}
                                    onDragLeave={handleDragLeave}
                                    onDrop={handleDrop}
                                    onClick={() => document.getElementById('sub-activity-file-upload')?.click()}
                                >
                                    <div className="flex flex-col items-center justify-center space-y-2">
                                        <Upload className={`h-8 w-8 ${isDragOver ? 'text-blue-500' : 'text-gray-400'}`} />
                                        <p className={`text-sm ${isDragOver ? 'text-blue-600' : 'text-gray-600'}`}>
                                            {isDragOver ? 'Drop files here' : 'Upload images to illustrate this sub-activity'}
                                        </p>
                                        <p className="text-xs text-gray-500">Drag and drop files here, or click to select files</p>
                                        <p className="text-xs text-gray-500">Max 5 files, up to 100MB each • JPEG, PNG only</p>
                                        <Input
                                            type="file"
                                            multiple
                                            accept="image/jpeg,image/png,image/jpg"
                                            onChange={(e) => {
                                                if (e.target.files && e.target.files.length > 0) {
                                                    const files = Array.from(e.target.files);
                                                    handleActivityImage(files);
                                                    e.target.value = ''; // Reset input
                                                }
                                            }}
                                            className="hidden"
                                            id="sub-activity-file-upload"
                                        />
                                        {!isDragOver && (
                                            <Button
                                                type="button"
                                                variant="outline"
                                                size="sm"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    document.getElementById('sub-activity-file-upload')?.click();
                                                }}
                                            >
                                                <Upload className="h-4 w-4 mr-2" />
                                                Select Files
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {uploadedImages.length > 0 && (
                                <div>
                                    <Label className="text-sm font-medium text-gray-700 mb-3 block">
                                        Uploaded Images ({uploadedImages.length})
                                    </Label>
                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                        {uploadedImages.map((image, index) => (
                                            <div key={index} className="relative group">
                                                <div className="border-2 border-gray-200 rounded-lg p-3 bg-white shadow-sm hover:shadow-md transition-shadow">
                                                    <ImageComponent fileName={image} size={'100'} name={true} />
                                                    <Button
                                                        variant="destructive"
                                                        size="sm"
                                                        className="absolute -top-2 -right-2 h-7 w-7 p-0 opacity-0 group-hover:opacity-100 transition-opacity shadow-lg"
                                                        onClick={() => setUploadedImages(uploadedImages.filter((_, i) => i !== index))}
                                                    >
                                                        <Trash2 className="h-3 w-3" />
                                                    </Button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            <div className="flex gap-3 pt-4 border-t border-gray-200">
                                <Button
                                    onClick={saveChanges}
                                    className="bg-green-600 hover:bg-green-700 text-white"
                                >
                                    <Save className="h-4 w-4 mr-2" />
                                    Save Changes
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={cancelChanges}
                                    className="border-gray-300 text-gray-700 hover:bg-gray-50"
                                >
                                    <X className="h-4 w-4 mr-2" />
                                    Cancel
                                </Button>
                            </div>
                        </div>
                    )}

                    {!isEditing && uploadedImages.length > 0 && (
                        <div className="mt-4">
                            <Label className="text-sm font-medium text-gray-700 mb-3 block">
                                Activity Images ({uploadedImages.length})
                            </Label>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                {uploadedImages.map((image, index) => (
                                    <div key={index} className="border-2 border-gray-200 rounded-lg p-3 bg-white shadow-sm">
                                        <ImageComponent fileName={image} size={'100'} name={true} />
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
};

export default SubActivityComponent;
