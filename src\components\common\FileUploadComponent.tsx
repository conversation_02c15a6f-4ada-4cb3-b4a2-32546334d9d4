import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import API from '@/services/axiosAPI';
import { API_BASE_URL } from '@/constants/index';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';

const FILE_URL = `${API_BASE_URL}/files`;

interface FileUploadComponentProps {
  onFileUpload?: (fileNames: string[]) => void;
  onFilesChange?: (files: File[]) => void;
  accept?: string;
  acceptedTypes?: string[];
  multiple?: boolean;
  maxFiles?: number;
  maxFileSize?: number;
  description?: string;
  fieldName?: string;
  initialFiles?: string[];
}

interface FilePreview {
  name: string;
  url: string;
  type: string;
  size: number;
}

const FileUploadComponent: React.FC<FileUploadComponentProps> = ({
  onFileUpload,
  onFilesChange,
  accept = '*',
  acceptedTypes = ['*'],
  multiple = true,
  maxFiles = 10,
  maxFileSize = 10485760, // 10MB default
  description = 'Upload files',
  fieldName,
  initialFiles = [],
}) => {
  const { toast } = useToast();
  const { accessToken } = useSelector((state: RootState) => state.auth);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<FilePreview[]>([]);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;

    const files = Array.from(e.target.files);

    // Check file size if maxFileSize is specified
    for (const file of files) {
      if (file.size > maxFileSize) {
        toast({
          title: "File too large",
          description: `${file.name} is too large. Maximum size is ${(maxFileSize / 1024 / 1024).toFixed(1)}MB.`,
          variant: "destructive"
        });
        return;
      }
    }

    // Check if adding these files would exceed the maximum
    if (selectedFiles.length + files.length > maxFiles) {
      toast({
        title: "Too many files",
        description: `You can upload a maximum of ${maxFiles} files.`,
        variant: "destructive"
      });
      return;
    }

    // If onFilesChange is provided, use it directly (for TestRiskAssessment)
    if (onFilesChange) {
      onFilesChange(files);
      // Clear the input value so the same file can be selected again
      e.target.value = '';
      return;
    }

    setIsUploading(true);

    // Process each file for upload (original behavior)
    for (const file of files) {
      try {
        // Create preview URL
        const previewUrl = URL.createObjectURL(file);

        // Upload file to server
        const formData = new FormData();
        formData.append('file', file);

        const response = await API.post(FILE_URL, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${accessToken}`
          },
        });

        if (response && response.status === 200) {
          const uploadedFileName = response.data.files[0].originalname;

          // Add to selected files
          setSelectedFiles(prev => [
            ...prev,
            {
              name: uploadedFileName,
              url: previewUrl,
              type: file.type,
              size: file.size
            }
          ]);

          // Notify parent component
          if (onFileUpload) {
            onFileUpload([...initialFiles, uploadedFileName]);
          }
        }
      } catch (error) {
        console.error('File upload error:', error);
        toast({
          title: "Upload Failed",
          description: `Failed to upload ${file.name}. Please try again.`,
          variant: "destructive"
        });
      }
    }

    setIsUploading(false);

    // Clear the input value so the same file can be selected again
    e.target.value = '';
  };

  const removeFile = (index: number) => {
    // Revoke the object URL to avoid memory leaks
    URL.revokeObjectURL(selectedFiles[index].url);

    // Remove the file from the array
    const newFiles = [...selectedFiles];
    newFiles.splice(index, 1);
    setSelectedFiles(newFiles);

    // Notify parent component
    const fileNames = newFiles.map(file => file.name);
    onFileUpload(fileNames);
  };

  return (
    <div className="space-y-4">
      <Input
        type="file"
        accept={acceptedTypes.length > 0 ? acceptedTypes.join(',') : accept}
        onChange={handleFileChange}
        className="flex-1"
        multiple={multiple}
        disabled={isUploading}
      />
      <p className="text-sm text-muted-foreground">
        {description}
      </p>

      {selectedFiles.length > 0 && (
        <div className="space-y-2">
          <p className="text-sm font-medium">Selected Files:</p>
          <div className="grid grid-cols-2 gap-4">
            {selectedFiles.map((file, index) => (
              <div key={index} className="border rounded-md p-2 relative">
                <div className="flex justify-between items-start mb-2">
                  <span className="text-sm font-medium truncate max-w-[80%]">
                    {file.name}
                  </span>
                  <span
                    role="button"
                    tabIndex={0}
                    className="text-red-500 hover:text-red-700 cursor-pointer"
                    onClick={() => removeFile(index)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        removeFile(index);
                      }
                    }}
                  >
                    ×
                  </span>
                </div>

                {file.type.startsWith('image/') ? (
                  <div className="h-24 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
                    <img
                      src={file.url}
                      alt={file.name}
                      className="max-h-full max-w-full object-contain"
                    />
                  </div>
                ) : (
                  <div className="h-24 bg-gray-100 rounded flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-3xl mb-1">📄</div>
                      <div className="text-xs text-gray-500">
                        {(file.size / 1024).toFixed(1)} KB
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUploadComponent;
