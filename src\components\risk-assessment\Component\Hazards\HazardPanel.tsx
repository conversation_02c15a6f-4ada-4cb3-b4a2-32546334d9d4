import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import HazardItem from '@/components/risk-assessment/Component/Hazards/HazardItem';

interface HazardItem {
  id: string;
  name: string;
  hazardName?: string;
}

interface HazardCategory {
  name: string;
  hazardItems?: HazardItem[];
  hazards?: HazardItem[];
}

interface HazardPanelProps {
  hazards: HazardCategory[];
  activeTabIndex: number;
  setActiveTabIndex: (index: number) => void;
  selectedHazards: HazardItem[];
  onClickHazards: (hazard: HazardItem) => void;
}

const HazardPanel: React.FC<HazardPanelProps> = ({ 
  hazards, 
  activeTabIndex, 
  setActiveTabIndex, 
  selectedHazards, 
  onClickHazards 
}) => {
  const hasSelectedIndustries = localStorage.getItem('SELECTED_INDUSTRIES');

  const getHazardItems = (haz: HazardCategory): HazardItem[] => {
    return hasSelectedIndustries ? (haz.hazardItems || []) : (haz.hazards || []);
  };

  return (
    <div className="w-full">
      <Tabs 
        value={hazards[activeTabIndex]?.name || hazards[0]?.name} 
        onValueChange={(value) => {
          const index = hazards.findIndex(h => h.name === value);
          if (index !== -1) setActiveTabIndex(index);
        }}
        orientation="vertical"
        className="flex h-[400px]"
      >
        <TabsList className="flex flex-col h-full w-48 justify-start">
          {hazards.map((haz, index) => (
            <TabsTrigger 
              key={index} 
              value={haz.name}
              className="w-full justify-start text-left whitespace-normal h-auto py-3 px-4"
            >
              {haz.name}
            </TabsTrigger>
          ))}
        </TabsList>
        
        <div className="flex-1 ml-4">
          {hazards.map((haz, index) => (
            <TabsContent key={index} value={haz.name} className="mt-0 h-full">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 max-h-[400px] overflow-y-auto">
                {getHazardItems(haz).map((hazardItem, j) => (
                  <HazardItem
                    key={j}
                    hazard={hazardItem}
                    selectedHazards={selectedHazards}
                    onClickHazards={onClickHazards}
                  />
                ))}
              </div>
            </TabsContent>
          ))}
        </div>
      </Tabs>
    </div>
  );
};

export default HazardPanel;
