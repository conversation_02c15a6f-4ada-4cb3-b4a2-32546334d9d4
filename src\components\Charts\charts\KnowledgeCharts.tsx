import { useState, useMemo } from "react";
import MetricCard from "@/components/Charts/MetricCard";
import MetricDetails from "@/components/Charts/MetricDetails";
import CascadingLocationFilter, { LocationFilterState } from "@/components/Charts/CascadingLocationFilter";

const KnowledgeCharts = () => {
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});
  const [locationFilters, setLocationFilters] = useState<LocationFilterState>({
    country: "all",
    region: "all",
    site: "all"
  });

  // Knowledge metrics data
  const overviewMetrics = [
    {
      id: "training-completion-rate",
      title: "% of Training Completion Rate",
      value: "88",
      unit: "courses (88%)",
      target: 100,
      targetPercentage: 12,
      trend: [84, 85, 86, 87, 88, 89, 88, 87, 88, 89, 90, 88],
      isImproving: true
    },
    {
      id: "knowledge-base-utilization",
      title: "% of Knowledge Base Utilization",
      value: "76",
      unit: "resources (76%)",
      target: 100,
      targetPercentage: 24,
      trend: [72, 73, 74, 75, 76, 77, 76, 75, 76, 77, 78, 76],
      isImproving: true
    },
    {
      id: "training-overdue",
      title: "% of Training Overdue",
      value: "6",
      unit: "training (6%)",
      target: 0,
      targetPercentage: 6,
      trend: [10, 9, 8, 7, 6, 5, 6, 7, 6, 5, 6, 6],
      isImproving: false
    },
    {
      id: "new-content-added",
      title: "Number of New Content Added",
      value: "24",
      unit: "documents",
      target: 30,
      targetPercentage: 20,
      trend: [18, 19, 20, 21, 22, 23, 24, 23, 22, 23, 24, 24],
      isImproving: true
    }
  ];

  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  const handleLocationFilterChange = (filterType: keyof LocationFilterState, value: string) => {
    setLocationFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  return (
    <div className="space-y-6">
      {/* Location Filters */}
      <CascadingLocationFilter
        filterState={locationFilters}
        onFilterChange={handleLocationFilterChange}
      />

      {/* Headline Metrics */}
      <div className="grid grid-cols-4 gap-4">
        {overviewMetrics.map((metric) => (
          <MetricCard
            key={metric.id}
            {...metric}
            showDetails={openDetailsId === metric.id}
            onToggleDetails={() => handleToggleDetails(metric.id)}
            selectedPeriod={getSelectedPeriod(metric.id)}
            onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
          />
        ))}
      </div>

      {/* Details Section - Rendered below the grid when a card is selected */}
      {openDetailsId && (() => {
        const selectedMetric = overviewMetrics.find(m => m.id === openDetailsId);
        return selectedMetric ? (
          <MetricDetails
            title={selectedMetric.title}
            value={selectedMetric.value}
            unit={selectedMetric.unit}
            targetPercentage={selectedMetric.targetPercentage}
            trend={selectedMetric.trend}
            isImproving={selectedMetric.isImproving}
            selectedPeriod={getSelectedPeriod(openDetailsId)}
          />
        ) : null;
      })()}
    </div>
  );
};

export default KnowledgeCharts;
