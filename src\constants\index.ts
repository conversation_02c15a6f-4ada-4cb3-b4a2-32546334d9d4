// API Base URL
export const API_BASE_URL = 'https://client-api.acuizen.com';

// File handling endpoints
export const FILE_DOWNLOAD = (file: string) => {
  return `${API_BASE_URL}/files/${file}/presigned-url`;
};

export const FILE_DOWNLOAD_WITH_S3 = (bucket: string, fileName: string) => {
  return `${API_BASE_URL}/files/${bucket}/${fileName}/presigned-url`;
};

export const GET_BLOB = `${API_BASE_URL}/files/blob`;

// Observation endpoints
export const OBSERVATION_REPORTS = `${API_BASE_URL}/observation-reports`;

// User endpoints
export const USER_DETAILS = `${API_BASE_URL}/users/me`;

// Services endpoints
export const SERVICES = `${API_BASE_URL}/services`;
export const ASSIGNED_ACTIONS = `${API_BASE_URL}/my-assigned-actions/All`;

// Login config endpoint
export const LOGIN_CONFIG = `${API_BASE_URL}/login-configs`;

// Risk Assessment endpoints
export const RISK_ASSESSMENTS = `${API_BASE_URL}/risk-assessments`;

// Change Management endpoints
export const CHANGE_REQUESTS = `${API_BASE_URL}/change-requests`;
export const IMPACT_ASSESSMENTS = `${API_BASE_URL}/impact-assessments`;
export const APPROVAL_WORKFLOWS = `${API_BASE_URL}/approval-workflows`;
export const TRAINING_ROLLOUTS = `${API_BASE_URL}/training-rollouts`;
export const TRAINING_ASSIGNMENTS = `${API_BASE_URL}/training-assignments`;

// User management endpoints
export const EXTERNAL_USERS_URL = `${API_BASE_URL}/users/external`;
export const USERS_URL_WITH_ID = (id: string) => `${API_BASE_URL}/users/${id}`;

// Role assignment endpoints
export const GET_INDIVIDUAL_USER_LOCATION_ROLE_URL = `${API_BASE_URL}/user-location-roles/get-individual-users`;
export const INDIVIDUAL_USER_LOCATION_ROLE_URL = `${API_BASE_URL}/user-location-roles`;

export const USER_LOCATION_ROLE_URL = `${API_BASE_URL}/individual-user-location-roles`;

// Service endpoints (additional)
export const SERVICE_DETAILS = `${API_BASE_URL}/services`;

// Additional user endpoints
export const USERS_URL = `${API_BASE_URL}/users`;

// Department and designation endpoints
export const DEPARTMENT_URL = `${API_BASE_URL}/departments`;
export const DESIGNATION_URL = `${API_BASE_URL}/designations`;

// Asset Tracking & Maintenance endpoints
export const ATM_BASE_URL = `${API_BASE_URL}/atm`;
export const ATM_ASSETS = `${ATM_BASE_URL}/assets`;
export const ATM_MAINTENANCE = `${ATM_BASE_URL}/maintenance`;
export const ATM_SENSORS = `${ATM_BASE_URL}/sensors`;
export const ATM_CALIBRATIONS = `${ATM_BASE_URL}/calibrations`;
export const ATM_DOWNTIME = `${ATM_BASE_URL}/downtime`;
export const ATM_DASHBOARD = `${ATM_BASE_URL}/dashboard`;
export const ATM_SENSOR_ALERTS = `${ATM_BASE_URL}/sensor-alerts`;
export const ATM_ASSET_CATEGORIES = `${ATM_BASE_URL}/asset-categories`;

// Other endpoints
export const GMS1_URL = `${API_BASE_URL}/gms1`;
export const WORKING_GROUP_URL = `${API_BASE_URL}/working-groups`;
