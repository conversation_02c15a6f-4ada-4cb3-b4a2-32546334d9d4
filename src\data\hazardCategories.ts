import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  UserCog,
  Brain,
  Skull,
  MoreHorizontal,
  Anchor,
  Footprints,
  Car,
  Umbrella,
  Zap,
  Thermometer,
  Atom,
  Flame,
  Droplets,
  Wind,
  Snowflake,
  Wrench,
  Truck,
  Axe,
  Stethoscope,
  Pill,
  LampFloor,
  FlaskConical,
  Skull as SkullIcon,
  Volume2,
  Loader,
  Lightbulb,
  Keyboard,
  Armchair,
  Users,
  Clock,
  Briefcase,
  Heart,
  Syringe,
  Bug,
  Leaf,
  Microscope,
  Skull as SkullIcon2,
  AlertOctagon,
  AlertCircle,
  Trash2,
  Droplet,
  Bomb,
  Sparkles,
  Gauge,
  Box,
  PlusCircle,
} from 'lucide-react';

// Define the type for a hazard item
export interface HazardItem {
  id: string;
  label: string;
  icon: React.ElementType;
}

// Define the type for a hazard category
export interface HazardCategory {
  id: string;
  label: string;
  hazards: HazardItem[];
}

// Create the hazard categories data
export const hazardCategories: HazardCategory[] = [
  {
    id: 'situational',
    label: 'Situational',
    hazards: [
      { id: 'moving-ship', label: 'Moving Ship', icon: Anchor },
      { id: 'trips-slips-falls', label: 'Trips, Slips and Falls', icon: Footprints },
      { id: 'vehicular-movement', label: 'Vehicular Movement', icon: Car },
      { id: 'drowning', label: 'Drowning', icon: Umbrella },
      { id: 'electrical-shock', label: 'Electrical Shock', icon: Zap },
      { id: 'extreme-temperature', label: 'Extreme Temperature', icon: Thermometer },
      { id: 'radiation', label: 'Radiation', icon: Atom },
      { id: 'fire', label: 'Fire', icon: Flame },
    ]
  },
  {
    id: 'physical',
    label: 'Physical',
    hazards: [
      { id: 'noise', label: 'Noise', icon: Volume2 },
      { id: 'vibration', label: 'Vibration', icon: Loader },
      { id: 'lighting', label: 'Poor Lighting', icon: Lightbulb },
      { id: 'manual-handling', label: 'Manual Handling', icon: Wrench },
      { id: 'mechanical', label: 'Mechanical', icon: Hammer },
      { id: 'working-at-height', label: 'Working at Height', icon: AlertTriangle },
      { id: 'confined-space', label: 'Confined Space', icon: Box },
      { id: 'falling-objects', label: 'Falling Objects', icon: AlertOctagon },
    ]
  },
  {
    id: 'chemical',
    label: 'Chemical',
    hazards: [
      { id: 'toxic', label: 'Toxic Substances', icon: Skull },
      { id: 'corrosive', label: 'Corrosive Substances', icon: Droplet },
      { id: 'flammable', label: 'Flammable Substances', icon: Flame },
      { id: 'explosive', label: 'Explosive Materials', icon: Bomb },
      { id: 'reactive', label: 'Reactive Substances', icon: Beaker },
      { id: 'oxidizing', label: 'Oxidizing Agents', icon: Sparkles },
      { id: 'compressed-gas', label: 'Compressed Gas', icon: Gauge },
      { id: 'asphyxiant', label: 'Asphyxiants', icon: Wind },
    ]
  },
  {
    id: 'biological',
    label: 'Biological',
    hazards: [
      { id: 'viruses', label: 'Viruses', icon: PlusCircle },
      { id: 'bacteria', label: 'Bacteria', icon: FlaskConical },
      { id: 'fungi', label: 'Fungi', icon: Leaf },
      { id: 'parasites', label: 'Parasites', icon: Bug },
      { id: 'blood-pathogens', label: 'Blood-borne Pathogens', icon: Droplets },
      { id: 'animal-bites', label: 'Animal Bites/Stings', icon: Syringe },
      { id: 'biological-waste', label: 'Biological Waste', icon: Trash2 },
      { id: 'allergens', label: 'Allergens', icon: Microscope },
    ]
  },
  {
    id: 'ergonomic',
    label: 'Ergonomic',
    hazards: [
      { id: 'repetitive-motion', label: 'Repetitive Motion', icon: Keyboard },
      { id: 'awkward-posture', label: 'Awkward Posture', icon: Armchair },
      { id: 'prolonged-sitting', label: 'Prolonged Sitting/Standing', icon: Clock },
      { id: 'heavy-lifting', label: 'Heavy Lifting', icon: Truck },
      { id: 'workstation-design', label: 'Poor Workstation Design', icon: Briefcase },
      { id: 'tool-design', label: 'Improper Tool Design', icon: Axe },
      { id: 'physical-strain', label: 'Physical Strain', icon: Heart },
      { id: 'repetitive-strain', label: 'Repetitive Strain Injury', icon: Wrench },
    ]
  },
  {
    id: 'psychosocial',
    label: 'Psychosocial',
    hazards: [
      { id: 'work-stress', label: 'Work-related Stress', icon: Brain },
      { id: 'workplace-violence', label: 'Workplace Violence', icon: AlertTriangle },
      { id: 'bullying', label: 'Bullying/Harassment', icon: Users },
      { id: 'fatigue', label: 'Fatigue', icon: Clock },
      { id: 'shift-work', label: 'Shift Work', icon: Clock },
      { id: 'poor-communication', label: 'Poor Communication', icon: UserCog },
      { id: 'job-insecurity', label: 'Job Insecurity', icon: Briefcase },
      { id: 'work-life-imbalance', label: 'Work-life Imbalance', icon: Users },
    ]
  },
  {
    id: 'ghs',
    label: 'GHS',
    hazards: [
      { id: 'explosive', label: 'Explosive', icon: Bomb },
      { id: 'flammable', label: 'Flammable', icon: Flame },
      { id: 'oxidizing', label: 'Oxidizing', icon: Sparkles },
      { id: 'compressed-gas', label: 'Compressed Gas', icon: Gauge },
      { id: 'corrosive', label: 'Corrosive', icon: Beaker },
      { id: 'toxic', label: 'Toxic', icon: SkullIcon2 },
      { id: 'health-hazard', label: 'Health Hazard', icon: Stethoscope },
      { id: 'environmental-hazard', label: 'Environmental Hazard', icon: Leaf },
    ]
  },
  {
    id: 'others',
    label: 'Others',
    hazards: [
      { id: 'weather-conditions', label: 'Adverse Weather Conditions', icon: Snowflake },
      { id: 'natural-disasters', label: 'Natural Disasters', icon: AlertCircle },
      { id: 'wildlife', label: 'Wildlife', icon: Bug },
      { id: 'remote-location', label: 'Remote Location', icon: AlertTriangle },
      { id: 'security-threats', label: 'Security Threats', icon: AlertTriangle },
      { id: 'custom-hazard', label: 'Custom Hazard', icon: MoreHorizontal },
    ]
  },
];
