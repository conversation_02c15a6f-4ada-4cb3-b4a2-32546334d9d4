import React, { useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Plus,
  Trash2,
  Save,
  ArrowLeft,
  ArrowRight
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

// Types
interface RiskAssessmentFormProps {
  onSubmit: (data: any, isDraft: boolean) => void;
  onCancel: () => void;
}

interface SubActivity {
  id: string;
  name: string;
  description: string;
  hazards: string[];
  controls: string[];
  riskLevel: 'Low' | 'Medium' | 'High' | 'Critical';
}

const RiskAssessmentForm: React.FC<RiskAssessmentFormProps> = ({
  onSubmit,
  onCancel
}) => {
  const { toast } = useToast();
  
  // Form state
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    assessmentType: 'Routine',
    shortName: '',
    department: '',
    workActivity: '',
    teamMembers: [] as string[],
    activityDescription: '',
    recommendationLevel: '',
    controlMeasures: '',
    additionalRecommendation: '',
    signature: ''
  });
  
  const [subActivities, setSubActivities] = useState<SubActivity[]>([]);
  const [showAddSubActivity, setShowAddSubActivity] = useState(false);
  const [newSubActivity, setNewSubActivity] = useState({
    name: '',
    description: '',
    hazards: '',
    controls: ''
  });

  // Mock data
  const DEPARTMENTS = [
    { value: 'operations', label: 'Operations' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'safety', label: 'Safety' },
    { value: 'engineering', label: 'Engineering' },
    { value: 'quality', label: 'Quality Assurance' },
  ];

  const WORK_ACTIVITIES = [
    { value: 'chemical-handling', label: 'Chemical Handling' },
    { value: 'working-at-heights', label: 'Working at Heights' },
    { value: 'confined-space-entry', label: 'Confined Space Entry' },
    { value: 'electrical-work', label: 'Electrical Work' },
    { value: 'hot-work', label: 'Hot Work' },
  ];

  const TEAM_MEMBERS = [
    { value: 'john-doe', label: 'John Doe' },
    { value: 'jane-smith', label: 'Jane Smith' },
    { value: 'bob-johnson', label: 'Bob Johnson' },
    { value: 'alice-williams', label: 'Alice Williams' },
  ];

  const RECOMMENDATION_LEVELS = [
    { value: 'low', label: 'Low Priority' },
    { value: 'medium', label: 'Medium Priority' },
    { value: 'high', label: 'High Priority' },
    { value: 'critical', label: 'Critical Priority' },
  ];

  // Handle form field changes
  const handleFieldChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle team member selection
  const handleTeamMemberToggle = (memberId: string) => {
    setFormData(prev => ({
      ...prev,
      teamMembers: prev.teamMembers.includes(memberId)
        ? prev.teamMembers.filter(id => id !== memberId)
        : [...prev.teamMembers, memberId]
    }));
  };

  // Add sub-activity
  const handleAddSubActivity = () => {
    if (newSubActivity.name.trim()) {
      const subActivity: SubActivity = {
        id: uuidv4(),
        name: newSubActivity.name,
        description: newSubActivity.description,
        hazards: newSubActivity.hazards.split(',').map(h => h.trim()).filter(h => h),
        controls: newSubActivity.controls.split(',').map(c => c.trim()).filter(c => c),
        riskLevel: 'Medium' // Default risk level
      };
      
      setSubActivities(prev => [...prev, subActivity]);
      setNewSubActivity({ name: '', description: '', hazards: '', controls: '' });
      setShowAddSubActivity(false);
      
      toast({
        title: "Sub-activity Added",
        description: `${subActivity.name} has been added to the assessment.`,
      });
    }
  };

  // Remove sub-activity
  const handleRemoveSubActivity = (id: string) => {
    setSubActivities(prev => prev.filter(sa => sa.id !== id));
    toast({
      title: "Sub-activity Removed",
      description: "Sub-activity has been removed from the assessment.",
    });
  };

  // Navigation
  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle form submission
  const handleSubmit = (isDraft: boolean) => {
    const submissionData = {
      id: `RA-${uuidv4().substring(0, 8)}`,
      ...formData,
      subActivities,
      status: isDraft ? 'Draft' : 'Pending',
      createdBy: 'Current User',
      createdDate: new Date().toISOString().split('T')[0],
      lastUpdated: new Date().toISOString().split('T')[0],
    };

    onSubmit(submissionData, isDraft);
  };

  // Validation
  const isStepValid = (step: number) => {
    switch (step) {
      case 1:
        return formData.assessmentType && formData.shortName;
      case 2:
        return formData.department && formData.workActivity && formData.teamMembers.length > 0;
      case 3:
        return true; // Sub-activities are optional
      case 4:
        return formData.recommendationLevel && formData.controlMeasures && formData.signature;
      default:
        return false;
    }
  };

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      {/* Progress indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {[1, 2, 3, 4].map((step) => (
            <div key={step} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                step <= currentStep 
                  ? 'bg-primary text-primary-foreground' 
                  : 'bg-muted text-muted-foreground'
              }`}>
                {step}
              </div>
              {step < 4 && (
                <div className={`w-16 h-1 mx-2 ${
                  step < currentStep ? 'bg-primary' : 'bg-muted'
                }`} />
              )}
            </div>
          ))}
        </div>
        <div className="flex justify-between mt-2 text-sm text-muted-foreground">
          <span>Basic Info</span>
          <span>Details</span>
          <span>Sub-Activities</span>
          <span>Review</span>
        </div>
      </div>

      {/* Step 1: Basic Information */}
      {currentStep === 1 && (
        <Card>
          <CardHeader>
            <CardTitle>Risk Assessment Type & Basic Information</CardTitle>
            <CardDescription>Choose the type of risk assessment and provide basic details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label className="text-base font-medium">Assessment Type</Label>
              <RadioGroup
                value={formData.assessmentType}
                onValueChange={(value) => handleFieldChange('assessmentType', value)}
                className="mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="Routine" id="routine" />
                  <Label htmlFor="routine">Routine</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="Non-Routine" id="non-routine" />
                  <Label htmlFor="non-routine">Non-Routine</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="High Risk" id="high-risk" />
                  <Label htmlFor="high-risk">High Risk</Label>
                </div>
              </RadioGroup>
            </div>

            <div>
              <Label htmlFor="shortName">Short Name / Title</Label>
              <Input
                id="shortName"
                value={formData.shortName}
                onChange={(e) => handleFieldChange('shortName', e.target.value)}
                placeholder="Enter a short name for this assessment"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="activityDescription">Activity Description</Label>
              <Textarea
                id="activityDescription"
                value={formData.activityDescription}
                onChange={(e) => handleFieldChange('activityDescription', e.target.value)}
                placeholder="Describe the activity being assessed"
                className="mt-1 min-h-[100px]"
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button 
              onClick={nextStep} 
              disabled={!isStepValid(1)}
            >
              Next <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </CardFooter>
        </Card>
      )}

      {/* Step 2: Department & Work Activity */}
      {currentStep === 2 && (
        <Card>
          <CardHeader>
            <CardTitle>Department & Work Activity</CardTitle>
            <CardDescription>Select the department, work activity, and team members</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="department">Department</Label>
                <Select
                  value={formData.department}
                  onValueChange={(value) => handleFieldChange('department', value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {DEPARTMENTS.map((dept) => (
                      <SelectItem key={dept.value} value={dept.value}>
                        {dept.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="workActivity">Work Activity</Label>
                <Select
                  value={formData.workActivity}
                  onValueChange={(value) => handleFieldChange('workActivity', value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select work activity" />
                  </SelectTrigger>
                  <SelectContent>
                    {WORK_ACTIVITIES.map((activity) => (
                      <SelectItem key={activity.value} value={activity.value}>
                        {activity.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label>Team Members</Label>
              <div className="mt-2 space-y-2">
                {TEAM_MEMBERS.map((member) => (
                  <div key={member.value} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={member.value}
                      checked={formData.teamMembers.includes(member.value)}
                      onChange={() => handleTeamMemberToggle(member.value)}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor={member.value}>{member.label}</Label>
                  </div>
                ))}
              </div>
              {formData.teamMembers.length > 0 && (
                <div className="mt-3">
                  <p className="text-sm text-muted-foreground">Selected members:</p>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {formData.teamMembers.map((memberId) => {
                      const member = TEAM_MEMBERS.find(m => m.value === memberId);
                      return (
                        <Badge key={memberId} variant="secondary">
                          {member?.label}
                        </Badge>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={prevStep}>
              <ArrowLeft className="h-4 w-4 mr-2" /> Back
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => handleSubmit(true)}>
                <Save className="h-4 w-4 mr-2" /> Save as Draft
              </Button>
              <Button
                onClick={nextStep}
                disabled={!isStepValid(2)}
              >
                Next <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </CardFooter>
        </Card>
      )}

      {/* Step 3: Sub-Activities */}
      {currentStep === 3 && (
        <Card>
          <CardHeader>
            <CardTitle>Sub-Activities</CardTitle>
            <CardDescription>Add sub-activities to break down the risk assessment</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {subActivities.length === 0 ? (
              <div className="text-center py-8 border border-dashed rounded-lg">
                <p className="text-muted-foreground mb-4">No sub-activities added yet</p>
                <Button onClick={() => setShowAddSubActivity(true)}>
                  <Plus className="h-4 w-4 mr-2" /> Add Sub-Activity
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {subActivities.map((subActivity) => (
                  <Card key={subActivity.id} className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-medium">{subActivity.name}</h4>
                        {subActivity.description && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {subActivity.description}
                          </p>
                        )}
                        {subActivity.hazards.length > 0 && (
                          <div className="mt-2">
                            <p className="text-xs font-medium text-muted-foreground">Hazards:</p>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {subActivity.hazards.map((hazard, index) => (
                                <Badge key={index} variant="destructive" className="text-xs">
                                  {hazard}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                        {subActivity.controls.length > 0 && (
                          <div className="mt-2">
                            <p className="text-xs font-medium text-muted-foreground">Controls:</p>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {subActivity.controls.map((control, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {control}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveSubActivity(subActivity.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </Card>
                ))}
                <Button
                  variant="outline"
                  onClick={() => setShowAddSubActivity(true)}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" /> Add Another Sub-Activity
                </Button>
              </div>
            )}

            {/* Add Sub-Activity Form */}
            {showAddSubActivity && (
              <Card className="p-4 border-primary">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="subActivityName">Sub-Activity Name</Label>
                    <Input
                      id="subActivityName"
                      value={newSubActivity.name}
                      onChange={(e) => setNewSubActivity(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter sub-activity name"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="subActivityDescription">Description</Label>
                    <Textarea
                      id="subActivityDescription"
                      value={newSubActivity.description}
                      onChange={(e) => setNewSubActivity(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Describe this sub-activity"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="subActivityHazards">Hazards (comma-separated)</Label>
                    <Input
                      id="subActivityHazards"
                      value={newSubActivity.hazards}
                      onChange={(e) => setNewSubActivity(prev => ({ ...prev, hazards: e.target.value }))}
                      placeholder="e.g., Chemical exposure, Fall risk, Electrical shock"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="subActivityControls">Controls (comma-separated)</Label>
                    <Input
                      id="subActivityControls"
                      value={newSubActivity.controls}
                      onChange={(e) => setNewSubActivity(prev => ({ ...prev, controls: e.target.value }))}
                      placeholder="e.g., PPE required, Safety barriers, Lockout procedures"
                      className="mt-1"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleAddSubActivity} disabled={!newSubActivity.name.trim()}>
                      Add Sub-Activity
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowAddSubActivity(false);
                        setNewSubActivity({ name: '', description: '', hazards: '', controls: '' });
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={prevStep}>
              <ArrowLeft className="h-4 w-4 mr-2" /> Back
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => handleSubmit(true)}>
                <Save className="h-4 w-4 mr-2" /> Save as Draft
              </Button>
              <Button onClick={nextStep}>
                Next <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </CardFooter>
        </Card>
      )}

      {/* Step 4: Review & Submit */}
      {currentStep === 4 && (
        <Card>
          <CardHeader>
            <CardTitle>Review & Submit</CardTitle>
            <CardDescription>Review your assessment and provide final details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="recommendationLevel">Recommendation Level</Label>
              <Select
                value={formData.recommendationLevel}
                onValueChange={(value) => handleFieldChange('recommendationLevel', value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select recommendation level" />
                </SelectTrigger>
                <SelectContent>
                  {RECOMMENDATION_LEVELS.map((level) => (
                    <SelectItem key={level.value} value={level.value}>
                      {level.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="controlMeasures">Control Measures</Label>
              <Textarea
                id="controlMeasures"
                value={formData.controlMeasures}
                onChange={(e) => handleFieldChange('controlMeasures', e.target.value)}
                placeholder="Describe the control measures recommended"
                className="mt-1 min-h-[100px]"
              />
            </div>

            <div>
              <Label htmlFor="additionalRecommendation">Additional Recommendations (Optional)</Label>
              <Textarea
                id="additionalRecommendation"
                value={formData.additionalRecommendation}
                onChange={(e) => handleFieldChange('additionalRecommendation', e.target.value)}
                placeholder="Any additional recommendations"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="signature">Digital Signature</Label>
              <Input
                id="signature"
                value={formData.signature}
                onChange={(e) => handleFieldChange('signature', e.target.value)}
                placeholder="Type your full name as digital signature"
                className="mt-1"
              />
            </div>

            {/* Summary */}
            <div className="border rounded-lg p-4 bg-muted/20">
              <h4 className="font-medium mb-3">Assessment Summary</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Type:</span> {formData.assessmentType}
                </div>
                <div>
                  <span className="font-medium">Title:</span> {formData.shortName}
                </div>
                <div>
                  <span className="font-medium">Department:</span> {
                    DEPARTMENTS.find(d => d.value === formData.department)?.label || 'Not selected'
                  }
                </div>
                <div>
                  <span className="font-medium">Work Activity:</span> {
                    WORK_ACTIVITIES.find(a => a.value === formData.workActivity)?.label || 'Not selected'
                  }
                </div>
                <div>
                  <span className="font-medium">Team Members:</span> {formData.teamMembers.length}
                </div>
                <div>
                  <span className="font-medium">Sub-Activities:</span> {subActivities.length}
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={prevStep}>
              <ArrowLeft className="h-4 w-4 mr-2" /> Back
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => handleSubmit(true)}>
                <Save className="h-4 w-4 mr-2" /> Save as Draft
              </Button>
              <Button
                onClick={() => handleSubmit(false)}
                disabled={!isStepValid(4)}
              >
                Submit Assessment
              </Button>
            </div>
          </CardFooter>
        </Card>
      )}
    </div>
  );
};

export default RiskAssessmentForm;
