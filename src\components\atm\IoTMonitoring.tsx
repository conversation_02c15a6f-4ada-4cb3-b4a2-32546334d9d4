import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Gauge,
  Thermometer,
  Zap,
  Droplets,
  Wind,
  TrendingUp,
  TrendingDown,
  Minus,
  Wifi,
  WifiOff,
  Bell,
  BellOff,
  RefreshCw
} from 'lucide-react';
import { IoTSensor, SensorReading, SensorAlert, SensorStatus, SensorType, AlertLevel } from '@/types/atm';
import { fetchSensors, fetchSensorAlerts, acknowledgeSensorAlert } from '@/services/atmApi';

const IoTMonitoring = () => {
  const [sensors, setSensors] = useState<IoTSensor[]>([]);
  const [alerts, setAlerts] = useState<SensorAlert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const { toast } = useToast();

  useEffect(() => {
    loadSensors();
    loadAlerts();
    
    // Set up real-time updates (mock)
    const interval = setInterval(() => {
      updateSensorReadings();
      setLastUpdate(new Date());
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const loadSensors = async () => {
    try {
      setIsLoading(true);
      const data = await fetchSensors();
      setSensors(data);
    } catch (error) {
      console.error('Error loading sensors:', error);
      // Use mock data for demo
      setSensors(getMockSensors());
      toast({
        title: "Demo Mode",
        description: "Using mock IoT sensor data for demonstration",
        variant: "default"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadAlerts = async () => {
    try {
      const data = await fetchSensorAlerts(false); // Get unacknowledged alerts
      setAlerts(data);
    } catch (error) {
      console.error('Error loading alerts:', error);
      setAlerts(getMockAlerts());
    }
  };

  const updateSensorReadings = () => {
    // Simulate real-time sensor updates
    setSensors(prevSensors => 
      prevSensors.map(sensor => ({
        ...sensor,
        lastReading: {
          ...sensor.lastReading!,
          value: generateRandomReading(sensor.type, sensor.lastReading?.value || 0),
          timestamp: new Date().toISOString()
        }
      }))
    );
  };

  const generateRandomReading = (type: SensorType, currentValue: number): number => {
    const variation = currentValue * 0.05; // 5% variation
    const change = (Math.random() - 0.5) * 2 * variation;
    return Math.max(0, currentValue + change);
  };

  const getMockSensors = (): IoTSensor[] => [
    {
      id: '1',
      assetId: '1',
      name: 'HVAC-A1 Pressure Sensor',
      type: SensorType.PRESSURE,
      location: 'Building A - HVAC Unit',
      status: SensorStatus.ONLINE,
      lastReading: {
        id: '1',
        sensorId: '1',
        value: 45.2,
        unit: 'PSI',
        timestamp: new Date().toISOString(),
        quality: 'Good' as any
      },
      thresholds: [
        { id: '1', name: 'High Pressure', minValue: undefined, maxValue: 50, alertLevel: AlertLevel.WARNING, enabled: true },
        { id: '2', name: 'Critical Pressure', minValue: undefined, maxValue: 55, alertLevel: AlertLevel.CRITICAL, enabled: true }
      ],
      calibrationDate: '2024-01-15',
      nextCalibrationDate: '2024-07-15',
      created: '2024-01-01T00:00:00Z',
      updated: new Date().toISOString()
    },
    {
      id: '2',
      assetId: '2',
      name: 'PV-101 Temperature Sensor',
      type: SensorType.TEMPERATURE,
      location: 'Plant Floor - Pressure Vessel',
      status: SensorStatus.ONLINE,
      lastReading: {
        id: '2',
        sensorId: '2',
        value: 185.7,
        unit: '°F',
        timestamp: new Date().toISOString(),
        quality: 'Good' as any
      },
      thresholds: [
        { id: '3', name: 'High Temperature', minValue: undefined, maxValue: 200, alertLevel: AlertLevel.WARNING, enabled: true },
        { id: '4', name: 'Critical Temperature', minValue: undefined, maxValue: 220, alertLevel: AlertLevel.CRITICAL, enabled: true }
      ],
      calibrationDate: '2024-01-10',
      nextCalibrationDate: '2024-07-10',
      created: '2024-01-01T00:00:00Z',
      updated: new Date().toISOString()
    },
    {
      id: '3',
      assetId: '3',
      name: 'CP-205 Vibration Sensor',
      type: SensorType.VIBRATION,
      location: 'Pump House - Centrifugal Pump',
      status: SensorStatus.ERROR,
      lastReading: {
        id: '3',
        sensorId: '3',
        value: 12.8,
        unit: 'mm/s',
        timestamp: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
        quality: 'Bad' as any
      },
      thresholds: [
        { id: '5', name: 'High Vibration', minValue: undefined, maxValue: 10, alertLevel: AlertLevel.WARNING, enabled: true },
        { id: '6', name: 'Critical Vibration', minValue: undefined, maxValue: 15, alertLevel: AlertLevel.CRITICAL, enabled: true }
      ],
      calibrationDate: '2024-01-20',
      nextCalibrationDate: '2024-07-20',
      created: '2024-01-01T00:00:00Z',
      updated: new Date().toISOString()
    },
    {
      id: '4',
      assetId: '1',
      name: 'HVAC-A1 Flow Sensor',
      type: SensorType.FLOW,
      location: 'Building A - HVAC Unit',
      status: SensorStatus.ONLINE,
      lastReading: {
        id: '4',
        sensorId: '4',
        value: 2450,
        unit: 'CFM',
        timestamp: new Date().toISOString(),
        quality: 'Good' as any
      },
      thresholds: [
        { id: '7', name: 'Low Flow', minValue: 2000, maxValue: undefined, alertLevel: AlertLevel.WARNING, enabled: true },
        { id: '8', name: 'Critical Low Flow', minValue: 1500, maxValue: undefined, alertLevel: AlertLevel.CRITICAL, enabled: true }
      ],
      calibrationDate: '2024-01-15',
      nextCalibrationDate: '2024-07-15',
      created: '2024-01-01T00:00:00Z',
      updated: new Date().toISOString()
    }
  ];

  const getMockAlerts = (): SensorAlert[] => [
    {
      id: '1',
      sensorId: '3',
      thresholdId: '6',
      message: 'Critical vibration level detected on CP-205',
      level: AlertLevel.CRITICAL,
      timestamp: new Date(Date.now() - 180000).toISOString(), // 3 minutes ago
      acknowledged: false
    },
    {
      id: '2',
      sensorId: '1',
      thresholdId: '1',
      message: 'High pressure warning on HVAC-A1',
      level: AlertLevel.WARNING,
      timestamp: new Date(Date.now() - 600000).toISOString(), // 10 minutes ago
      acknowledged: false
    }
  ];

  const getSensorIcon = (type: SensorType) => {
    const icons = {
      [SensorType.PRESSURE]: Gauge,
      [SensorType.TEMPERATURE]: Thermometer,
      [SensorType.VIBRATION]: Activity,
      [SensorType.FLOW]: Wind,
      [SensorType.LEVEL]: Droplets,
      [SensorType.HUMIDITY]: Droplets,
      [SensorType.VOLTAGE]: Zap,
      [SensorType.CURRENT]: Zap
    };
    const Icon = icons[type] || Activity;
    return <Icon className="h-5 w-5" />;
  };

  const getStatusBadge = (status: SensorStatus) => {
    const variants = {
      [SensorStatus.ONLINE]: { variant: 'default' as any, icon: Wifi, color: 'text-green-600' },
      [SensorStatus.OFFLINE]: { variant: 'secondary' as any, icon: WifiOff, color: 'text-gray-600' },
      [SensorStatus.ERROR]: { variant: 'destructive' as any, icon: AlertTriangle, color: 'text-red-600' },
      [SensorStatus.MAINTENANCE]: { variant: 'outline' as any, icon: CheckCircle, color: 'text-orange-600' }
    };
    const config = variants[status];
    const Icon = config.icon;
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {status}
      </Badge>
    );
  };

  const getAlertBadge = (level: AlertLevel) => {
    const variants = {
      [AlertLevel.INFO]: 'outline',
      [AlertLevel.WARNING]: 'secondary',
      [AlertLevel.CRITICAL]: 'destructive',
      [AlertLevel.EMERGENCY]: 'destructive'
    };
    return <Badge variant={variants[level] as any}>{level}</Badge>;
  };

  const getReadingTrend = (value: number, thresholds: any[]) => {
    // Simple trend calculation based on thresholds
    const warningThreshold = thresholds.find(t => t.alertLevel === AlertLevel.WARNING);
    if (!warningThreshold) return <Minus className="h-4 w-4 text-gray-400" />;
    
    if (warningThreshold.maxValue && value > warningThreshold.maxValue * 0.9) {
      return <TrendingUp className="h-4 w-4 text-red-500" />;
    } else if (warningThreshold.minValue && value < warningThreshold.minValue * 1.1) {
      return <TrendingDown className="h-4 w-4 text-red-500" />;
    }
    return <Minus className="h-4 w-4 text-green-500" />;
  };

  const handleAcknowledgeAlert = async (alertId: string) => {
    try {
      await acknowledgeSensorAlert(alertId);
      setAlerts(prev => prev.filter(alert => alert.id !== alertId));
      toast({
        title: "Alert Acknowledged",
        description: "The alert has been acknowledged successfully",
      });
    } catch (error) {
      console.error('Error acknowledging alert:', error);
      toast({
        title: "Error",
        description: "Failed to acknowledge alert",
        variant: "destructive"
      });
    }
  };

  const getHealthScore = () => {
    const onlineSensors = sensors.filter(s => s.status === SensorStatus.ONLINE).length;
    return Math.round((onlineSensors / sensors.length) * 100);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Refresh */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">IoT Monitoring</h2>
          <p className="text-muted-foreground">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </p>
        </div>
        <Button onClick={() => { loadSensors(); loadAlerts(); }} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">System Health</p>
                <p className="text-2xl font-bold">{getHealthScore()}%</p>
              </div>
              <Activity className="h-8 w-8 text-green-600" />
            </div>
            <Progress value={getHealthScore()} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Online Sensors</p>
                <p className="text-2xl font-bold">
                  {sensors.filter(s => s.status === SensorStatus.ONLINE).length}
                </p>
                <p className="text-xs text-muted-foreground">of {sensors.length} total</p>
              </div>
              <Wifi className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Alerts</p>
                <p className="text-2xl font-bold text-red-600">{alerts.length}</p>
                <p className="text-xs text-muted-foreground">
                  {alerts.filter(a => a.level === AlertLevel.CRITICAL).length} critical
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Offline Sensors</p>
                <p className="text-2xl font-bold">
                  {sensors.filter(s => s.status === SensorStatus.OFFLINE || s.status === SensorStatus.ERROR).length}
                </p>
                <p className="text-xs text-muted-foreground">Need attention</p>
              </div>
              <WifiOff className="h-8 w-8 text-gray-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Active Alerts */}
      {alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Active Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {alerts.map(alert => (
                <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <AlertTriangle className={`h-5 w-5 ${
                      alert.level === AlertLevel.CRITICAL ? 'text-red-600' : 'text-yellow-600'
                    }`} />
                    <div>
                      <p className="font-medium">{alert.message}</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(alert.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getAlertBadge(alert.level)}
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleAcknowledgeAlert(alert.id)}
                    >
                      <BellOff className="h-4 w-4 mr-1" />
                      Acknowledge
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sensor Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sensors.map(sensor => (
          <Card key={sensor.id}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg flex items-center gap-2">
                  {getSensorIcon(sensor.type)}
                  {sensor.name}
                </CardTitle>
                {getStatusBadge(sensor.status)}
              </div>
              <CardDescription>{sensor.location}</CardDescription>
            </CardHeader>
            <CardContent>
              {sensor.lastReading ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-2xl font-bold">
                        {sensor.lastReading.value.toFixed(1)} {sensor.lastReading.unit}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(sensor.lastReading.timestamp).toLocaleString()}
                      </p>
                    </div>
                    {getReadingTrend(sensor.lastReading.value, sensor.thresholds)}
                  </div>
                  
                  {/* Threshold indicators */}
                  <div className="space-y-1">
                    {sensor.thresholds.filter(t => t.enabled).map(threshold => {
                      const isTriggered = threshold.maxValue 
                        ? sensor.lastReading!.value > threshold.maxValue
                        : threshold.minValue 
                        ? sensor.lastReading!.value < threshold.minValue
                        : false;
                      
                      return (
                        <div key={threshold.id} className="flex items-center justify-between text-xs">
                          <span className={isTriggered ? 'text-red-600 font-medium' : 'text-muted-foreground'}>
                            {threshold.name}
                          </span>
                          <span className={isTriggered ? 'text-red-600 font-medium' : 'text-muted-foreground'}>
                            {threshold.maxValue ? `< ${threshold.maxValue}` : `> ${threshold.minValue}`} {sensor.lastReading!.unit}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">No recent readings</p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default IoTMonitoring;
