import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Card } from '@/components/ui/card';
import HazardPanel from '@/components/risk-assessment/Component/Hazards/HazardPanel';

interface HazardItem {
  id: string;
  name: string;
  hazardName?: string;
}

interface TaskItem {
  selected?: HazardItem[];
  [key: number]: any;
}

interface HazardAccordionProps {
  hazards: any[];
  activeTabIndex: number;
  setActiveTabIndex: (index: number) => void;
  selectedHazards: HazardItem[];
  onClickHazards: (hazard: HazardItem) => void;
  required: boolean;
  item: TaskItem[];
}

const HazardAccordion: React.FC<HazardAccordionProps> = ({ 
  hazards, 
  activeTabIndex, 
  setActiveTabIndex, 
  selectedHazards, 
  onClickHazards, 
  required, 
  item 
}) => {
  const hasError = required === false && item[1]?.selected?.length === 0;

  return (
    <Accordion type="single" defaultValue="hazards" collapsible>
      <AccordionItem value="hazards">
        <AccordionTrigger 
          className={`${hasError ? 'border border-destructive' : ''} hover:no-underline`}
        >
          <div className="text-left">
            <h6 className="font-semibold text-base">Hazards Identification</h6>
            <p className="text-sm text-muted-foreground mt-1">
              Identify potential hazards associated with sub-activity
            </p>
          </div>
        </AccordionTrigger>
        <AccordionContent>
          <Card className="border">
            <HazardPanel
              hazards={hazards}
              activeTabIndex={activeTabIndex}
              setActiveTabIndex={setActiveTabIndex}
              selectedHazards={item[1]?.selected || []}
              onClickHazards={onClickHazards}
            />
          </Card>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default HazardAccordion;
