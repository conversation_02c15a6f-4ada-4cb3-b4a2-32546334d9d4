import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Info, Save, FileText, Eye, Send, Upload, X } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import apiService from "@/services/apiService";
import { API_BASE_URL } from "@/constants/index";
import DocumentGeneralInfoModal from "@/components/documents/DocumentGeneralInfoModal";
import DocumentSidebar from "@/components/document-curation/DocumentSidebar";
import DocumentDroppableArea from "@/components/document-curation/DocumentDroppableArea";
import DocumentPreview from "@/components/document-curation/DocumentPreview";

// API Configuration
const DOCUMENTS_API = `${API_BASE_URL}/documents`;

interface DocumentComponent {
  id: string;
  type: string;
  content: any;
  position: number;
}

interface Document {
  id: string;
  name: string;
  type: 'pdf' | 'doc' | 'image' | 'video' | 'other';
  size: string;
  uploadedBy: string;
  uploadedDate: string;
  category: string;
  tags: string[];
  description?: string;
  maskId?: string;
  scopeApplicability?: string;
  purpose?: string;
  docId?: string;
  created?: string;
  updated?: string;
  creatorTargetDate?: string;
  reviewerTargetDate?: string;
  approverTargetDate?: string;
  initiatorId?: string;
  creatorId?: string;
  reviewerId?: string;
  approverId?: string;
  documentCategoryId?: string;
  initiator?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  creator?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  reviewer?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  approver?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  documentCategory?: {
    id: string;
    name: string;
    level?: number;
  };
  value?: DocumentComponent[];
}

const DocumentCurationPage: React.FC = () => {
  const navigate = useNavigate();
  const { id, actionId } = useParams<{ id: string; actionId: string }>();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();

  // Check if we're in edit mode
  const isEditMode = searchParams.get('mode') === 'edit';

  // State management
  const [document, setDocument] = useState<Document | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isGeneralInfoOpen, setIsGeneralInfoOpen] = useState(false);
  const [actionType, setActionType] = useState<string | null>(null);
  const [actionComments, setActionComments] = useState<string>('');

  // Document curation state
  const [documentComponents, setDocumentComponents] = useState<DocumentComponent[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);

  // Edit mode state
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editFormData, setEditFormData] = useState({
    reasonReview: '',
    changes: '',
    reasonChange: '',
    initiatedBy: '',
    approvedBy: '',
    reference: ''
  });
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load document data on component mount
  useEffect(() => {
    const loadDocumentData = async () => {
      if (id) {
        try {
          setIsLoading(true);

          // Fetch action details if actionId is provided to determine action type
          if (actionId) {
            try {
              const actionResponse = await apiService.get(`/actions/${actionId}`);
              setActionType(actionResponse.actionType);
              setActionComments(actionResponse.comments || actionResponse.reviewerComments || actionResponse.reviewComments || '');
              console.log('Action type:', actionResponse.actionType);
              console.log('Action comments:', actionResponse.comments);
            } catch (error) {
              console.error('Error fetching action details:', error);
              // Continue with document loading even if action fetch fails
            }
          }

          // Fetch document details from API
          const uriString = {
            include: [
              { relation: "creator" },
              { relation: "documentCategory" },
              { relation: "reviewer" },
              { relation: "approver" },
              { relation: "initiator" }
            ]
          };

          const url = `${DOCUMENTS_API}/${id}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
          const documentResponse = await apiService.get(url);

          // Transform API response to match our Document interface
          const transformedDocument: Document = {
            id: documentResponse.id,
            name: documentResponse.name,
            type: getFileTypeFromName(documentResponse.name),
            size: documentResponse.size || 'Unknown',
            uploadedBy: documentResponse.creator?.firstName || documentResponse.uploadedBy || documentResponse.createdBy || 'Unknown',
            uploadedDate: documentResponse.uploadedDate || documentResponse.created || new Date().toISOString(),
            category: documentResponse.documentCategory?.name || documentResponse.category || 'Uncategorized',
            tags: documentResponse.keywords ? documentResponse.keywords.split(',').map((tag: string) => tag.trim()) : [],
            description: documentResponse.purpose || documentResponse.description || '',
            maskId: documentResponse.maskId,
            scopeApplicability: documentResponse.scopeApplicability,
            purpose: documentResponse.purpose,
            docId: documentResponse.docId,
            created: documentResponse.created,
            updated: documentResponse.updated,
            creatorTargetDate: documentResponse.creatorTargetDate,
            reviewerTargetDate: documentResponse.reviewerTargetDate,
            approverTargetDate: documentResponse.approverTargetDate,
            initiatorId: documentResponse.initiatorId,
            creatorId: documentResponse.creatorId,
            reviewerId: documentResponse.reviewerId,
            approverId: documentResponse.approverId,
            documentCategoryId: documentResponse.documentCategoryId,
            initiator: documentResponse.initiator,
            creator: documentResponse.creator,
            reviewer: documentResponse.reviewer,
            approver: documentResponse.approver,
            documentCategory: documentResponse.documentCategory
          };

          setDocument(transformedDocument);

          // Load existing document components from the value field
          if (documentResponse.value && Array.isArray(documentResponse.value)) {
            // Sort components by position to maintain order
            const sortedComponents = documentResponse.value.sort((a: any, b: any) => a.position - b.position);
            setDocumentComponents(sortedComponents);

            toast({
              title: "Document Loaded",
              description: `Document loaded with ${sortedComponents.length} existing components`,
            });
          } else {
            // No existing components, start with empty state
            setDocumentComponents([]);
          }
        } catch (error) {
          console.error("Error loading document:", error);
          toast({
            title: "Error",
            description: "Failed to load document data",
            variant: "destructive"
          });
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    };

    loadDocumentData();
  }, [id, actionId, toast]);

  // Helper function to determine file type from filename
  const getFileTypeFromName = (filename: string): 'pdf' | 'doc' | 'image' | 'video' | 'other' => {
    const extension = filename.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'doc';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return 'image';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return 'video';
      default:
        return 'other';
    }
  };

  // Drag and drop handlers
  const handleDragStart = () => {
    setIsDragging(true);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleAddComponent = (component: DocumentComponent) => {
    setDocumentComponents(prev => [...prev, component]);
    toast({
      title: "Component Added",
      description: `${getComponentLabel(component.type)} has been added.`,
    });
  };

  const handleComponentClick = (type: string) => {
    const newComponent: DocumentComponent = {
      id: generateId(),
      type: type,
      content: getDefaultContent(type),
      position: documentComponents.length
    };

    handleAddComponent(newComponent);
  };

  // Helper functions
  const generateId = () => {
    return 'comp_' + Math.random().toString(36).substr(2, 9);
  };

  const getComponentLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      'document-header': 'Document Header',
      'section-header': 'Section Header',
      'paragraph': 'Paragraph',
      'bullet-list': 'Bullet List',
      'numbered-list': 'Numbered List',
      'quote': 'Quote Block',
      'separator': 'Separator',
      'image': 'Image',
      'video': 'Video',
      'file-attachment': 'File Attachment',
      'table': 'Table',
      'link': 'Link',
      'download-button': 'Download Button',
      'text-input': 'Text Input',
      'checkbox': 'Checkbox',
      'date-picker': 'Date Picker',
      'signature': 'Signature',
      'file-upload': 'File Upload'
    };
    return labels[type] || type;
  };

  const getDefaultContent = (type: string) => {
    switch (type) {
      case 'document-header':
        return { text: 'Document Title', level: 1 };
      case 'section-header':
        return { text: 'Section Header', level: 2 };
      case 'paragraph':
        return { text: 'Enter your paragraph content here...' };
      case 'bullet-list':
        return { items: ['List item 1', 'List item 2', 'List item 3'] };
      case 'numbered-list':
        return { items: ['First item', 'Second item', 'Third item'] };
      case 'quote':
        return { text: 'Enter your quote here...', author: '' };
      case 'separator':
        return { style: 'line' };
      case 'image':
        return { src: '', alt: '', caption: '' };
      case 'video':
        return { filename: '', alt: '', caption: '' };
      case 'file-attachment':
        return { filename: '', description: '', size: '' };
      case 'table':
        return {
          headers: ['Column 1', 'Column 2', 'Column 3'],
          rows: [
            ['Row 1 Col 1', 'Row 1 Col 2', 'Row 1 Col 3'],
            ['Row 2 Col 1', 'Row 2 Col 2', 'Row 2 Col 3']
          ]
        };
      case 'link':
        return { text: 'Link text', url: '', target: '_blank' };
      case 'download-button':
        return { text: 'Download', filename: '', description: '' };
      case 'text-input':
        return { label: 'Text Input', placeholder: 'Enter text...', required: false };
      case 'checkbox':
        return { label: 'Checkbox option', checked: false };
      case 'date-picker':
        return { label: 'Select Date', required: false };
      case 'signature':
        return { label: 'Digital Signature', required: true };
      case 'file-upload':
        return { label: 'Upload File', acceptedTypes: '', maxSize: '10MB' };
      default:
        return { text: 'Default content' };
    }
  };

  const handleRemoveComponent = (id: string) => {
    setDocumentComponents(prev => prev.filter(comp => comp.id !== id));
    toast({
      title: "Component Removed",
      description: "Component has been removed.",
    });
  };

  const handleUpdateComponent = (id: string, content: any) => {
    setDocumentComponents(prev =>
      prev.map(comp => comp.id === id ? { ...comp, content } : comp)
    );
  };

  const handleReorderComponents = (sourceIndex: number, targetIndex: number) => {
    setDocumentComponents(prev => {
      const newComponents = [...prev];
      const [removed] = newComponents.splice(sourceIndex, 1);
      newComponents.splice(targetIndex, 0, removed);
      return newComponents.map((comp, index) => ({ ...comp, position: index }));
    });
  };

  const handleBack = () => {
    navigate('/apps/doc');
  };

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newFiles = Array.from(files);
      setUploadedFiles(prev => [...prev, ...newFiles]);
    }
  };

  // Remove uploaded file
  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleGeneralInfo = () => {
    setIsGeneralInfoOpen(true);
  };

  const handlePreview = () => {
    setPreviewOpen(true);
  };

  // Validation function for document components
  const validateComponent = (component: DocumentComponent): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    switch (component.type) {
      case 'document-header':
        if (!component.content?.text || component.content.text.trim() === '' || component.content.text === 'Document Title') {
          errors.push("Document header text is required");
        }
        break;

      case 'section-header':
        if (!component.content?.text || component.content.text.trim() === '' || component.content.text === 'Section Header') {
          errors.push("Section header text is required");
        }
        break;

      case 'paragraph':
        if (!component.content?.text || component.content.text.trim() === '' || component.content.text === 'Enter your paragraph content here...') {
          errors.push("Paragraph content is required");
        }
        break;

      case 'bullet-list':
      case 'numbered-list':
        if (!component.content?.items || component.content.items.length === 0) {
          errors.push("List must have at least one item");
        } else {
          const hasEmptyItems = component.content.items.some((item: string) => !item || item.trim() === '');
          if (hasEmptyItems) {
            errors.push("All list items must have content");
          }
        }
        break;

      case 'quote':
        if (!component.content?.text || component.content.text.trim() === '' || component.content.text === 'Enter your quote here...') {
          errors.push("Quote text is required");
        }
        break;

      case 'image':
        if (!component.content?.filename || component.content.filename.trim() === '') {
          errors.push("Image file is required");
        }
        if (!component.content?.alt || component.content.alt.trim() === '') {
          errors.push("Image alt text is required");
        }
        break;

      case 'video':
        if (!component.content?.filename || component.content.filename.trim() === '') {
          errors.push("Video file is required");
        }
        if (!component.content?.alt || component.content.alt.trim() === '') {
          errors.push("Video description is required");
        }
        break;

      case 'file-attachment':
        if (!component.content?.filename || component.content.filename.trim() === '') {
          errors.push("File attachment is required");
        }
        break;

      case 'link':
        if (!component.content?.text || component.content.text.trim() === '' || component.content.text === 'Link text') {
          errors.push("Link text is required");
        }
        if (!component.content?.url || component.content.url.trim() === '') {
          errors.push("Link URL is required");
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  // Validation function for sending to reviewer
  const validateForReview = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // Check if document has components
    if (documentComponents.length === 0) {
      errors.push("Document must have at least one component before sending to reviewer");
      return { isValid: false, errors };
    }

    // Check if document has required basic information
    if (!document.name || document.name.trim() === '') {
      errors.push("Document name is required");
    }

    if (!document.purpose || document.purpose.trim() === '') {
      errors.push("Document purpose is required");
    }

    if (!document.reviewerId) {
      errors.push("Reviewer must be assigned to the document");
    }

    // Validate all components
    const componentValidations = documentComponents.map((component, index) => ({
      component,
      index,
      validation: validateComponent(component)
    }));

    const invalidComponents = componentValidations.filter(cv => !cv.validation.isValid);

    if (invalidComponents.length > 0) {
      invalidComponents.forEach(cv => {
        const componentLabel = getComponentLabel(cv.component.type);
        cv.validation.errors.forEach(error => {
          errors.push(`${componentLabel} (Position ${cv.index + 1}): ${error}`);
        });
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  const handleSave = async (action: 'draft' | 'review') => {
    try {
      // Validate for review submission
      if (action === 'review') {
        const validation = validateForReview();
        if (!validation.isValid) {
          toast({
            title: "Validation Error",
            description: `Please fix the following issues before sending to reviewer:\n${validation.errors.slice(0, 3).join('\n')}${validation.errors.length > 3 ? `\n... and ${validation.errors.length - 3} more issues` : ''}`,
            variant: "destructive"
          });
          return;
        }
      }

      // Prepare the document data with all components in JSON format
      const documentData = {
        documentId: document.id,
        name: document.name,
        category: document.category,
        categoryId: document.documentCategoryId,
        categoryName: document.documentCategory?.name,
        maskId: document.maskId,
        description: document.description,
        purpose: document.purpose,
        scopeApplicability: document.scopeApplicability,
        status: action === 'draft' ? 'draft' : 'pending_review',
        initiatorId: document.initiatorId,
        creatorId: document.creatorId,
        reviewerId: document.reviewerId,
        approverId: document.approverId,
        components: documentComponents.map(component => ({
          id: component.id,
          type: component.type,
          content: component.content,
          position: component.position
        })),
        metadata: {
          totalComponents: documentComponents.length,
          lastModified: new Date().toISOString(),
          action: action,
          version: '1.0'
        }
      };

      console.log('Document data to be saved:', JSON.stringify(documentData, null, 2));

      // Prepare API payload
      const apiPayload = {
        value: documentComponents.map(component => ({
          id: component.id,
          type: component.type,
          content: component.content,
          position: component.position
        })),
        docStatus: action === 'draft' ? 'In Draft' : 'Published'
      };

      // Determine which API endpoint to use based on action type
      let apiEndpoint = `/curator-submit-documents/${actionId}`;
      if (actionType === 'doc_reinitiated') {
        apiEndpoint = `/curator-resubmit-documents/${actionId}`;
      }

      console.log('Using API endpoint:', apiEndpoint);
      console.log('Action type:', actionType);

      // Make API call using the determined endpoint
      const response = await apiService.patch(apiEndpoint, apiPayload);

      console.log('API Response:', response);

      if (action === 'draft') {
        toast({
          title: "Draft Saved",
          description: `Document saved as draft with ${documentComponents.length} components.`,
        });
      } else if (action === 'review') {
        toast({
          title: "Sent to Reviewer",
          description: `Document with ${documentComponents.length} components has been sent for review.`,
        });
      }

      // Redirect to document page after successful save
      setTimeout(() => {
        navigate('/apps/doc');
      }, 1500); // Wait 1.5 seconds to allow user to see the success message
    } catch (error) {
      console.error('Error saving document:', error);

      // Handle specific error cases
      let errorMessage = "There was an error saving the document. Please try again.";
      if (action === 'review') {
        errorMessage = "Failed to send document to reviewer. Please try again.";
      }

      // Check if it's a network or API error
      if (error instanceof Error) {
        if (error.message.includes('401')) {
          errorMessage = "Authentication failed. Please login again.";
        } else if (error.message.includes('403')) {
          errorMessage = "You don't have permission to perform this action.";
        } else if (error.message.includes('404')) {
          errorMessage = "Document not found. Please refresh and try again.";
        } else if (error.message.includes('500')) {
          errorMessage = "Server error. Please try again later.";
        }
      }

      toast({
        title: "Save Failed",
        description: errorMessage,
        variant: "destructive"
      });
    }
  };

  // Handle edit save with additional details
  const handleEditSave = async () => {
    try {
      // First, update the document content
      const documentPayload = {
        value: documentComponents.map((comp, index) => ({
          id: comp.id,
          type: comp.type,
          content: comp.content,
          position: index + 1
        }))
      };

      console.log('Document Update Payload:', documentPayload);

      // Make PATCH request to update the document content
      const documentResponse = await apiService.patch(`${DOCUMENTS_API}/${id}`, documentPayload);

      console.log('Document Update Response:', documentResponse);

      // Second, upload files if any
      const uploadedFileNames: string[] = [];
      if (uploadedFiles.length > 0) {
        for (const file of uploadedFiles) {
          const formData = new FormData();
          formData.append('file', file);

          try {
            const fileResponse = await apiService.post('/files', formData, {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            });

            // Follow the pattern used in other components
            if (fileResponse?.files?.[0]?.originalname) {
              uploadedFileNames.push(fileResponse.files[0].originalname);
            }
          } catch (fileError) {
            console.error('Error uploading file:', file.name, fileError);
            // Continue with other files even if one fails
          }
        }
      }

      // Third, send the edit details to the document-updates endpoint
      const updateDetailsPayload = {
        reasonReview: editFormData.reasonReview,
        changes: editFormData.changes,
        reasonChange: editFormData.reasonChange,
        initiatedBy: editFormData.initiatedBy,
        approvedBy: editFormData.approvedBy,
        reference: editFormData.reference,
        files: uploadedFileNames // Add uploaded file names
      };

      console.log('Document Updates Payload:', updateDetailsPayload);

      // Make POST request to document-updates endpoint
      const updatesResponse = await apiService.post(`${DOCUMENTS_API}/${id}/document-updates`, updateDetailsPayload);

      console.log('Document Updates Response:', updatesResponse);

      toast({
        title: "Document Updated",
        description: `Document has been successfully updated with ${documentComponents.length} components, edit details recorded${uploadedFileNames.length > 0 ? `, and ${uploadedFileNames.length} file(s) uploaded` : ''}.`,
      });

      // Close the modal and redirect
      setIsEditModalOpen(false);
      setTimeout(() => {
        navigate('/apps/doc');
      }, 1500);
    } catch (error) {
      console.error('Error updating document:', error);

      let errorMessage = "There was an error updating the document. Please try again.";

      if (error instanceof Error) {
        if (error.message.includes('401')) {
          errorMessage = "Authentication failed. Please login again.";
        } else if (error.message.includes('403')) {
          errorMessage = "You don't have permission to perform this action.";
        } else if (error.message.includes('404')) {
          errorMessage = "Document not found. Please refresh and try again.";
        } else if (error.message.includes('500')) {
          errorMessage = "Server error. Please try again later.";
        }
      }

      toast({
        title: "Update Failed",
        description: errorMessage,
        variant: "destructive"
      });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-600">Loading document...</p>
        </div>
      </div>
    );
  }

  if (!document) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center">
        <div className="text-center">
          <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Document Not Found</h2>
          <p className="text-gray-500 mb-4">The requested document could not be found.</p>
          <Button onClick={handleBack} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Documents
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="bg-white border-b border-slate-200 shadow-sm">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handleBack}
                className="hover:bg-slate-50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <Separator orientation="vertical" className="h-6" />
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">📄</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-slate-800">
                    Document Curation
                  </h1>
                  <p className="text-sm text-slate-600">
                    {document.name}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm font-medium text-blue-700">
                  {document.documentCategory?.name || document.category}
                </span>
              </div>
              <div className="flex items-center gap-2 px-3 py-2 bg-green-50 rounded-lg border border-green-200">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-700">
                  {documentComponents.length} components
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleGeneralInfo}
                className="hover:bg-blue-50"
              >
                <Info className="h-4 w-4 mr-2" />
                Information
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handlePreview}
                className="hover:bg-purple-50"
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              {isEditMode ? (
                // Edit mode - only show Save button
                <Button
                  size="sm"
                  onClick={() => setIsEditModalOpen(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </Button>
              ) : (
                // Normal mode - show both buttons
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSave('draft')}
                    className="hover:bg-gray-50"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save as Draft
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => handleSave('review')}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    Send to Reviewer
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Reviewer Comments Section - Only show for doc_reinitiated */}
      {actionType === 'doc_reinitiated' && actionComments && (
        <div className="px-6 py-2 bg-amber-50 border-b border-amber-200">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-start gap-3 bg-white border border-amber-200 rounded-lg p-3">
              <div className="flex-shrink-0 w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center mt-0.5">
                <FileText className="h-4 w-4 text-amber-600" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="text-sm font-semibold text-amber-900">Reviewer Feedback</h3>
                  <span className="text-xs bg-amber-100 text-amber-700 px-2 py-0.5 rounded-full">
                    Returned for changes
                  </span>
                </div>
                <p className="text-sm text-gray-700 whitespace-pre-wrap leading-relaxed">
                  {actionComments}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex flex-1 min-h-0" onDragEnd={handleDragEnd}>
        <DocumentSidebar
          onDragStart={handleDragStart}
          onComponentClick={handleComponentClick}
        />

        <DocumentDroppableArea
          items={documentComponents}
          onAddItem={handleAddComponent}
          onRemoveItem={handleRemoveComponent}
          onUpdateItem={handleUpdateComponent}
          onReorderItems={handleReorderComponents}
        />
      </div>

      {/* Document General Info Modal */}
      <DocumentGeneralInfoModal
        open={isGeneralInfoOpen}
        onOpenChange={setIsGeneralInfoOpen}
        action={null}
        document={document}
        loading={false}
      />

      {/* Document Preview Modal */}
      <DocumentPreview
        open={previewOpen}
        onOpenChange={setPreviewOpen}
        components={documentComponents}
        documentTitle={document.name}
      />

      {/* Edit Details Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Document Edit Details</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="reasonReview">Reason for Review</Label>
                <Input
                  id="reasonReview"
                  value={editFormData.reasonReview}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, reasonReview: e.target.value }))}
                  placeholder="Enter reason for review"
                />
              </div>
              <div>
                <Label htmlFor="reference">Reference</Label>
                <Input
                  id="reference"
                  value={editFormData.reference}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, reference: e.target.value }))}
                  placeholder="Enter reference"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="changes">Changes Made</Label>
              <Textarea
                id="changes"
                value={editFormData.changes}
                onChange={(e) => setEditFormData(prev => ({ ...prev, changes: e.target.value }))}
                placeholder="Describe the changes made to the document"
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="reasonChange">Reason for Change</Label>
              <Textarea
                id="reasonChange"
                value={editFormData.reasonChange}
                onChange={(e) => setEditFormData(prev => ({ ...prev, reasonChange: e.target.value }))}
                placeholder="Explain why these changes were necessary"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="initiatedBy">Initiated By</Label>
                <Input
                  id="initiatedBy"
                  value={editFormData.initiatedBy}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, initiatedBy: e.target.value }))}
                  placeholder="Enter who initiated the change"
                />
              </div>
              <div>
                <Label htmlFor="approvedBy">Approved By</Label>
                <Input
                  id="approvedBy"
                  value={editFormData.approvedBy}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, approvedBy: e.target.value }))}
                  placeholder="Enter who approved the change"
                />
              </div>
            </div>

            {/* File Upload Section */}
            <div>
              <Label htmlFor="fileUpload">Upload Documents</Label>
              <div className="mt-2">
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="w-full border-dashed border-2 border-gray-300 hover:border-gray-400 h-20"
                >
                  <Upload className="h-6 w-6 mr-2" />
                  Click to upload files or drag and drop
                </Button>
              </div>

              {/* Display uploaded files */}
              {uploadedFiles.length > 0 && (
                <div className="mt-3 space-y-2">
                  <Label className="text-sm font-medium">Selected Files:</Label>
                  {uploadedFiles.map((file, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-gray-500" />
                        <span className="text-sm">{file.name}</span>
                        <span className="text-xs text-gray-500 ml-2">
                          ({(file.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                        className="h-6 w-6 p-0 hover:bg-red-100"
                      >
                        <X className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button
                variant="outline"
                onClick={() => setIsEditModalOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleEditSave}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Save Changes
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DocumentCurationPage;
