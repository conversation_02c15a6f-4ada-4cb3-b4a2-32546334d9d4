import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Trash2, Upload, Shield, FileImage, CheckCircle2, AlertCircle } from 'lucide-react';
import ImageComponent from '@/components/common/ImageComponent';

interface ControlOption {
  label: string;
  value: string;
}

interface ControlTypeOption {
  label: string;
  value: string;
}

interface CurrentControlItem {
  current_type: string;
  method: string;
  value: string;
  files: string[];
  required: boolean;
  validity: boolean;
}

interface CurrentControlProps {
  con: CurrentControlItem;
  i: number;
  control: ControlOption[];
  controlType: ControlTypeOption[];
  onImapactOn: (value: string, index: number, type: string) => void;
  onConseqText: (value: string, index: number, type: string) => void;
  onDeleteConseq: (index: number, type: string) => void;
  onConseqRequired: (currentValue: boolean, index: number, type: string, field: string) => void;
  handleTaskFileChange: (files: File[], index: number, type: string) => void;
  onMethodOn: (value: string, index: number, type: string) => void;
  required: boolean;
  type: string;
  handleRemoveMainImage: (imageIndex: number, controlIndex: number, type: string) => void;
}

const CurrentControl: React.FC<CurrentControlProps> = ({
  con,
  i,
  control,
  controlType,
  onImapactOn,
  onConseqText,
  onDeleteConseq,
  onConseqRequired,
  handleTaskFileChange,
  onMethodOn,
  required,
  type,
  handleRemoveMainImage
}) => {
  const hasControlError = required === false && con.current_type === '';
  const hasDescriptionError = required === false && con.value === '';
  const [isDragOver, setIsDragOver] = useState(false);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const files = Array.from(e.dataTransfer.files);
      // Filter for image files only
      const imageFiles = files.filter(file =>
        file.type.startsWith('image/') &&
        ['image/jpeg', 'image/png', 'image/jpg'].includes(file.type)
      );

      if (imageFiles.length > 0) {
        handleTaskFileChange(imageFiles, i, 'current_control');
      }
    }
  };

  return (
    <div className="relative bg-gradient-to-br from-white to-gray-50/50 border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
      {/* Header with control number and status indicator */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
              <Shield className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Control #{i + 1}</h3>
              <p className="text-sm text-gray-600">Current Control Measure</p>
            </div>
          </div>
          {con.current_type !== "No Control" && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDeleteConseq(i, 'current_control')}
              className="text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full p-3 h-10 w-10 flex items-center justify-center"
            >
              <Trash2 className="h-5 w-5" />
            </Button>
          )}
        </div>
      </div>

      {/* Main content */}
      <div className="p-6 space-y-6">
        {/* Control Selection Section */}
        <div className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Method of Control */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                <Shield className="w-4 h-4 text-blue-500" />
                <span>Method of Control</span>
                {hasControlError && <AlertCircle className="w-4 h-4 text-red-500" />}
              </Label>
              <Select
                value={con.current_type}
                onValueChange={(value) => onImapactOn(value, i, 'current_control')}
              >
                <SelectTrigger className={`h-11 ${hasControlError ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400'} transition-colors`}>
                  <SelectValue placeholder="Select control method" />
                </SelectTrigger>
                <SelectContent>
                  {control.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {hasControlError && (
                <p className="text-xs text-red-600 flex items-center space-x-1">
                  <AlertCircle className="w-3 h-3" />
                  <span>Please select a control method</span>
                </p>
              )}
            </div>

            {/* Purpose of Control */}
            {con.current_type !== "No Control" && (
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                  <CheckCircle2 className="w-4 h-4 text-green-500" />
                  <span>Purpose of Control</span>
                </Label>
                <Select
                  value={con.method}
                  onValueChange={(value) => onMethodOn(value, i, 'current_control')}
                >
                  <SelectTrigger className="h-11 border-gray-300 hover:border-gray-400 transition-colors">
                    <SelectValue placeholder="Select purpose" />
                  </SelectTrigger>
                  <SelectContent>
                    {controlType.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          {/* Description Section */}
          {con.current_type !== "No Control" && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                <FileImage className="w-4 h-4 text-purple-500" />
                <span>Control Description</span>
                {hasDescriptionError && <AlertCircle className="w-4 h-4 text-red-500" />}
              </Label>
              <Textarea
                value={con.value}
                onChange={(e) => onConseqText(e.target.value, i, 'current_control')}
                placeholder="Provide a detailed description of the control that is implemented..."
                className={`min-h-[100px] resize-none ${hasDescriptionError ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400'} transition-colors`}
                rows={4}
              />
              {hasDescriptionError && (
                <p className="text-xs text-red-600 flex items-center space-x-1">
                  <AlertCircle className="w-3 h-3" />
                  <span>Please provide a control description</span>
                </p>
              )}
            </div>
          )}
        </div>

        {/* Control Properties */}
        {con.current_type !== "No Control" && (
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center space-x-2">
              <CheckCircle2 className="w-4 h-4 text-green-500" />
              <span>Control Properties</span>
            </h4>
            <div className="flex flex-wrap gap-6">
              <div className="flex items-center space-x-3 bg-white rounded-lg px-4 py-2 border border-gray-200">
                <Checkbox
                  id={`required-${i}`}
                  checked={con.required}
                  onCheckedChange={() => onConseqRequired(con.required, i, 'current_control', 'required')}
                  className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                />
                <Label htmlFor={`required-${i}`} className="text-sm font-medium text-gray-700 cursor-pointer">
                  Required Control
                </Label>
              </div>

              <div className="flex items-center space-x-3 bg-white rounded-lg px-4 py-2 border border-gray-200">
                <Checkbox
                  id={`validity-${i}`}
                  checked={con.validity}
                  onCheckedChange={() => onConseqRequired(con.validity, i, 'current_control', 'validity')}
                  className="data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                />
                <Label htmlFor={`validity-${i}`} className="text-sm font-medium text-gray-700 cursor-pointer">
                  Valid Control
                </Label>
              </div>
            </div>
          </div>
        )}

        {/* Image Upload Section */}
        {con.current_type !== "No Control" && (
          <div className="space-y-4">
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-200">
              <Label htmlFor="imageUploads" className="text-sm font-medium text-gray-700 flex items-center space-x-2 mb-3">
                <FileImage className="w-4 h-4 text-purple-500" />
                <span>Supporting Images</span>
              </Label>
              <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                {type === 'hazard'
                  ? 'Upload images that illustrate the controls to be implemented for this Critical High Risk Activity. These images will help enhance understanding and communication of the controls to personnel involved in managing the associated risks.'
                  : 'Upload relevant images to visually represent the current controls identified here. These images will be used in other modules as appropriate.'
                }
              </p>

              <div
                className={`relative border-2 border-dashed rounded-xl p-8 transition-all duration-300 cursor-pointer group ${
                  isDragOver
                    ? 'border-purple-400 bg-purple-50 scale-[1.02]'
                    : 'border-gray-300 bg-white hover:border-purple-300 hover:bg-purple-25'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => document.getElementById(`control-file-upload-${i}`)?.click()}
              >
                <div className="flex flex-col items-center justify-center space-y-4">
                  <div className={`p-4 rounded-full transition-colors ${
                    isDragOver ? 'bg-purple-100' : 'bg-gray-100 group-hover:bg-purple-100'
                  }`}>
                    <Upload className={`h-8 w-8 transition-colors ${
                      isDragOver ? 'text-purple-600' : 'text-gray-400 group-hover:text-purple-500'
                    }`} />
                  </div>

                  <div className="text-center space-y-2">
                    <p className={`text-lg font-medium transition-colors ${
                      isDragOver ? 'text-purple-700' : 'text-gray-700'
                    }`}>
                      {isDragOver ? 'Drop files here' : 'Upload Control Images'}
                    </p>
                    <p className="text-sm text-gray-500">
                      Drag and drop files here, or click to browse
                    </p>
                    <div className="flex items-center justify-center space-x-4 text-xs text-gray-400">
                      <span>Max 5 files</span>
                      <span>•</span>
                      <span>Up to 100MB each</span>
                      <span>•</span>
                      <span>JPEG, PNG only</span>
                    </div>
                  </div>

                  <Input
                    type="file"
                    multiple
                    accept="image/jpeg,image/png,image/jpg"
                    onChange={(e) => {
                      if (e.target.files && e.target.files.length > 0) {
                        const files = Array.from(e.target.files);
                        handleTaskFileChange(files, i, 'current_control');
                        e.target.value = ''; // Reset input
                      }
                    }}
                    className="hidden"
                    id={`control-file-upload-${i}`}
                  />

                  {!isDragOver && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="border-purple-300 text-purple-600 hover:bg-purple-50 hover:border-purple-400"
                      onClick={(e) => {
                        e.stopPropagation();
                        document.getElementById(`control-file-upload-${i}`)?.click();
                      }}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Browse Files
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {/* Uploaded Images Gallery */}
            {con.files && con.files.length > 0 && (
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                  <FileImage className="w-4 h-4 text-green-500" />
                  <span>Uploaded Images ({con.files.length})</span>
                </Label>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {con.files.map((item, m) => (
                    <div key={m} className="group relative">
                      <div className="relative bg-white border border-gray-200 rounded-lg p-3 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
                        <div className="aspect-square rounded-md overflow-hidden bg-gray-50">
                          <ImageComponent fileName={item} size={'100'} name={true} />
                        </div>

                        {/* Delete button with improved styling */}
                        <Button
                          variant="destructive"
                          size="sm"
                          className="absolute -top-0 -right-0 h-8 w-8 p-0 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-red-500 hover:bg-red-600 flex items-center justify-center"
                          onClick={() => handleRemoveMainImage(m, i, 'current_control')}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>

                        {/* Image overlay on hover */}
                        {/* <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 rounded-lg" /> */}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CurrentControl;
