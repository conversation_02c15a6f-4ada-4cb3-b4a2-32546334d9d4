import PageHeader from "@/components/common/PageHeader";
import TabsContainer from "@/components/common/TabsContainer";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import ChartNavigation from "@/components/Charts/charts/ChartNavigation";
import RiskAssessmentCharts from "@/components/Charts/charts/RiskAssessmentCharts";
import PermitToWorkCharts from "@/components/Charts/charts/PermitToWorkCharts";
import OperationalTasksCharts from "@/components/Charts/charts/OperationalTasksCharts";
import IncidentInvestigationCharts from "@/components/Charts/charts/IncidentInvestigationCharts";
import InspectionCharts from "@/components/Charts/charts/InspectionCharts";
import KnowledgeCharts from "@/components/Charts/charts/KnowledgeCharts";
import { useObservationData } from "@/hooks/useObservationData";
import {
  ClipboardList,
  FileWarning,
  AlertTriangle,
  Clipboard,
  CheckSquare,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>he<PERSON>
} from 'lucide-react';

const placeholderCard = (title: string, description: string, count: number | string) => (
  <Card className="border hover:shadow-md transition-shadow">
    <CardHeader className="pb-2">
      <CardTitle className="text-lg">{title}</CardTitle>
      <CardDescription>{description}</CardDescription>
    </CardHeader>
    <CardContent>
      <div className="text-3xl font-bold text-primary">{count}</div>
    </CardContent>
  </Card>
);

const HomePage = () => {
  // Fetch observation data for the dashboard metrics
  const { observations, isLoading } = useObservationData();

  const tabs = [
    {
      value: "observation",
      label: "Observation",
      icon: <ClipboardList className="h-4 w-4" />,
      content: (
        <div className="space-y-6">
          <ChartNavigation observations={observations} />
        </div>
      )
    },
    {
      value: "risk-assessment",
      label: "Integrated Risk Assessment",
      icon: <FileWarning className="h-4 w-4" />,
      content: (
        <div className="space-y-6">
          <RiskAssessmentCharts />
        </div>
      )
    },
    {
      value: "permit-to-work",
      label: "ePermit to Work",
      icon: <FileCheck className="h-4 w-4" />,
      content: (
        <div className="space-y-6">
          <PermitToWorkCharts />
        </div>
      )
    },
    {
      value: "operational-tasks",
      label: "Operational Tasks",
      icon: <Clipboard className="h-4 w-4" />,
      content: (
        <div className="space-y-6">
          <OperationalTasksCharts />
        </div>
      )
    },
    {
      value: "incident-investigation",
      label: "Incident Investigation",
      icon: <AlertTriangle className="h-4 w-4" />,
      content: (
        <div className="space-y-6">
          <IncidentInvestigationCharts />
        </div>
      )
    },
    {
      value: "inspection",
      label: "Inspection",
      icon: <CheckSquare className="h-4 w-4" />,
      content: (
        <div className="space-y-6">
          <InspectionCharts />
        </div>
      )
    },
    {
      value: "knowledge",
      label: "Knowledge",
      icon: <BookOpen className="h-4 w-4" />,
      content: (
        <div className="space-y-6">
          <KnowledgeCharts />
        </div>
      )
    }
  ];

  return (
    <>
      <PageHeader
        title=" Dashboard"
        description="Monitor and manage all your safety initiatives in one place"
      />

      <TabsContainer tabs={tabs} />
    </>
  );
};

export default HomePage;
