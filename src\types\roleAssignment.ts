export interface Location {
  country: string;
  region: string;
  site: string;
  level: string;
}

export interface Module {
  id: string;
  name: string;
  roles: string[];
}

export interface RoleAssignment {
  id: string;
  userId: string;
  userName: string;
  location: Location;
  module: string;
  roles: string[];
  assignedAt: Date;
}

export const MODULES: Module[] = [
  {
    id: "integrated-risk-assessment",
    name: "Integrated Risk Assessment",
    roles: ["Member", "Leader", "Admin"]
  },
  {
    id: "observation-reporting",
    name: "Observation Reporting",
    roles: ["Reporter", "Action Owner", "Reviewer", "Viewer", "Admin"]
  },
  {
    id: "operational-tasks",
    name: "Operational Tasks",
    roles: ["Assignor", "Assignee", "Reviewer", "Viewer", "Admin"]
  },
  {
    id: "epermit-to-work",
    name: "ePermit to Work",
    roles: ["Applicant", "Reviewer", "Assessor", "Approver", "Acknowledger", "Admin"]
  },
  {
    id: "incident-investigation",
    name: "Incident Investigation",
    roles: ["Assignee", "Approver", "Lead Investigator", "Action Reviewer", "Core Team", "Reviewer", "Reporter", "Admin"]
  },
  {
    id: "knowledge",
    name: "Knowledge",
    roles: ["Curator", "Admin"]
  }
];

// Mock location data
export const COUNTRIES = ["United States", "United Kingdom", "Canada", "Australia", "Germany", "France", "Japan"];

export const REGIONS: Record<string, string[]> = {
  "United States": ["East Coast", "West Coast", "Midwest", "South", "Northwest"],
  "United Kingdom": ["England", "Scotland", "Wales", "Northern Ireland"],
  "Canada": ["Ontario", "Quebec", "British Columbia", "Alberta", "Manitoba"],
  "Australia": ["New South Wales", "Victoria", "Queensland", "Western Australia"],
  "Germany": ["Bavaria", "Berlin", "Hamburg", "Saxony"],
  "France": ["Île-de-France", "Provence-Alpes-Côte d'Azur", "Normandy", "Brittany"],
  "Japan": ["Kanto", "Kansai", "Chubu", "Kyushu"]
};

export const SITES: Record<string, string[]> = {
  "East Coast": ["New York", "Boston", "Philadelphia", "Miami"],
  "West Coast": ["Los Angeles", "San Francisco", "Seattle", "Portland"],
  "Midwest": ["Chicago", "Detroit", "Minneapolis", "Cleveland"],
  "South": ["Atlanta", "Dallas", "Houston", "New Orleans"],
  "Northwest": ["Portland", "Seattle", "Boise", "Spokane"],
  "England": ["London", "Manchester", "Birmingham", "Liverpool"],
  "Scotland": ["Edinburgh", "Glasgow", "Aberdeen", "Dundee"],
  "Wales": ["Cardiff", "Swansea", "Newport", "Bangor"],
  "Northern Ireland": ["Belfast", "Derry", "Lisburn", "Newry"],
  "Ontario": ["Toronto", "Ottawa", "Hamilton", "London"],
  "Quebec": ["Montreal", "Quebec City", "Laval", "Gatineau"],
  "British Columbia": ["Vancouver", "Victoria", "Kelowna", "Abbotsford"],
  "Alberta": ["Calgary", "Edmonton", "Red Deer", "Lethbridge"],
  "Manitoba": ["Winnipeg", "Brandon", "Steinbach", "Thompson"],
  "New South Wales": ["Sydney", "Newcastle", "Wollongong", "Central Coast"],
  "Victoria": ["Melbourne", "Geelong", "Ballarat", "Bendigo"],
  "Queensland": ["Brisbane", "Gold Coast", "Sunshine Coast", "Townsville"],
  "Western Australia": ["Perth", "Fremantle", "Bunbury", "Geraldton"],
  "Bavaria": ["Munich", "Nuremberg", "Augsburg", "Regensburg"],
  "Berlin": ["Mitte", "Kreuzberg", "Charlottenburg", "Neukölln"],
  "Hamburg": ["Altona", "Eimsbüttel", "Hamburg-Nord", "Wandsbek"],
  "Saxony": ["Dresden", "Leipzig", "Chemnitz", "Zwickau"],
  "Île-de-France": ["Paris", "Versailles", "Saint-Denis", "Boulogne-Billancourt"],
  "Provence-Alpes-Côte d'Azur": ["Marseille", "Nice", "Toulon", "Aix-en-Provence"],
  "Normandy": ["Rouen", "Le Havre", "Caen", "Cherbourg"],
  "Brittany": ["Rennes", "Brest", "Quimper", "Saint-Malo"],
  "Kanto": ["Tokyo", "Yokohama", "Saitama", "Chiba"],
  "Kansai": ["Osaka", "Kyoto", "Kobe", "Nara"],
  "Chubu": ["Nagoya", "Shizuoka", "Hamamatsu", "Niigata"],
  "Kyushu": ["Fukuoka", "Kitakyushu", "Kumamoto", "Kagoshima"]
};

export const LEVELS: Record<string, string[]> = {
  // United States
  "New York": ["Floor 1", "Floor 2", "Floor 3", "Basement"],
  "Boston": ["Floor 1", "Floor 2", "Floor 3", "Roof"],
  "Philadelphia": ["Floor 1", "Floor 2", "Floor 3", "Garage"],
  "Miami": ["Floor 1", "Floor 2", "Floor 3", "Pool Area"],
  "Los Angeles": ["Floor 1", "Floor 2", "Floor 3", "Parking"],
  "San Francisco": ["Floor 1", "Floor 2", "Floor 3", "Terrace"],
  "Seattle": ["Floor 1", "Floor 2", "Floor 3", "Cafeteria"],
  "Portland": ["Floor 1", "Floor 2", "Floor 3", "Garden"],
  "Chicago": ["Floor 1", "Floor 2", "Floor 3", "Lobby"],
  "Detroit": ["Floor 1", "Floor 2", "Floor 3", "Workshop"],
  "Minneapolis": ["Floor 1", "Floor 2", "Floor 3", "Conference Room"],
  "Cleveland": ["Floor 1", "Floor 2", "Floor 3", "Storage"],
  "Atlanta": ["Floor 1", "Floor 2", "Floor 3", "Reception"],
  "Dallas": ["Floor 1", "Floor 2", "Floor 3", "Meeting Room"],
  "Houston": ["Floor 1", "Floor 2", "Floor 3", "Warehouse"],
  "New Orleans": ["Floor 1", "Floor 2", "Floor 3", "Courtyard"],
  "Boise": ["Floor 1", "Floor 2", "Floor 3", "Entrance"],
  "Spokane": ["Floor 1", "Floor 2", "Floor 3", "Exit"],
  
  // United Kingdom
  "London": ["Floor 1", "Floor 2", "Floor 3", "Basement"],
  "Manchester": ["Floor 1", "Floor 2", "Floor 3", "Roof"],
  "Birmingham": ["Floor 1", "Floor 2", "Floor 3", "Garage"],
  "Liverpool": ["Floor 1", "Floor 2", "Floor 3", "Canteen"],
  "Edinburgh": ["Floor 1", "Floor 2", "Floor 3", "Parking"],
  "Glasgow": ["Floor 1", "Floor 2", "Floor 3", "Terrace"],
  "Aberdeen": ["Floor 1", "Floor 2", "Floor 3", "Cafeteria"],
  "Dundee": ["Floor 1", "Floor 2", "Floor 3", "Garden"],
  "Cardiff": ["Floor 1", "Floor 2", "Floor 3", "Lobby"],
  "Swansea": ["Floor 1", "Floor 2", "Floor 3", "Workshop"],
  "Newport": ["Floor 1", "Floor 2", "Floor 3", "Conference Room"],
  "Bangor": ["Floor 1", "Floor 2", "Floor 3", "Storage"],
  "Belfast": ["Floor 1", "Floor 2", "Floor 3", "Reception"],
  "Derry": ["Floor 1", "Floor 2", "Floor 3", "Meeting Room"],
  "Lisburn": ["Floor 1", "Floor 2", "Floor 3", "Warehouse"],
  "Newry": ["Floor 1", "Floor 2", "Floor 3", "Courtyard"],
  
  // Default for all other sites
  "default": ["Floor 1", "Floor 2", "Floor 3", "Ground Floor"]
};
