import React, { useEffect, useState, useRef } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

// Icons
import { Plus, Trash2, Upload, Save, Send, FileText, AlertTriangle, Shield, Users, Loader2 } from 'lucide-react';
import { MultiSelect } from '@/components/ui/multi-select';

// Services and Utils
import apiService from '@/services/apiService';
import { RootState } from '@/store';

// Components
import MultiFileUpload from '@/components/common/MultiFileUpload';
import ImageComponent from '@/components/common/ImageComponent';
import SignatureCanvas from 'react-signature-canvas';
import Consequence from '@/components/risk-assessment/Component/Consequence/Consequence';
import CurrentControl from '@/components/risk-assessment/Component/CurrentControl/CurrentControl';
// Mock Swal for now - will be replaced with proper toast notifications
const customSwal2 = {
  fire: (title: string, text: string, icon: string) => {
    console.log(`${icon}: ${title} - ${text}`);
    return Promise.resolve({ isConfirmed: true });
  },
  mixin: (config: any) => customSwal2
};

// TypeScript Interfaces
interface User {
  id: string;
  firstName: string;
  lastName?: string;
  email?: string;
}

interface TeamMember {
  id: string;
  name: string;
}

interface HazardItem {
  id: string;
  name: string;
  hazardName?: string;
}

interface TaskOption {
  value: string;
  files: string[];
  current_type: string;
  method: string;
  person?: string;
  date?: Date | null;
  required: boolean;
  validity: boolean;
}

interface TaskStatus {
  hazardsIdentification: string;
  consequences: string;
  currentControls: string;
  riskEstimation: string;
  additionalControls: string;
}

interface TaskItem {
  type: string;
  name?: string;
  images?: string[];
  selected?: HazardItem[];
  option?: TaskOption[];
  severity?: string;
  likelyhood?: string;
  level?: string | string[] | number[] | any;
  accept?: boolean;
  step?: number;
  value?: TaskStatus | string[] | number | any;
}

interface Recommendation {
  label: string;
  value: string;
}

interface RiskAssessmentData {
  id: string;
  departmentId: string;
  workActivityId: string;
  status: string;
  teamLeaderDeclaration: {
    name: string;
    sign: string;
  };
  raTeamMembers: TeamMember[];
  tasks: TaskItem[][];
  additonalRemarks?: string;
  overallRecommendationOne?: Recommendation;
  overallRecommendationTwo?: Recommendation;
  highRisk?: HazardItem[];
  nonRoutineDepartment?: string;
  nonRoutineWorkActivity?: string;
  hazardName?: string;
  description?: string;
  shortName?: string;
}

interface HazardAssessmentFormProps {
  data?: RiskAssessmentData | null;
  domain?: 'new' | 'edit';
}

const HazardAssessmentForm: React.FC<HazardAssessmentFormProps> = ({
  data = null,
  domain = 'new'
}) => {
  // Following test.js structure exactly
  const user = useSelector((state: RootState) => state.auth.user);
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const signRef = useRef<SignatureCanvas>(null);

  // State Management - Exact copy from test.js
  const [files, setFiles] = useState([]);
  const [depart, setDepart] = useState([]);
  const [activity, setActivity] = useState([]);
  const [crew, setCrew] = useState<TeamMember[]>([]);
  const [isLoadingCrew, setIsLoadingCrew] = useState(false);
  const [selectedDepart, setSelectedDepart] = useState(null);
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [selectedCrew, setSelectedCrew] = useState<TeamMember[]>([]);
  const [addSubActivity, setAddSubActivity] = useState(false);
  const [activityDesc, setActivityDesc] = useState('');
  const [task, setTask] = useState<TaskItem[][]>([[
    { type: 'consequence', option: [{ value: "", files: [], current_type: '', method: '', required: false, validity: false }] },
    { type: 'current_control', option: [{ value: "", files: [], current_type: '', method: '', required: false, validity: false }] }
  ]]);
  const [subActivityName, setSubActivityName] = useState('');
  const [visible, setVisible] = useState(false);
  const [item, setItem] = useState('');
  const [index, setIndex] = useState(0);
  const [activeIndex, setActiveIndex] = useState(0);
  const [hazards, setHazards] = useState([]);
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [severityTable, setSeverityTable] = useState(false);
  const [likelyhoodTable, setLikelyhoodTable] = useState(false);
  const [riskTable, setRiskTable] = useState(false);
  const [responsibility, setResponsibility] = useState([]);
  const [required, setRequired] = useState(true);
  const [recommendationOne, setRecommendationOne] = useState<Recommendation>({ label: '', value: '' });
  const [recommendationTwo, setRecommendationTwo] = useState<Recommendation>({ label: '', value: '' });
  const [nonRoutineDepartment, setNonRoutineDepartment] = useState('');
  const [nonRoutineActivity, setNonRoutineActivity] = useState('');
  const [additionalRecommendation, setAdditionalRecommendation] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [hazardName, setHazardName] = useState('');
  const [riskUpdate, setRiskUpdate] = useState(false);
  const [Update, setUpdate] = useState([]);
  const [shortName, setShortName] = useState('');
  const [draggedItemIndex, setDraggedItemIndex] = useState<number | null>(null);
  const [onEdit, setOnEdit] = useState(false);

  // API Constants - Following test.js structure
  const GET_USER_ROLE_BY_MODE = '/users/get_users';
  const FILE_URL = '/files';
  const RISKASSESSMENT_LIST = '/risk-assessments';
  const RISK_WITH_ID_URL = (id: string) => `/risk-assessments-update/${id}`;
  const DRAFT_RA = '/risk-assessments-draft';
  const RISK_UPDATE_WITH_ID_URL = (id: string) => `/risk-assessments/${id}/risk-updates`;
  const RISK_UPDATE_DRAFT_WITH_ID = (id: string) => `/risk-assessments-draft/${id}`;

  // Impact and Control Options
  const impactOn = [
    { label: 'Personnel', value: 'Personnel' },
    { label: 'Property', value: 'Property' },
    { label: 'Environment', value: 'Environment' },
    { label: 'Service Loss', value: 'Service Loss' },
  ];

  const control = [
    { label: 'Engineering', value: 'Engineering' },
    { label: 'Administrative', value: 'Administrative' },
    { label: 'PPE', value: 'PPE' }
  ];

  const controlType = [
    { label: 'Preventative', value: 'Preventative' },
    { label: 'Mitigative', value: 'Mitigative' }
  ];

  // Initialize data when in edit mode - Following test.js structure
  useEffect(() => {
    if (data && domain === 'edit') {
      setHazardName(data.hazardName || '');
      setShortName(data.shortName || '');
      setActivityDesc(data.description || '');
      setTask(data.tasks || []);
      setAdditionalRecommendation(data.additonalRemarks || '');
      setRecommendationOne(data.overallRecommendationOne || { label: '', value: '' });
      setRecommendationTwo(data.overallRecommendationTwo || { label: '', value: '' });
      setSelectedCrew(data.raTeamMembers || []);
      setNonRoutineDepartment(data.nonRoutineDepartment || '');
      setNonRoutineActivity(data.nonRoutineWorkActivity || '');
    }
  }, [data, domain]);

  // Fetch initial data - Following test.js structure
  useEffect(() => {
    const fetchData = async () => {
      try {
        if (!user) {
          return;
        }

        await Promise.all([
          getCrewList()
        ]);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: "Error",
          description: "Failed to load initial data",
          variant: "destructive",
        });
      }
    };

    fetchData();
  }, [user]);

  // Set edit mode - Following test.js structure
  useEffect(() => {
    setOnEdit(true);
  }, [domain === "edit"]);

  // Following test.js getRiskUpdate function exactly
  const getRiskUpdate = async () => {
    try {
      const response = await apiService.get(RISK_UPDATE_WITH_ID_URL(data?.id || ''));
      if (response.status === 200) {
        setUpdate(response.data);
      }
    } catch (error) {
      console.error("Error fetching risk update:", error);
    }
  };

  // Following test.js getCrewList function exactly
  const getCrewList = async () => {
    try {
      setIsLoadingCrew(true);

      const response = await apiService.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: "",
        locationTwoId: "",
        locationThreeId: "",
        locationFourId: "",
        mode: 'ra_member'
      });

      // apiService.post returns the data directly, not a response object
      let data: TeamMember[] = [];
      if (Array.isArray(response)) {
        response.forEach((item: any) => {
          if (item.id !== user?.id) {
            data.push({ name: item.firstName, id: item.id });
          }
        });
      }

      setCrew(data);

    } catch (error) {
      console.error('Error fetching crew list:', error);
      toast({
        title: "Error",
        description: "Failed to fetch team members",
        variant: "destructive",
      });
    } finally {
      setIsLoadingCrew(false);
    }
  };

  // Utility function to convert data URI to file - Following test.js structure
  const dataURItoFile = (dataURI: string, filename: string) => {
    const arr = dataURI.split(',');
    const mime = arr[0].match(/:(.*?);/)?.[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  };

  // Upload signature function - Following test.js structure
  const uploadSignature = async () => {
    const filename = new Date().getTime() + "captin_sign.png";
    const formData1 = new FormData();
    formData1.append('file', dataURItoFile(signRef.current!.getTrimmedCanvas().toDataURL("image/png"), filename));

    try {
      const response = await apiService.post(FILE_URL, formData1, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });

      if (response && response.status === 200) {
        return response.data.files[0].originalname;
      } else {
        throw new Error("File upload failed.");
      }
    } catch (error) {
      console.error("File upload error: ", error);
      throw error;
    }
  };

  // Submit function - Following test.js structure
  const submitHandler = async () => {
    setIsLoading(true);

    try {
      let uploadedSignature = '';

      if (signRef.current && !signRef.current.isEmpty()) {
        uploadedSignature = await uploadSignature();
      }

      const response = await apiService.post(RISKASSESSMENT_LIST, {
        type: 'High-Risk Hazard',
        tasks: task,
        teamLeaderDeclaration: { name: user?.firstName, sign: uploadedSignature },
        workActivityId: '',
        departmentId: '',
        teamLeaderId: user?.id,
        overallRecommendationOne: recommendationOne,
        overallRecommendationTwo: recommendationTwo,
        additonalRemarks: additionalRecommendation,
        highRisk: [],
        nonRoutineDepartment: nonRoutineDepartment,
        nonRoutineWorkActivity: nonRoutineActivity,
        status: 'Pending',
        hazardName: hazardName,
        raTeamMembersList: selectedCrew,
        description: activityDesc,
        shortName: shortName,
      });

      if (response) {
        toast({
          title: "Success",
          description: "Hazard assessment submitted successfully",
        });
        navigate('/risk-assessment');
      }
    } catch (error) {
      console.error('Error submitting assessment:', error);
      toast({
        title: "Error",
        description: "Failed to submit assessment",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };



  // Update function for edit mode - Following test.js structure
  const editUserHandler = async () => {
    setIsLoading(true);

    try {
      let uploadedSignature = '';

      if (signRef.current && !signRef.current.isEmpty()) {
        uploadedSignature = await uploadSignature();
      }

      const response = await apiService.patch(RISK_WITH_ID_URL(data?.id || ''), {
        tasks: task,
        workActivityId: '',
        shortName: shortName,
        departmentId: '',
        overallRecommendationOne: recommendationOne,
        overallRecommendationTwo: recommendationTwo,
        additonalRemarks: additionalRecommendation,
        highRisk: [],
        nonRoutineDepartment: nonRoutineDepartment,
        nonRoutineWorkActivity: nonRoutineActivity,
        hazardName: hazardName,
        raTeamMembersList: selectedCrew,
        description: activityDesc,
        status: 'Pending',
        teamLeaderDeclaration: {
          name: user?.firstName,
          sign: uploadedSignature || ''
        }
      });

      if (response) {
        toast({
          title: "Success",
          description: "Assessment updated successfully",
        });
        navigate('/risk-assessment');
      }
    } catch (error) {
      console.error('Error updating assessment:', error);
      toast({
        title: "Error",
        description: "Failed to update assessment",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Drag and drop functions - Following test.js structure
  const handleDragStart = (event: React.DragEvent, index: number) => {
    setDraggedItemIndex(index);
    event.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (event: React.DragEvent, dropIndex: number) => {
    event.preventDefault();

    if (draggedItemIndex === null) return;

    const newTask = [...task];
    const draggedItem = newTask[draggedItemIndex];

    newTask.splice(draggedItemIndex, 1);
    newTask.splice(dropIndex, 0, draggedItem);

    setTask(newTask);
    setDraggedItemIndex(null);
  };

  // Task management functions - Following test.js structure
  const addNewTask = () => {
    const newTaskItem: TaskItem[] = [
      { type: 'consequence', option: [{ value: "", files: [], current_type: '', method: '', required: false, validity: false }] },
      { type: 'current_control', option: [{ value: "", files: [], current_type: '', method: '', required: false, validity: false }] }
    ];
    setTask([...task, newTaskItem]);
  };

  const removeTask = (index: number) => {
    if (task.length > 1) {
      const newTask = task.filter((_, i) => i !== index);
      setTask(newTask);
    }
  };

  // Submit handler based on domain - Following test.js structure
  const onSubmitUpdate = () => {
    if (domain === 'edit') {
      editUserHandler();
    } else {
      submitHandler();
    }
  };

  // Handler functions for components - Following test.js structure
  const onImapactOn = (value: string, index: number, type: string) => {
    const newTask = [...task];
    if (type === 'consequence') {
      newTask[0][0].option![index].current_type = value;
    } else if (type === 'current_control') {
      newTask[0][1].option![index].current_type = value;
    }
    setTask(newTask);
  };

  const onConseqText = (value: string, index: number, type: string) => {
    const newTask = [...task];
    if (type === 'consequence') {
      newTask[0][0].option![index].value = value;
    } else if (type === 'current_control') {
      newTask[0][1].option![index].value = value;
    }
    setTask(newTask);
  };

  const onDeleteConseq = (index: number, type: string) => {
    const newTask = [...task];
    if (type === 'consequence') {
      newTask[0][0].option!.splice(index, 1);
    } else if (type === 'current_control') {
      newTask[0][1].option!.splice(index, 1);
    }
    setTask(newTask);
  };

  const onConseqRequired = (currentValue: boolean, index: number, type: string, field: string) => {
    const newTask = [...task];
    if (type === 'current_control') {
      if (field === 'required') {
        newTask[0][1].option![index].required = !currentValue;
      } else if (field === 'validity') {
        newTask[0][1].option![index].validity = !currentValue;
      }
    }
    setTask(newTask);
  };

  const onMethodOn = (value: string, index: number, type: string) => {
    const newTask = [...task];
    if (type === 'current_control') {
      newTask[0][1].option![index].method = value;
    }
    setTask(newTask);
  };

  const handleTaskFileChange = (files: File[], index: number, type: string) => {
    // Handle file upload logic here
    console.log('File upload:', files, index, type);
  };

  const handleRemoveImage = (imageIndex: number, consequenceIndex: number, type: string) => {
    const newTask = [...task];
    if (type === 'consequence') {
      newTask[0][0].option![consequenceIndex].files.splice(imageIndex, 1);
    } else if (type === 'current_control') {
      newTask[0][1].option![consequenceIndex].files.splice(imageIndex, 1);
    }
    setTask(newTask);
  };

  const addConsequence = (type: string) => {
    const newTask = [...task];
    if (type === 'consequence') {
      newTask[0][0].option!.push({ value: "", files: [], current_type: '', method: '', required: false, validity: false });
    } else if (type === 'current_control') {
      newTask[0][1].option!.push({ value: "", files: [], current_type: '', method: '', required: false, validity: false });
    }
    setTask(newTask);
  };

  // Initialize task structure if empty
  useEffect(() => {
    if (task.length === 0) {
      setTask([[
        { type: 'consequence', option: [{ value: "", files: [], current_type: '', method: '', required: false, validity: false }] },
        { type: 'current_control', option: [{ value: "", files: [], current_type: '', method: '', required: false, validity: false }] }
      ]]);
    }
  }, [task.length]);

  // Following test.js createUserHandler function exactly
  const createUserHandler = async () => {
    setIsLoading(true);

    if (signRef.current?.isEmpty()) {
      customSwal2.fire("Please Sign!", "", "error");
      setIsLoading(false);
      return;
    }

    let uploadedSignature = '';
    const filename = new Date().getTime() + "captin_sign.png";

    const formData1 = new FormData();
    formData1.append('file', dataURItoFile(signRef.current!.getTrimmedCanvas().toDataURL("image/png"), filename));

    try {
      const response = await apiService.post(FILE_URL, formData1, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });

      if (response && response.status === 200) {
        uploadedSignature = response.data.files[0].originalname;
      } else {
        customSwal2.fire("File Upload Failed!", "", "error");
        setIsLoading(false);
        return;
      }
    } catch (error) {
      console.error("File upload error: ", error);
      setIsLoading(false);
      customSwal2.fire("Please Try Again!", "", "error");
      return;
    }

    try {
      const response1 = await apiService.post(RISKASSESSMENT_LIST, {
        type: 'High-Risk Hazard',
        tasks: task,
        teamLeaderDeclaration: { name: user?.firstName, sign: uploadedSignature },
        workActivityId: selectedActivity?.id,
        departmentId: selectedDepart?.id,
        teamLeaderId: user?.id,
        overallRecommendationOne: recommendationOne,
        overallRecommendationTwo: recommendationTwo,
        additonalRemarks: additionalRecommendation,
        highRisk: [],
        nonRoutineDepartment: nonRoutineDepartment,
        nonRoutineWorkActivity: nonRoutineActivity,
        status: 'Pending',
        hazardName: hazardName,
        raTeamMembersList: selectedCrew,
        description: activityDesc,
        shortName: shortName,
      });

      if (response1.status === 200) {
        setIsLoading(false);
        customSwal2.fire("Risk Assessment Created!", "", "success").then((result) => {
          if (result.isConfirmed) {
            window.location.reload();
          }
        });
      } else {
        customSwal2.fire("Please Try Again!", "", "error");
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Risk assessment creation error: ", error);
      customSwal2.fire("Please Try Again!", "", "error");
      setIsLoading(false);
    }
  };

  // Following test.js draftUserHandler function exactly
  const draftUserHandler = async () => {
    setIsLoading(true);

    try {
      let uploadedSignature = '';

      if (!signRef.current?.isEmpty()) {
        uploadedSignature = await uploadSignature();
      } else {
        uploadedSignature = "";
      }

      const draftSubmitted = await submitDraft(uploadedSignature);

      if (draftSubmitted) {
        setIsLoading(false);
        customSwal2.fire("Risk Assessment Drafted!", "", "success").then((result) => {
          if (result.isConfirmed) {
            window.location.reload();
          }
        });
      }
    } catch (error) {
      customSwal2.fire("Please Try Again!", "", "error");
      setIsLoading(false);
    }
  };

  // Following test.js submitDraft function exactly
  const submitDraft = async (uploadedSignature: string) => {
    try {
      const response = await apiService.post(DRAFT_RA, {
        type: 'High-Risk Hazard',
        tasks: task,
        teamLeaderDeclaration: { name: user?.firstName, sign: uploadedSignature },
        workActivityId: selectedActivity?.id,
        departmentId: selectedDepart?.id,
        teamLeaderId: user?.id,
        overallRecommendationOne: recommendationOne,
        overallRecommendationTwo: recommendationTwo,
        additonalRemarks: additionalRecommendation,
        highRisk: [],
        nonRoutineDepartment: nonRoutineDepartment,
        nonRoutineWorkActivity: nonRoutineActivity,
        status: 'Draft',
        hazardName: hazardName,
        raTeamMembersList: selectedCrew,
        description: activityDesc
      });

      if (response.status === 200) {
        return true;
      } else {
        throw new Error("Draft submission failed.");
      }
    } catch (error) {
      console.error("Draft submission error: ", error);
      throw error;
    }
  };

  // Following test.js draftUserEditHandler function exactly
  const draftUserEditHandler = async () => {
    setIsLoading(true);

    try {
      let uploadedSignature = '';

      if (data?.teamLeaderDeclaration?.sign) {
        uploadedSignature = data.teamLeaderDeclaration.sign;
      } else {
        if (!signRef.current?.isEmpty()) {
          uploadedSignature = await uploadSignature();
        }
      }

      const response1 = await apiService.patch(RISK_UPDATE_DRAFT_WITH_ID(data?.id || ''), {
        tasks: task,
        status: 'Draft',
        hazardName: hazardName,
        raTeamMembersList: selectedCrew,
        description: activityDesc,
        teamLeaderDeclaration: {
          name: user?.firstName,
          sign: uploadedSignature || ''
        }
      });

      if (response1.status === 204) {
        setIsLoading(false);
        customSwal2.fire("Risk Assessment Drafted!", "", "success").then((result) => {
          if (result.isConfirmed) {
            window.location.reload();
          }
        });
      } else {
        customSwal2.fire("Please Try Again!", "", "error");
        setIsLoading(false);
      }

    } catch (error) {
      console.error("Error: ", error);
      customSwal2.fire("Please Try Again!", "", "error");
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="max-w-6xl mx-auto p-6">
        <Card className="shadow-xl border-0 bg-white/95 backdrop-blur-sm">
          {/* Header Section */}
          <CardHeader className="bg-gradient-to-r from-red-500 to-red-600 text-white rounded-t-lg">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                <AlertTriangle className="w-6 h-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold">High-Risk Hazard Assessment</CardTitle>
                <p className="text-red-100 mt-1">Systematic Hazard Identification & Mandatory Controls</p>
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            {/* Introduction Section */}
            <div className="p-6 bg-gradient-to-r from-red-50 to-orange-50 border-b border-red-100">
              <div className="space-y-4">
                <p className="text-gray-700 leading-relaxed">
                  Systematically identify hazards and define mandatory controls for high-risk scenarios (e.g., confined spaces, chemical handling) that may occur during routine or non-routine work.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                  <Card className="border-red-200 bg-white/80">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-red-600 font-bold text-sm">1</span>
                        </div>
                        <div>
                          <h6 className="font-semibold text-gray-800 mb-2">Evaluate Generic Hazards</h6>
                          <p className="text-sm text-gray-600">Identify risks tied to the scenario itself (e.g., fall hazards for "Working at Heights"), regardless of the specific activity.</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-red-200 bg-white/80">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-red-600 font-bold text-sm">2</span>
                        </div>
                        <div>
                          <h6 className="font-semibold text-gray-800 mb-2">Define Mandatory Controls</h6>
                          <p className="text-sm text-gray-600">Document safeguards (e.g., harness systems, gas detectors) that enforce organizational policies and comply with regulatory mandates.</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-red-200 bg-white/80">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-red-600 font-bold text-sm">3</span>
                        </div>
                        <div>
                          <h6 className="font-semibold text-gray-800 mb-2">Link to Work Permits</h6>
                          <p className="text-sm text-gray-600">These controls are automatically added as required checkpoints in permits for any work involving the scenario.</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card className="border-amber-200 bg-amber-50/50 mt-6">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-white text-xs font-bold">!</span>
                      </div>
                      <div>
                        <h6 className="font-semibold text-amber-800 mb-2">Example</h6>
                        <p className="text-amber-700 text-sm mb-2">For <strong>"Hot Work"</strong>, controls like:</p>
                        <ul className="text-amber-700 text-sm space-y-1 ml-4">
                          <li>• Fire extinguishers on-site</li>
                          <li>• Clearance of flammable materials</li>
                        </ul>
                        <p className="text-amber-700 text-sm mt-2">become mandatory fields in permits.</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Activity Name Section */}
            <div className="p-6 border-b border-gray-200">
              <div className="space-y-4">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                    <FileText className="w-4 h-4 text-red-600" />
                  </div>
                  <h5 className="text-lg font-semibold text-gray-800">Activity Information</h5>
                </div>

                <div className="max-w-2xl">
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">
                    Critical High Risk Activity Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    placeholder="Enter the critical high risk activity name"
                    value={hazardName}
                    onChange={(e) => setHazardName(e.target.value)}
                    className="w-full border-gray-300 focus:border-red-500 focus:ring-red-500"
                  />
                </div>
              </div>
            </div>

            {/* Team Member Selection Section */}
            <Card className="mx-6 mb-6 shadow-md border-0">
              <CardHeader className="bg-gray-50 border-b">
                <CardTitle className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                  <div className="w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Users className="w-4 h-4 text-purple-600" />
                  </div>
                  Risk Assessment Team Members
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-6">
                  <div>
                    <Label htmlFor="team-members" className="text-sm font-medium text-gray-700 mb-2 block">
                      Identify the qualified RA Team Members to include in this Risk Assessment using the drop-down selector; only qualified members will be listed. If a required member is not listed, please contact the Administrator to have them added. Once you've made your selections, click the Send Notification button to notify them via email about their inclusion in the team.
                    </Label>

                    <MultiSelect
                      options={crew.map(member => ({
                        label: member.name,
                        value: member.id
                      }))}
                      selected={selectedCrew
                        .filter(selected => crew.find(c => c.id === selected.id))
                        .map(member => member.id)
                      }
                      onChange={(values) => {
                        console.log('MultiSelect onChange:', values); // Debug log
                        // Only allow selection from current crew members
                        const selectedMembers = crew.filter(member => values.includes(member.id));
                        setSelectedCrew(selectedMembers);
                      }}
                      placeholder={isLoadingCrew ? "Loading members..." : crew.length === 0 ? "No members available" : "Choose Members..."}
                      className="mt-2"
                      disabled={isLoadingCrew}
                    />
                    {isLoadingCrew && (
                      <div className="mt-2 text-sm text-gray-500 flex items-center gap-2">
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Loading team members...
                      </div>
                    )}
                  </div>

                  <div>
                    <Button
                      variant="outline"
                      className="w-fit flex items-center gap-2"
                    >
                      <Send className="w-4 h-4" />
                      Send Notification
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Description Section */}
            <div className="p-6 border-b border-gray-200">
              <div className="space-y-4">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <FileText className="w-4 h-4 text-green-600" />
                  </div>
                  <h5 className="text-lg font-semibold text-gray-800">Activity Description</h5>
                </div>

                <div className="max-w-4xl">
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">
                    Provide additional information about the high risk activity to clarify scope
                    <span className="text-gray-500 ml-1">(Optional)</span>
                  </Label>
                  <Textarea
                    rows={4}
                    value={activityDesc}
                    onChange={(e) => setActivityDesc(e.target.value)}
                    className="resize-none border-gray-300 focus:border-green-500 focus:ring-green-500"
                    placeholder="Describe the scope, context, and specific details of this high-risk activity..."
                  />
                </div>
              </div>
            </div>

            {/* Risk Assessment Section */}
            <div className="p-6 border-b border-gray-200">
              <div className="space-y-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                    <AlertTriangle className="w-4 h-4 text-orange-600" />
                  </div>
                  <h5 className="text-lg font-semibold text-gray-800">Risk Assessment</h5>
                </div>

                {task.map((item, i) => {
                  return (
                    <div key={i} className="space-y-8">
                      {/* Consequences Section */}
                      <Card className="border-orange-200 bg-orange-50/30">
                        <CardHeader className="bg-gradient-to-r from-orange-100 to-red-100 border-b border-orange-200">
                          <CardTitle className="flex items-center gap-3 text-lg">
                            <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                              <AlertTriangle className="w-4 h-4 text-white" />
                            </div>
                            Consequences
                          </CardTitle>
                          <p className="text-gray-700 mt-2 leading-relaxed">
                            Identify the potential consequences of this Critical High Risk Activity on Personnel, Environment,
                            Equipment / Property & Service due to the associated high-risk factors.
                          </p>
                        </CardHeader>
                        <CardContent className="p-6 space-y-4">
                          {item[0].option && item[0].option.map((con, idx) => {
                            return (
                              <Consequence
                                key={idx}
                                con={con}
                                i={idx}
                                impactOn={impactOn}
                                onImapactOn={onImapactOn}
                                onConseqText={onConseqText}
                                onDeleteConseq={onDeleteConseq}
                                handleTaskFileChange={handleTaskFileChange}
                                required={required}
                                type={'hazard'}
                                handleRemoveImage={handleRemoveImage}
                              />
                            )
                          })}

                          <Button
                            variant="outline"
                            onClick={() => addConsequence('consequence')}
                            className="mt-4 bg-orange-50 border-orange-300 text-orange-700 hover:bg-orange-100"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Add Consequence
                          </Button>
                        </CardContent>
                      </Card>

                      {/* Necessary Controls Section */}
                      <Card className="border-blue-200 bg-blue-50/30">
                        <CardHeader className="bg-gradient-to-r from-blue-100 to-indigo-100 border-b border-blue-200">
                          <CardTitle className="flex items-center gap-3 text-lg">
                            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                              <Shield className="w-4 h-4 text-white" />
                            </div>
                            Necessary Controls
                          </CardTitle>
                          <p className="text-gray-700 mt-2 leading-relaxed">
                            Identify the necessary controls to manage the hazards, associated risks, and potential consequences of this Critical High Risk Activity.
                            These controls should be specific to this activity and aimed at minimizing or eliminating the identified risks to ensure safe execution.
                            These controls will also reflect in the Permit to Work Applications when the work involves these High Risk Activities.
                          </p>
                        </CardHeader>
                        <CardContent className="p-6 space-y-4">
                          {item[1].option && item[1].option.map((con, idx) => {
                            return (
                              <CurrentControl
                                key={idx}
                                con={con}
                                i={idx}
                                control={control}
                                controlType={controlType}
                                onImapactOn={onImapactOn}
                                onConseqText={onConseqText}
                                onDeleteConseq={onDeleteConseq}
                                onConseqRequired={onConseqRequired}
                                handleTaskFileChange={handleTaskFileChange}
                                required={required}
                                type={'hazard'}
                                handleRemoveMainImage={handleRemoveImage}
                                onMethodOn={onMethodOn}
                              />
                            )
                          })}

                          <Button
                            variant="outline"
                            onClick={() => addConsequence('current_control')}
                            className="mt-4 bg-blue-50 border-blue-300 text-blue-700 hover:bg-blue-100"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Add Necessary Control
                          </Button>
                        </CardContent>
                      </Card>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Declaration Section */}
            <div className="mx-6 mb-6">
              <div className="bg-white border border-gray-200 rounded-lg shadow-md overflow-hidden">
                {/* Header Section */}
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-200 p-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h5 className="text-xl font-bold text-gray-800 mb-1">Team Leader Declaration</h5>
                      <p className="text-sm text-green-600">Risk Assessment Leadership Acknowledgment</p>
                    </div>
                  </div>
                </div>

                {/* Declaration Text */}
                <div className="p-6 bg-green-50 border-b border-green-100">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="text-green-800 leading-relaxed">
                      <strong>Leadership Declaration:</strong>
                      <p className="mt-2">
                        As the Team Leader for this exercise, I confirm my role in identifying the potential consequences of this{' '}
                        <strong>Critical High Risk Activity</strong> and in outlining the necessary controls. Our team has used its
                        professional judgment to determine that these controls are essential to ensuring safety during this type of work.
                        The results are based on the team's collective expertise and consensus, drawing on our full capabilities.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Signature Section */}
                <div className="p-6">
                  <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-8 border border-gray-200">
                    <div className="text-center mb-6">
                      <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">Team Leader Signature</h3>
                      <p className="text-sm text-gray-600">Digital signature confirms leadership accountability</p>
                    </div>

                    {domain === 'edit' ? (
                      data && data.teamLeaderDeclaration && data.teamLeaderDeclaration?.sign ? (
                        <div className="flex flex-col items-center space-y-4">
                          <div className="bg-white border-2 border-green-200 rounded-xl p-4 w-100 shadow-lg">
                            <ImageComponent fileName={data.teamLeaderDeclaration.sign} size={'100'} name={false} />
                          </div>
                          <div className="text-center">
                            <div className="flex items-center justify-center gap-2 mb-2">
                              <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <span className="text-sm font-medium text-green-700">Digitally Signed</span>
                            </div>
                            <p className="text-lg font-semibold text-gray-800">
                              {data.teamLeaderDeclaration.name}
                            </p>
                            <p className="text-sm text-gray-500">Team Leader</p>
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center space-y-4">
                          <div className="relative bg-white rounded-xl border-2 border-dashed border-gray-300 hover:border-green-400 transition-colors">
                            <SignatureCanvas
                              penColor="#059669"
                              canvasProps={{
                                width: 450,
                                height: 120,
                                className: "rounded-xl",
                              }}
                              ref={signRef}
                            />
                            <Button
                              variant="outline"
                              size="sm"
                              className="absolute top-2 right-2 bg-white/90 hover:bg-white border-gray-300"
                              onClick={() => signRef.current?.clear()}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                              <p className="text-gray-400 text-sm">Sign here</p>
                            </div>
                          </div>
                          <div className="text-center">
                            <p className="text-lg font-semibold text-gray-800">
                              {user?.firstName}
                            </p>
                            <p className="text-sm text-gray-500">Team Leader</p>
                          </div>
                        </div>
                      )
                    ) : (
                      <div className="flex flex-col items-center space-y-4">
                        <div className="relative bg-white rounded-xl border-2 border-dashed border-gray-300 hover:border-green-400 transition-colors">
                          <SignatureCanvas
                            penColor="#059669"
                            canvasProps={{
                              width: 450,
                              height: 120,
                              className: "rounded-xl",
                            }}
                            ref={signRef}
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            className="absolute top-2 right-2 bg-white/90 hover:bg-white border-gray-300"
                            onClick={() => signRef.current?.clear()}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                            <p className="text-gray-400 text-sm">Sign here</p>
                          </div>
                        </div>
                        <div className="text-center">
                          <p className="text-lg font-semibold text-gray-800">
                            {user?.firstName}
                          </p>
                          <p className="text-sm text-gray-500">Team Leader</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons Section */}
            <div className="p-6 bg-gray-50 border-t border-gray-200">
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button
                  variant="outline"
                  size="lg"
                  onClick={(e) => {
                    e.preventDefault();
                    if (domain === 'edit') {
                      draftUserEditHandler();
                    } else {
                      draftUserHandler();
                    }
                  }}
                  disabled={isLoading}
                  className="w-full sm:w-auto bg-white border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-3"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Save as Draft
                    </>
                  )}
                </Button>

                <Button
                  size="lg"
                  onClick={(e) => {
                    e.preventDefault();
                    if (domain === 'edit') {
                      if (data?.status === 'Draft') {
                        editUserHandler();
                      } else {
                        setRiskUpdate(true);
                      }
                    } else {
                      createUserHandler();
                    }
                  }}
                  disabled={isLoading}
                  className="w-full sm:w-auto bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-8 py-3"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4 mr-2" />
                      Release Draft for Affirmation
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="p-6">
            <div className="flex items-center gap-3">
              <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
              <span className="text-gray-700">Processing your request...</span>
            </div>
          </Card>
        </div>
      )}

      {/* Debug and Modals */}
      {riskUpdate && <div>Risk Update Modal</div>}
      {Update.length !== 0 && <div>Update Table</div>}
    </div>
  );
};

export default HazardAssessmentForm;
