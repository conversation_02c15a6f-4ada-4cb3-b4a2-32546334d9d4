import React, { useState, useEffect } from 'react';
import { ConsequenceItem, IMPACT_OPTIONS } from '@/types/consequence';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { X, Plus, Image, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { v4 as uuidv4 } from 'uuid';

interface ConsequencesPanelProps {
  consequences: ConsequenceItem[];
  onConsequencesChange: (consequences: ConsequenceItem[]) => void;
  onComplete: () => void;
  isCompleted: boolean;
  isFinalized: boolean;
}

const ConsequencesPanel: React.FC<ConsequencesPanelProps> = ({
  consequences,
  onConsequencesChange,
  onComplete,
  isCompleted,
  isFinalized
}) => {
  const [localConsequences, setLocalConsequences] = useState<ConsequenceItem[]>(consequences);
  const [previewUrls, setPreviewUrls] = useState<Record<string, string>>({});

  // Update local state when prop changes
  useEffect(() => {
    setLocalConsequences(consequences);
  }, [consequences]);

  // Generate preview URLs for attachments
  useEffect(() => {
    const urls: Record<string, string> = {};
    
    localConsequences.forEach(consequence => {
      if (consequence.attachment && !consequence.attachmentUrl) {
        const url = URL.createObjectURL(consequence.attachment);
        urls[consequence.id] = url;
      }
    });
    
    setPreviewUrls(prev => ({ ...prev, ...urls }));
    
    // Cleanup function to revoke object URLs
    return () => {
      Object.values(urls).forEach(URL.revokeObjectURL);
    };
  }, [localConsequences]);

  // Add a new consequence
  const handleAddConsequence = (e: React.MouseEvent) => {
    e.preventDefault();
    
    const newConsequence: ConsequenceItem = {
      id: uuidv4(),
      impactOn: '',
      description: '',
      attachment: null
    };
    
    setLocalConsequences([...localConsequences, newConsequence]);
  };

  // Update a consequence field
  const handleConsequenceChange = (id: string, field: keyof ConsequenceItem, value: any) => {
    setLocalConsequences(prev => 
      prev.map(item => 
        item.id === id ? { ...item, [field]: value } : item
      )
    );
  };

  // Handle file upload
  const handleFileChange = (id: string, e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      
      // Only accept image files
      if (file.type.startsWith('image/')) {
        handleConsequenceChange(id, 'attachment', file);
      }
    }
  };

  // Remove a consequence
  const handleRemoveConsequence = (id: string) => {
    setLocalConsequences(prev => prev.filter(item => item.id !== id));
  };

  // Save all consequences
  const handleSave = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    onConsequencesChange(localConsequences);
    
    if (localConsequences.length > 0 && 
        localConsequences.every(item => item.impactOn && item.description)) {
      onComplete();
    }
  };

  // Check if a consequence is valid
  const isConsequenceValid = (consequence: ConsequenceItem) => {
    return consequence.impactOn.trim() !== '' && consequence.description.trim() !== '';
  };

  // Check if all consequences are valid
  const areAllConsequencesValid = () => {
    return localConsequences.length > 0 && 
           localConsequences.every(isConsequenceValid);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="font-medium">Consequences</h4>
        {!isFinalized && (
          <Button 
            type="button" 
            onClick={handleAddConsequence}
            size="sm"
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            Add Consequence
          </Button>
        )}
      </div>

      {localConsequences.length === 0 ? (
        <div className="text-center p-6 border border-dashed rounded-md">
          <p className="text-muted-foreground">No consequences added yet. Click "Add Consequence" to begin.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {localConsequences.map((consequence, index) => (
            <Card key={consequence.id} className="relative">
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={`impact-${consequence.id}`} className="mb-2 block">
                      Impact on:
                    </Label>
                    <Select
                      disabled={isFinalized}
                      value={consequence.impactOn}
                      onValueChange={(value) => handleConsequenceChange(consequence.id, 'impactOn', value)}
                    >
                      <SelectTrigger id={`impact-${consequence.id}`} className="w-full">
                        <SelectValue placeholder="Select impact area" />
                      </SelectTrigger>
                      <SelectContent>
                        {IMPACT_OPTIONS.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="md:col-span-2">
                    <Label htmlFor={`description-${consequence.id}`} className="mb-2 block">
                      Description:
                    </Label>
                    <Textarea
                      id={`description-${consequence.id}`}
                      value={consequence.description}
                      onChange={(e) => handleConsequenceChange(consequence.id, 'description', e.target.value)}
                      placeholder="Enter a detailed description of the consequence"
                      className="min-h-[80px]"
                      disabled={isFinalized}
                    />
                  </div>

                  <div className="md:col-span-2">
                    <Label htmlFor={`attachment-${consequence.id}`} className="mb-2 block">
                      Attachment (Image only):
                    </Label>
                    <div className="flex flex-col gap-2">
                      <Input
                        id={`attachment-${consequence.id}`}
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleFileChange(consequence.id, e)}
                        className="flex-1"
                        disabled={isFinalized}
                      />
                      
                      {(consequence.attachment || consequence.attachmentUrl) && (
                        <div className="mt-2">
                          <div className="h-32 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
                            <img
                              src={consequence.attachmentUrl || previewUrls[consequence.id]}
                              alt="Consequence attachment"
                              className="max-h-full max-w-full object-contain"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {!isFinalized && (
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={() => handleRemoveConsequence(consequence.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Save button and status */}
      <div className="flex justify-between items-center mt-4">
        <div>
          {isCompleted && (
            <Badge variant="outline" className={isFinalized ? "bg-green-100 text-green-800" : "bg-orange-100 text-orange-800"}>
              {isFinalized ? "Finalized" : "Amend"}
            </Badge>
          )}
        </div>
        {!isFinalized && (
          <Button
            type="button"
            onClick={(e) => handleSave(e)}
            disabled={!areAllConsequencesValid()}
          >
            Save
          </Button>
        )}
      </div>
    </div>
  );
};

export default ConsequencesPanel;
