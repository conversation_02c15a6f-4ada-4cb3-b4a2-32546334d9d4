import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import PageHeader from '@/components/common/PageHeader';
import TabsContainer from '@/components/common/TabsContainer';
import ExpandableDataTable from '@/components/common/ExpandableDataTable';
import { Badge } from '@/components/ui/badge';
import { format, parseISO, differenceInDays } from 'date-fns';
import { Input } from '@/components/ui/input';
import {
  Search,
  Plus,
  Download,
  Loader2,
  User
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import RecordObservationModal, { ObservationSubmitData } from '@/components/observations/RecordObservationModal';
import { PDFService } from '@/services/pdfService';
import ObservationDetailsModal from '@/components/observations/ObservationDetailsModal';
import ActionsModal from '@/components/observations/ActionsModal';
import { MyAction, Observation } from '@/types/observation';
import { fetchObservations, ObservationResponse, fetchObservationActions, ObservationAction } from '@/services/api';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import apiService from '@/services/apiService';

// API Base URL
const API_BASE_URL = 'https://client-api.acuizen.com';

// Empty array for initial state
const mockMyActions: MyAction[] = [];

// Function to format action type for display
const getActionTypeDisplay = (actionType: string): string => {
  switch (actionType) {
    case 'take_action':
      return 'Take Action';
    case 'verify_action':
      return 'Verify Action';
    case 'review':
      return 'Review Action';
    case 'reperform_action':
      return 'Re-perform Action';
    default:
      return actionType || 'Unknown';
  }
};

// Function to determine user's role in an action
const getUserRole = (action: ObservationAction, currentUserId: string): string => {
  // Check if user is assigned to this action
  if (action.assignedToId && action.assignedToId.includes(currentUserId)) {
    switch (action.actionType) {
      case 'take_action':
        return 'Action Owner';
      case 'verify_action':
        return 'Reviewer';
      case 'review':
        return 'Reviewer';
      case 'reperform_action':
        return 'Action Owner';
      default:
        return 'Assignee';
    }
  }

  // Check if user submitted the action
  if (action.submittedById === currentUserId) {
    return 'Submitter';
  }

  return 'Viewer';
};

// Function to calculate timeline status
const getTimelineStatus = (dueDate: string | undefined): string => {
  if (!dueDate) return 'No Due Date';

  try {
    const due = new Date(dueDate);
    const today = new Date();

    // Reset time to compare only dates
    today.setHours(0, 0, 0, 0);
    due.setHours(0, 0, 0, 0);

    const diffInDays = differenceInDays(due, today);

    if (diffInDays < 0) {
      return 'Overdue';
    } else if (diffInDays === 0) {
      return 'Due Now';
    } else {
      return 'Upcoming';
    }
  } catch (error) {
    console.error('Error calculating timeline status:', error);
    return 'Invalid Date';
  }
};

// Function to sort alphanumeric Action IDs properly
const sortActionIds = (a: string, b: string): number => {
  // Handle null/undefined values
  if (!a && !b) return 0;
  if (!a) return 1;
  if (!b) return -1;

  // Convert to strings and handle case sensitivity
  const aStr = String(a).toLowerCase();
  const bStr = String(b).toLowerCase();

  // Try to extract numeric parts for better sorting
  const aMatch = aStr.match(/^([a-z]*-?)(\d+)(.*)$/);
  const bMatch = bStr.match(/^([a-z]*-?)(\d+)(.*)$/);

  if (aMatch && bMatch) {
    // Both have numeric parts
    const [, aPrefix, aNum, aSuffix] = aMatch;
    const [, bPrefix, bNum, bSuffix] = bMatch;

    // Compare prefixes first
    if (aPrefix !== bPrefix) {
      return aPrefix.localeCompare(bPrefix);
    }

    // Compare numeric parts
    const aNumber = parseInt(aNum, 10);
    const bNumber = parseInt(bNum, 10);
    if (aNumber !== bNumber) {
      return aNumber - bNumber;
    }

    // Compare suffixes
    return aSuffix.localeCompare(bSuffix);
  }

  // Fallback to string comparison
  return aStr.localeCompare(bStr);
};

const ObservationPage = () => {
  const { toast } = useToast();
  const [myActionsSearch, setMyActionsSearch] = useState('');
  const [allObservationsSearch, setAllObservationsSearch] = useState('');
  // Track active tab for potential future use
  const [, setActiveTab] = useState("my-actions");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [myActions, setMyActions] = useState(mockMyActions);
  const [allObservations, setAllObservations] = useState<Observation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [incidentCount, setIncidentCount] = useState(0);

  // New state variables for observation actions
  const [observationActions, setObservationActions] = useState<ObservationAction[]>([]);
  const [loadingActions, setLoadingActions] = useState(false);
  const [actionCount, setActionCount] = useState(0);

  const { accessToken, user } = useSelector((state: RootState) => state.auth);

  // Debug: Log user roles for verification
  useEffect(() => {
    if (user && (user as any)?.roles) {
      console.log('User roles in ObservationPage:', (user as any).roles);
      console.log('Has obsreporter role:', (user as any).roles.some((role: any) => ['obsreporter'].includes(role.maskId)));
    }
  }, [user]);

  // Function to format location from API response
  const formatLocation = useCallback((observation: ObservationResponse): string => {
    if (observation.isCustomLocation && observation.customLocation) {
      return observation.customLocation;
    } else {
      const locations = [
        observation.locationOne?.name,
        observation.locationTwo?.name,
        observation.locationThree?.name,
        observation.locationFour?.name,
        observation.locationFive?.name,
        observation.locationSix?.name
      ].filter(Boolean);

      return locations.join(' > ');
    }
  }, []);

  // Function to convert API response to Observation format
  const mapApiResponseToObservation = useCallback((response: ObservationResponse): Observation => {
    return {
      id: response.id,
      maskId: response.maskId,
      location: formatLocation(response),
      fullLocation: {
        country: response.locationOne?.name || '',
        region: response.locationTwo?.name || '',
        site: response.locationThree?.name || '',
        level: response.locationFour?.name || ''
      },
      category: response.observationCategory,
      type: response.observationType,
      observationType: response.observationType,
      actionCondition: response.observationActOrCondition || '',
      reportedBy: response.reporter ? `${response.reporter.firstName} ${response.reporter.lastName || ''}`.trim() : 'Unknown',
      reportedDate: parseISO(response.created),
      actionAssignee: (() => {
        // Handle single action owner
        if (response.actionOwner) {
          return `${response.actionOwner.firstName} ${response.actionOwner.lastName || ''}`.trim();
        }
        // Handle multiple action owners
        if (response.multiActionOwners && response.multiActionOwners.length > 0) {
          return response.multiActionOwners.map((owner: any) => owner.firstName).join(', ');
        }
        // Handle legacy actionOwners field (single)
        if (response.actionOwners) {
          return response.actionOwners.firstName;
        }
        return null;
      })(),
      reviewedBy: response.reviewer ? `${response.reviewer.firstName} ${response.reviewer.lastName || ''}`.trim() : null,
      status: response.status,
      description: response.description || '',
      attachments: response.uploads || [],
      uploads: response.uploads || [],
      dueDate: response.dueDate ? parseISO(response.dueDate) : null,
      rectifiedOnSpot: response.rectifiedOnSpot,
      actionTaken: response.actionTaken || null,
      evidenceImages: response.evidence || [],
      evidence: response.evidence || [],
      needsReviewer: response.isReviewerRequired,
      actionToBeTaken: response.actionToBeTaken || null,
      comments: response.comments || '',
      submittedBy: response.reporter ? `${response.reporter.firstName} ${response.reporter.lastName || ''}`.trim() : 'Unknown',
      observationActions: response.observationActions?.map(action => ({
        id: action.id,
        actionType: action.actionType,
        status: action.status,
        actionToBeTaken: action.actionToBeTaken,
        actionTaken: action.actionTaken,
        dueDate: action.dueDate,
        createdDate: action.created,
        assignedToId: action.assignedToId,
        uploads: action.uploads || [],
        application: action.application,
        applicationId: action.applicationId,
        maskId: action.maskId,
        trackId: action.trackId,
        description: action.description,
        sequence: action.sequence,
        prefix: action.prefix,
        objectId: action.objectId,
        submitURL: action.submitURL,
        serviceId: action.serviceId,
        submittedById: action.submittedById,
        submittedBy: action.submittedBy
      }))
    };
  }, [formatLocation]);

  // Function to fetch observation data
  const fetchObservationData = useCallback(async () => {
    if (!accessToken) {
      console.warn('No access token available - skipping observation data fetch');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetchObservations(accessToken);

      // Map API response to Observation format
      const observations = response.map(mapApiResponseToObservation);

      setAllObservations(observations);
      setIncidentCount(observations.length);

      // Create MyAction objects for observations assigned to the current user
      // This is a simplified approach - in a real app, you'd filter based on the current user
      const userActions = observations
        .filter(obs => obs.actionAssignee || obs.reviewedBy)
        .map(obs => ({
          id: obs.id,
          location: obs.location,
          description: obs.description || 'No description provided',
          submittedBy: obs.reportedBy,
          dueDate: obs.dueDate ? obs.dueDate.toISOString().split('T')[0] : 'N/A',
          status: obs.status
        }));

      setMyActions(userActions);
      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching observations:', error);

      // Check if it's an authentication error
      if (error instanceof Error && error.message.includes('401')) {
        toast({
          title: "Authentication Error",
          description: "Please log in again to access your data.",
          variant: "destructive"
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to load observations. Please try again.",
          variant: "destructive"
        });
      }
      setIsLoading(false);
    }
  }, [accessToken, toast, mapApiResponseToObservation]);

  // Function to fetch observation actions
  const fetchActions = useCallback(async () => {
    if (!accessToken) {
      console.warn('No access token available - skipping actions fetch');
      setLoadingActions(false);
      return;
    }

    setLoadingActions(true); // Start loading

    try {
      const actions = await fetchObservationActions(accessToken);
      setObservationActions(actions);
      setActionCount(actions.length);
    } catch (error) {
      console.error('Error fetching actions:', error);

      // Check if it's an authentication error
      if (error instanceof Error && error.message.includes('401')) {
        toast({
          title: "Authentication Error",
          description: "Please log in again to access your actions.",
          variant: "destructive"
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to load actions. Please try again.",
          variant: "destructive"
        });
      }
    } finally {
      setLoadingActions(false); // Stop loading regardless of success or error
    }
  }, [accessToken, toast]);

  // Fetch observations when component mounts and when accessToken changes
  useEffect(() => {
    if (accessToken) {
      fetchObservationData();
      fetchActions(); // Also fetch actions
    } else {
      // Reset data when no token is available
      setAllObservations([]);
      setObservationActions([]);
      setIncidentCount(0);
      setActionCount(0);
      setIsLoading(false);
      setLoadingActions(false);
    }
  }, [accessToken, fetchObservationData, fetchActions]);

  // State for details modal
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedObservation, setSelectedObservation] = useState<Observation | null>(null);
  const [selectedMyAction, setSelectedMyAction] = useState<MyAction | null>(null);

  // State for actions modal
  const [isActionsModalOpen, setIsActionsModalOpen] = useState(false);
  const [selectedAction, setSelectedAction] = useState<ObservationAction | null>(null);
  const [selectedObservationDetails, setSelectedObservationDetails] = useState<ObservationResponse | null>(null);

  // Current user - in a real app, this would come from authentication
  const currentUser = "Current User";

  // Function to handle ID click
  const handleIdClick = (id: string, isMyAction: boolean = false) => {
    if (isMyAction) {
      const action = myActions.find(item => item.id === id);
      if (action) {
        setSelectedMyAction(action);
        setSelectedObservation(null);
        setIsDetailsModalOpen(true);
      }
    } else {
      // First try to find by maskId, then fall back to id
      const observation = allObservations.find(item => item.maskId === id) ||
                          allObservations.find(item => item.id === id);
      if (observation) {
        setSelectedObservation(observation);
        setSelectedMyAction(null);
        setIsDetailsModalOpen(true);
      }
    }
  };

  // Function to handle action click
  const handleActionClick = async (action: ObservationAction) => {
    if (!accessToken) {
      toast({
        title: "Authentication Error",
        description: "Please log in to view action details.",
        variant: "destructive"
      });
      return;
    }

    try {
      setSelectedAction(action);

      // Fetch observation details
      const uriString = {
        include: [
          { relation: "locationOne" },
          { relation: "locationTwo" },
          { relation: "locationThree" },
          { relation: "locationFour" },
          { relation: "locationFive" },
          { relation: "locationSix" },
          { relation: "reporter" },
          { relation: "actionOwner" },
          { relation: "reviewer" },
        ]
      };

      const url = `/observation-reports/${action.applicationId}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      // Use centralized API service which includes 401 handling
      const data = await apiService.get(url);
      setSelectedObservationDetails(data);
      setIsActionsModalOpen(true);
    } catch (error) {
      console.error('Error fetching observation details:', error);
      toast({
        title: "Error",
        description: "Failed to load observation details. Please try again.",
        variant: "destructive"
      });
    }
  };

  const filteredMyActions = observationActions.filter(item =>
    Object.values(item).some(val =>
      String(val).toLowerCase().includes(myActionsSearch.toLowerCase())
    )
  );

  const filteredAllObservations = allObservations.filter(item =>
    Object.values(item).some(val =>
      String(val).toLowerCase().includes(allObservationsSearch.toLowerCase())
    )
  );

  const handleObservationSubmit = (_submissionData: ObservationSubmitData) => {
    // Show success message
    toast({
      title: "Observation Recorded",
      description: "Your observation has been successfully recorded.",
      variant: "default"
    });

    // Refresh the observation list to get the latest data from the server
    fetchObservationData();
    fetchActions();
  };

  interface ReviewData {
    status?: string;
    comments?: string;
    reviewedBy?: string;
    actionTaken?: string;
    dueDate?: Date;
    actionOwner?: string;
  }

  // Handle review submission
  const handleReviewSubmit = (observationId: string, reviewData: ReviewData) => {
    // Find the observation to update
    const observationIndex = allObservations.findIndex(obs => obs.id === observationId);

    if (observationIndex !== -1) {
      // Create updated observation
      const updatedObservation = {
        ...allObservations[observationIndex],
        actionTaken: reviewData.actionTaken,
        dueDate: reviewData.dueDate,
        actionAssignee: reviewData.actionOwner,
        status: 'In Progress'
      };

      // Update the observation in the all observations list
      const newAllObservations = [...allObservations];
      newAllObservations[observationIndex] = updatedObservation;
      setAllObservations(newAllObservations);

      // Create a MyAction for the action owner
      const myAction: MyAction = {
        id: updatedObservation.id,
        location: updatedObservation.location,
        description: updatedObservation.description || 'No description provided',
        submittedBy: updatedObservation.reportedBy,
        dueDate: updatedObservation.dueDate ? updatedObservation.dueDate.toISOString().split('T')[0] : 'N/A',
        status: updatedObservation.status
      };

      // Add to My Actions list (or update if already exists)
      setMyActions(prev => {
        const existingIndex = prev.findIndex(action => action.id === myAction.id);
        if (existingIndex !== -1) {
          const newMyActions = [...prev];
          newMyActions[existingIndex] = myAction;
          return newMyActions;
        } else {
          return [myAction, ...prev];
        }
      });

      // Show success message
      const observation = allObservations.find(obs => obs.id === observationId);
      const displayId = observation?.maskId || observationId;

      toast({
        title: "Review Submitted",
        description: `Observation ${displayId} has been updated and assigned to ${reviewData.actionOwner}.`,
        variant: "default"
      });
    }
  };

  // Handle PDF download for observations
  const handleObservationPdfDownload = async (observation: Observation) => {
    try {
      console.log('Generating PDF for observation:', observation.id);

      await PDFService.downloadPDF({
        observation,
        companyName: 'AcuiZen',
        // You can add logoUrl here if available
        // logoUrl: 'path/to/logo.png'
      }, `Observation_Report_${observation.id}.pdf`);

      toast({
        title: "PDF Generated",
        description: "The observation report has been downloaded successfully.",
        variant: "default"
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: "PDF Generation Failed",
        description: "There was an error generating the PDF report. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Create tabs with updated counts
  const tabs = [
    {
      value: "my-actions",
      label: `My Actions (${loadingActions ? '...' : actionCount})`,
      content: (
        <div className="space-y-4">
          <div className="flex items-center justify-between gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search my actions..."
                value={myActionsSearch}
                onChange={(e) => setMyActionsSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="text-sm text-muted-foreground">
              {loadingActions ? (
                <div className="flex items-center">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Loading actions...
                </div>
              ) : (
                `Showing ${filteredMyActions.length} of ${actionCount} items`
              )}
            </div>
          </div>

          {loadingActions ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                <p className="text-muted-foreground">Loading action data...</p>
              </div>
            </div>
          ) : (
            <ExpandableDataTable
              data={filteredMyActions}
              columns={[
                {
                  key: 'maskId',
                  header: 'Action ID',
                  sortable: true,
                  width: 'w-[120px]',
                  sortFunction: sortActionIds,
                  render: (value, row) => (
                    <span
                      className="text-blue-600 hover:text-blue-800 hover:underline font-medium cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleActionClick(row);
                      }}
                    >
                      {value}
                    </span>
                  )
                },
                {
                  key: 'myRole',
                  header: 'My Role',
                  width: 'w-[120px]',
                  filterable: true,
                  filterType: 'text',
                  render: (_, row) => {
                    const currentUserId = user?.id || '';
                    const role = getUserRole(row, currentUserId);
                    let badgeClass = '';
                    switch(role) {
                      case 'Action Owner': badgeClass = 'bg-blue-700 hover:bg-blue-800 text-white border-blue-800'; break;
                      case 'Reviewer': badgeClass = 'bg-orange-600 hover:bg-orange-700 text-white border-orange-700'; break;
                      case 'Submitter': badgeClass = 'bg-emerald-600 hover:bg-emerald-700 text-white border-emerald-700'; break;
                      case 'Assignee': badgeClass = 'bg-violet-600 hover:bg-violet-700 text-white border-violet-700'; break;
                      case 'Viewer': badgeClass = 'bg-slate-600 hover:bg-slate-700 text-white border-slate-700'; break;
                      default: badgeClass = 'bg-gray-500 hover:bg-gray-600 text-white border-gray-600';
                    }
                    return <Badge className={badgeClass}>{role}</Badge>;
                  }
                },
                {
                  key: 'actionType',
                  header: 'Required Action',
                  width: 'w-[120px]',
                  filterable: true,
                  filterType: 'text',
                  render: (value) => getActionTypeDisplay(value)
                },
                {
                  key: 'submittedBy',
                  header: 'Submitted By',
                  width: 'w-[140px]',
                  filterable: true,
                  filterType: 'text',
                  render: (value) => value?.firstName || '-'
                },
                {
                  key: 'dueDate',
                  header: 'Due Date',
                  width: 'w-[120px]',
                  render: (value) => {
                    // Handle invalid date values gracefully
                    if (!value) return 'N/A';
                    try {
                      const date = new Date(value);
                      // Check if date is valid
                      if (isNaN(date.getTime())) return 'N/A';
                      return format(date, 'MM/dd/yyyy');
                    } catch (error) {
                      console.error('Error formatting date:', error, value);
                      return 'N/A';
                    }
                  }
                },
                {
                  key: 'timeline',
                  header: 'Timeline',
                  width: 'w-[120px]',
                  filterable: true,
                  filterType: 'select',
                  filterOptions: [
                    { label: 'Upcoming', value: 'Upcoming' },
                    { label: 'Due Now', value: 'Due Now' },
                    { label: 'Overdue', value: 'Overdue' },
                    { label: 'No Due Date', value: 'No Due Date' },
                  ],
                  render: (_, row) => {
                    const timelineStatus = getTimelineStatus(row.dueDate);
                    let badgeClass = '';
                    switch(timelineStatus) {
                      case 'Upcoming': badgeClass = 'bg-teal-600 hover:bg-teal-700 text-white border-teal-700'; break;
                      case 'Due Now': badgeClass = 'bg-amber-500 hover:bg-amber-600 text-black border-amber-600 font-semibold'; break;
                      case 'Overdue': badgeClass = 'bg-red-600 hover:bg-red-700 text-white border-red-700 font-semibold'; break;
                      case 'No Due Date': badgeClass = 'bg-neutral-500 hover:bg-neutral-600 text-white border-neutral-600'; break;
                      case 'Invalid Date': badgeClass = 'bg-rose-500 hover:bg-rose-600 text-white border-rose-600'; break;
                      default: badgeClass = 'bg-gray-400 hover:bg-gray-500 text-white border-gray-500';
                    }
                    return <Badge className={badgeClass}>{timelineStatus}</Badge>;
                  }
                },
                // {
                //   key: 'status',
                //   header: 'Status',
                //   width: 'w-[120px]',
                //   filterable: true,
                //   filterType: 'select',
                //   filterOptions: [
                //     { label: 'Initiated', value: 'Initiated' },
                //     { label: 'In Progress', value: 'In Progress' },
                //     { label: 'Action in Progress', value: 'Action in Progress' },
                //     { label: 'Rectified on Spot', value: 'Rectified on Spot' },
                //     { label: 'In Review', value: 'In Review' },
                //     { label: 'Completed', value: 'Completed' },
                //     { label: 'Rejected', value: 'Rejected' },
                //   ],
                //   render: (value) => {
                //     let badgeClass = '';
                //     switch(value) {
                //       case 'Initiated': badgeClass = 'bg-safety-400 hover:bg-safety-500 text-white'; break;
                //       case 'In Progress': badgeClass = 'bg-primary hover:bg-primary/90 text-white'; break;
                //       case 'Action in Progress': badgeClass = 'bg-blue-600 hover:bg-blue-700 text-white'; break;
                //       case 'Rectified on Spot': badgeClass = 'bg-purple-500 hover:bg-purple-600 text-white'; break;
                //       case 'In Review': badgeClass = 'bg-orange-500 hover:bg-orange-600 text-white'; break;
                //       case 'Completed': badgeClass = 'bg-success-500 hover:bg-success-600 text-white'; break;
                //       case 'Rejected': badgeClass = 'bg-destructive hover:bg-destructive/90 text-white'; break;
                //       default: badgeClass = 'bg-muted hover:bg-muted/90 text-white';
                //     }
                //     return <Badge className={badgeClass}>{value}</Badge>;
                //   }
                // },
              ]}
              onRowClick={(row) => handleActionClick(row)}
              highlightOnHover={true}
              striped={true}
            />
          )}
        </div>
      )
    },
    {
      value: "all-observations",
      label: `All Observations (${isLoading ? '...' : incidentCount})`,
      content: (
        <div className="space-y-4">
          <div className="flex items-center justify-between gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search all observations..."
                value={allObservationsSearch}
                onChange={(e) => setAllObservationsSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-muted-foreground">
                {isLoading ? (
                  <div className="flex items-center">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading observations...
                  </div>
                ) : (
                  `Showing ${filteredAllObservations.length} of ${incidentCount} items`
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => toast({ title: "Export Data", description: "Exporting all observations data" })}
                disabled={isLoading}
                className="bg-blue-50 hover:bg-blue-100 border-blue-300 text-blue-700"
              >
                <Download className="h-4 w-4 mr-2" /> Export
              </Button>
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                <p className="text-muted-foreground">Loading observation data...</p>
              </div>
            </div>
          ) : (
            <>
              <ExpandableDataTable
                data={filteredAllObservations}
                columns={[
                  {
                    key: 'maskId',
                    header: 'ID',
                    sortable: true,
                    width: 'w-[120px]',
                    sortFunction: sortActionIds,
                    render: (value) => (
                      <span className="text-blue-600 hover:text-blue-800 hover:underline font-medium cursor-pointer">
                        {value}
                      </span>
                    )
                  },
                  { key: 'location', header: 'Location', width: 'w-[180px]', filterable: true, filterType: 'text' },
                  {
                    key: 'category',
                    header: 'Category',
                    width: 'w-[140px]',
                    filterable: true,
                    filterType: 'select',
                    filterOptions: [
                      { label: 'Equipment', value: 'Equipment' },
                      { label: 'Infrastructure', value: 'Infrastructure' },
                      { label: 'Compliance', value: 'Compliance' },
                      { label: 'Safety', value: 'Safety' },
                      { label: 'Procedure', value: 'Procedure' },
                    ]
                  },
                  {
                    key: 'type',
                    header: 'Type',
                    width: 'w-[120px]',
                    filterable: true,
                    filterType: 'select',
                    filterOptions: [
                      { label: 'Safe', value: 'Safe' },
                      { label: 'Unsafe', value: 'Unsafe' },
                    ]
                  },
                  { key: 'reportedBy', header: 'Reported By', width: 'w-[140px]', filterable: true, filterType: 'text' },
                  {
                    key: 'reportedDate',
                    header: 'Reported Date',
                    width: 'w-[120px]',
                    sortable: true,
                    render: (value) => {
                      // Handle invalid date values gracefully
                      if (!value) return '-';
                      try {
                        const date = new Date(value);
                        // Check if date is valid
                        if (isNaN(date.getTime())) return '-';
                        return format(date, 'MM/dd/yyyy');
                      } catch (error) {
                        console.error('Error formatting date:', error, value);
                        return '-';
                      }
                    }
                  },
                  { key: 'actionAssignee', header: 'Action Assignee', width: 'w-[140px]', filterable: true, filterType: 'text' },
                  {
                    key: 'reviewedBy',
                    header: 'Reviewed By',
                    width: 'w-[140px]',
                    filterable: true,
                    filterType: 'text',
                    render: (value) => value || '-'
                  },
                  {
                    key: 'status',
                    header: 'Status',
                    width: 'w-[120px]',
                    filterable: true,
                    filterType: 'select',
                    filterOptions: [
                      { label: 'New', value: 'New' },
                      { label: 'Open', value: 'Open' },
                      { label: 'In Progress', value: 'In Progress' },
                      { label: 'Action in Progress', value: 'Action in Progress' },
                      { label: 'Rectified on Spot', value: 'Rectified on Spot' },
                      { label: 'Pending Review', value: 'Pending Review' },
                      { label: 'In Review', value: 'In Review' },
                      { label: 'Completed', value: 'Completed' },
                      { label: 'Closed', value: 'Closed' },
                      { label: 'Action Completed & Closed', value: 'Action Completed & Closed' },
                    ],
                    render: (value) => {
                      let badgeClass = '';
                      switch(value) {
                        case 'New': badgeClass = 'bg-safety-400 hover:bg-safety-500 text-white'; break;
                        case 'Open': badgeClass = 'bg-warning-400 hover:bg-warning-500 text-black'; break;
                        case 'In Progress': badgeClass = 'bg-primary hover:bg-primary/90 text-white'; break;
                        case 'Action in Progress': badgeClass = 'bg-blue-600 hover:bg-blue-700 text-white'; break;
                        case 'Rectified on Spot': badgeClass = 'bg-purple-500 hover:bg-purple-600 text-white'; break;
                        case 'Pending Review': badgeClass = 'bg-muted-foreground hover:bg-gray-600 text-white'; break;
                        case 'In Review': badgeClass = 'bg-orange-500 hover:bg-orange-600 text-white'; break;
                        case 'Completed': badgeClass = 'bg-green-600 hover:bg-green-700 text-white'; break;
                        case 'Closed': badgeClass = 'bg-success-500 hover:bg-success-600 text-white'; break;
                        case 'Action Completed & Closed': badgeClass = 'bg-indigo-600 hover:bg-indigo-700 text-white'; break;
                        default: badgeClass = 'bg-muted hover:bg-muted/90 text-white';
                      }
                      return <Badge className={badgeClass}>{value}</Badge>;
                    }
                  },
                ]}
                onRowClick={(row) => handleIdClick(row.maskId || row.id, false)}
                highlightOnHover={true}
                striped={true}
                showActions={false}
                onEdit={(item) => toast({ title: "Edit Observation", description: `Editing observation ${item.maskId || item.id}` })}
                onDelete={(item) => toast({ title: "Delete Observation", description: `Deleting observation ${item.maskId || item.id}`, variant: "destructive" })}
              />
            </>
          )}
        </div>
      )
    }
  ];

  // Show authentication message if no token
  if (!accessToken) {
    return (
      <div className="space-y-4">
        <PageHeader
          title="Observations"
          description="Track and manage safety observations across your organization"
        />
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Authentication Required</h3>
            <p className="text-muted-foreground">Please log in to view your observations and actions.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <PageHeader
          title="Observations"
          description="Track and manage safety observations across your organization"
        />
        {user && (user as any)?.roles?.some((role: any) => ['obsreporter'].includes(role.maskId)) && (
          <Button className="flex items-center gap-1" onClick={() => setIsModalOpen(true)}>
            <Plus className="h-4 w-4" /> Record Observation
          </Button>
        )}
      </div>

      <TabsContainer
        key={`tabs-${actionCount}-${incidentCount}`}
        tabs={tabs}
        defaultValue="all-observations"
        onValueChange={(value) => setActiveTab(value)}
      />

      {/* Record Observation Modal */}
      <RecordObservationModal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        onSubmit={handleObservationSubmit}
      />

      {/* Observation Details Modal */}
      <ObservationDetailsModal
        open={isDetailsModalOpen}
        onOpenChange={setIsDetailsModalOpen}
        observation={selectedObservation}
        myAction={selectedMyAction}
        currentUser={currentUser}
        onReviewSubmit={handleReviewSubmit}
      />

      {/* Actions Modal */}
      {selectedAction && selectedObservationDetails && (
        <ActionsModal
          show={isActionsModalOpen}
          applicationDetails={selectedObservationDetails}
          showItem={selectedAction}
          closeModal={() => {
            setIsActionsModalOpen(false);
            setSelectedAction(null);
            setSelectedObservationDetails(null);
          }}
          accessToken={accessToken}
          onActionSubmitted={fetchActions}
        />
      )}
    </div>
  );
};

export default ObservationPage;
