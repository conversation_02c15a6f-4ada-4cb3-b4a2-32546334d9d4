import React, { useEffect, useState, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { format, isValid } from 'date-fns';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import apiService from '@/services/apiService';
import { useReactToPrint } from 'react-to-print';
import {
  Calendar,
  User as UserIcon,
  FileText,
  CheckCircle,
  AlertCircle,
  Building,
  PenTool,
  TextCursor,
  Heading1,
  Heading2,
  Type,
  ClipboardList,
  Printer,
  Image,
  Settings,
  Hash
} from 'lucide-react';

interface ChecklistDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  checklistId: string | null;
}

interface ChecklistDetails {
  id: string;
  customId: string;
  name: string;
  description: string;
  category: string;
  version: string;
  status: string;
  created: string;
  updated: string;
  curator?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  value?: {
    metadata?: {
      name: string;
      version: string;
      createdAt: string;
      totalComponents: number;
      modes: {
        communicate: number;
        feedback: number;
      };
    };
    components?: any[];
  };
}

const ChecklistDetailsModal: React.FC<ChecklistDetailsModalProps> = ({
  open,
  onOpenChange,
  checklistId
}) => {
  const [checklistDetails, setChecklistDetails] = useState<ChecklistDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const { accessToken } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();
  const printRef = useRef<HTMLDivElement>(null);

  const handlePrint = useReactToPrint({
    contentRef: printRef,
    documentTitle: `Checklist_${checklistDetails?.customId}_${format(new Date(), 'yyyyMMdd_HHmm')}`,
    onAfterPrint: () => {
      toast({
        title: "Print Successful",
        description: "The checklist details have been sent to the printer.",
        variant: "default",
      });
    },
    onPrintError: (error) => {
      console.error('Print error:', error);
      toast({
        title: "Print Failed",
        description: "An error occurred while printing. Please try again.",
        variant: "destructive",
      });
    },
  });

  useEffect(() => {
    if (open && checklistId && accessToken) {
      fetchChecklistDetails();
    }
  }, [open, checklistId, accessToken]);

  const fetchChecklistDetails = async () => {
    if (!checklistId || !accessToken) return;
    
    setLoading(true);
    try {
      const uriString = {
        include: [{ relation: "curator" }]
      };
      
      const url = `/checklists/${checklistId}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      const response = await apiService.get(url);
      setChecklistDetails(response);
    } catch (error) {
      console.error('Error fetching checklist details:', error);
      toast({
        title: "Error",
        description: "Failed to fetch checklist details. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string | undefined | null) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      return isValid(date) ? format(date, 'dd-MM-yyyy HH:mm') : "N/A";
    } catch {
      return "N/A";
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return <Badge className="bg-green-500 text-white"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>;
      case 'published':
        return <Badge className="bg-blue-500 text-white"><FileText className="w-3 h-3 mr-1" />Published</Badge>;
      case 'draft':
        return <Badge className="bg-yellow-500 text-white"><Settings className="w-3 h-3 mr-1" />Draft</Badge>;
      case 'archived':
        return <Badge className="bg-red-500 text-white"><AlertCircle className="w-3 h-3 mr-1" />Archived</Badge>;
      default:
        return <Badge variant="secondary"><AlertCircle className="w-3 h-3 mr-1" />{status || 'Unknown'}</Badge>;
    }
  };

  const getComponentIcon = (type: string) => {
    const iconProps = { className: "h-4 w-4" };
    
    switch (type) {
      case 'header':
        return <Heading1 {...iconProps} />;
      case 'section-header':
        return <Heading2 {...iconProps} />;
      case 'text-body':
        return <Type {...iconProps} />;
      case 'text-input':
        return <TextCursor {...iconProps} />;
      case 'image-input':
        return <Image {...iconProps} />;
      case 'sign':
        return <PenTool {...iconProps} />;
      case 'checkpoint':
        return <CheckCircle {...iconProps} />;
      case 'checkpoint-group':
        return <ClipboardList {...iconProps} />;
      case 'date':
        return <Calendar {...iconProps} />;
      default:
        return <Hash {...iconProps} />;
    }
  };

  const getComponentLabel = (type: string) => {
    switch (type) {
      case 'header': return 'Header';
      case 'section-header': return 'Section Header';
      case 'text-body': return 'Text Body';
      case 'text-input': return 'Text Input';
      case 'image-input': return 'Image Input';
      case 'sign': return 'Signature';
      case 'checkpoint': return 'Checkpoint';
      case 'checkpoint-group': return 'Checkpoint Group';
      case 'date': return 'Date';
      default: return type || 'Component';
    }
  };

  const renderComponents = () => {
    const components = checklistDetails?.value?.components || [];
    
    if (components.length === 0) {
      return (
        <Card>
          <CardContent className="p-6 text-center text-muted-foreground">
            <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No components found in this checklist.</p>
          </CardContent>
        </Card>
      );
    }

    // Sort components by position
    const sortedComponents = [...components].sort((a, b) => (a.position || 0) - (b.position || 0));

    return (
      <div className="space-y-3">
        {sortedComponents.map((component: any, index: number) => (
          <Card key={component.id || `component-${index}`} className="border-l-4 border-l-blue-400">
            <CardContent className="pt-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 mt-1">
                  {getComponentIcon(component.type)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-sm">{getComponentLabel(component.type)}</span>
                    <Badge variant="outline" className="text-xs">
                      Position {(component.position || 0) + 1}
                    </Badge>
                    {component.data?.required && (
                      <Badge variant="destructive" className="text-xs">Required</Badge>
                    )}
                  </div>
                  {component.data?.title && (
                    <p className="text-sm text-gray-700 mb-1">
                      <strong>Title:</strong> {component.data.title}
                    </p>
                  )}
                  {component.data?.text && (
                    <p className="text-sm text-gray-700 mb-1">
                      <strong>Text:</strong> {component.data.text}
                    </p>
                  )}
                  {component.data?.label && (
                    <p className="text-sm text-gray-700 mb-1">
                      <strong>Label:</strong> {component.data.label}
                    </p>
                  )}
                  {component.data?.content && (
                    <p className="text-sm text-gray-700 mb-1">
                      <strong>Content:</strong> {component.data.content.substring(0, 100)}
                      {component.data.content.length > 100 && '...'}
                    </p>
                  )}
                  {component.data?.placeholder && (
                    <p className="text-sm text-gray-500">
                      <strong>Placeholder:</strong> {component.data.placeholder}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  if (!checklistDetails && !loading) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-2xl font-bold">
                {checklistDetails?.name || 'Checklist Details'}
              </DialogTitle>
              <DialogDescription className="text-base mt-1">
                ID: {checklistDetails?.customId} • Version: {checklistDetails?.version}
              </DialogDescription>
            </div>
            <div className="flex items-center space-x-3 no-print">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrint}
                className="bg-green-50 hover:bg-green-100 border-green-300 text-green-700"
              >
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              {checklistDetails && getStatusBadge(checklistDetails.status)}
            </div>
          </div>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-muted-foreground">Loading checklist details...</p>
            </div>
          </div>
        ) : (
          <div ref={printRef} className="print-content">
            <div className="space-y-6">
              {/* Overview Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-3">
                      <Building className="w-8 h-8 text-blue-500" />
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Category</p>
                        <p className="text-lg font-semibold">{checklistDetails?.category || "N/A"}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-3">
                      <Hash className="w-8 h-8 text-green-500" />
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Version</p>
                        <p className="text-lg font-semibold">{checklistDetails?.version || "N/A"}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-3">
                      <UserIcon className="w-8 h-8 text-purple-500" />
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Curator</p>
                        <p className="text-lg font-semibold">{checklistDetails?.curator?.firstName || "N/A"}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-3">
                      <ClipboardList className="w-8 h-8 text-orange-500" />
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Components</p>
                        <p className="text-lg font-semibold">
                          {checklistDetails?.value?.components?.length || 0}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Description */}
              {checklistDetails?.description && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <FileText className="w-5 h-5" />
                      <span>Description</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm leading-relaxed">{checklistDetails.description}</p>
                  </CardContent>
                </Card>
              )}

              {/* Metadata */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Calendar className="w-5 h-5" />
                    <span>Metadata</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Created</label>
                      <p className="text-sm mt-1">{formatDate(checklistDetails?.created)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                      <p className="text-sm mt-1">{formatDate(checklistDetails?.updated)}</p>
                    </div>
                  </div>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Checklist ID</label>
                    <p className="text-sm mt-1 font-mono">{checklistDetails?.customId || "N/A"}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Components */}
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
                  <ClipboardList className="w-5 h-5" />
                  <span>Checklist Components</span>
                </h3>
                {renderComponents()}
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ChecklistDetailsModal;
