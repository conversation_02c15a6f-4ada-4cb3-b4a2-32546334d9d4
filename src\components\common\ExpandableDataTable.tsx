import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Plus,
  Download,
  ArrowUp,
  ArrowDown,
  Filter,
  Pencil,
  Trash2
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from '@/lib/utils';

interface Column {
  key: string;
  header: string;
  sortable?: boolean;
  filterable?: boolean;
  filterType?: 'text' | 'select';
  filterOptions?: { label: string; value: string }[];
  render?: (value: any, row: any) => React.ReactNode;
  width?: string;
  sortFunction?: (a: any, b: any) => number;
}

// Define a generic type for the data rows
interface DataRow {
  [key: string]: any;
}

interface CustomAction<T = any> {
  icon: React.ReactNode;
  title: string;
  onClick: (item: T) => void;
  className?: string;
}

interface ExpandableDataTableProps<T extends DataRow = DataRow> {
  data: T[];
  columns: Column[];
  onRowClick?: (row: T) => void;
  rowKeyField?: string;
  highlightOnHover?: boolean;
  striped?: boolean;
  className?: string;
  showActions?: boolean;
  onEdit?: (item: T) => void;
  onDelete?: (item: T) => void;
  customActions?: CustomAction<T>[];
  showAddButton?: boolean;
  onAdd?: () => void;
  showExportButton?: boolean;
  onExport?: () => void;
  addButtonText?: string;
}

const ExpandableDataTable = <T extends DataRow = DataRow>({
  data,
  columns,
  onRowClick,
  rowKeyField = 'id',
  highlightOnHover = true,
  striped = true,
  className,
  showActions = false,
  onEdit,
  onDelete,
  customActions = [],
  showAddButton = false,
  onAdd,
  showExportButton = false,
  onExport,
  addButtonText = "Add New",
}: ExpandableDataTableProps<T>) => {
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);
  const [filters, setFilters] = useState<Record<string, string>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Apply sorting and filtering
  const filteredData = data.filter((item) => {
    return Object.keys(filters).every((key) => {
      if (!filters[key]) return true;
      return String(item[key]).toLowerCase().includes(filters[key].toLowerCase());
    });
  });

  const sortedData = React.useMemo(() => {
    if (!sortConfig) return filteredData;

    // Find the column configuration for the sort key
    const column = columns.find(col => col.key === sortConfig.key);

    return [...filteredData].sort((a, b) => {
      let result = 0;

      if (column?.sortFunction) {
        // Use custom sort function if provided
        result = column.sortFunction(a[sortConfig.key], b[sortConfig.key]);
      } else {
        // Default sorting logic
        if (a[sortConfig.key] < b[sortConfig.key]) {
          result = -1;
        } else if (a[sortConfig.key] > b[sortConfig.key]) {
          result = 1;
        } else {
          result = 0;
        }
      }

      // Apply sort direction
      return sortConfig.direction === 'asc' ? result : -result;
    });
  }, [filteredData, sortConfig, columns]);

  // Pagination
  const totalPages = Math.ceil(sortedData.length / itemsPerPage);
  const paginatedData = sortedData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div className={cn("space-y-4", className)}>
      <div className="table-container overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full text-sm table-fixed">
            <thead className="table-header border-b">
              <tr>
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className={cn(
                      "px-4 py-3 text-left font-medium",
                      column.width ? column.width : ""
                    )}
                    style={{
                      width: column.width ? undefined : `${100 / (columns.length + (showActions ? 1 : 0))}%`
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1">
                        <span className={cn(
                          "select-none",
                          column.sortable && "cursor-pointer hover:text-primary"
                        )}
                        onClick={column.sortable ? () => handleSort(column.key) : undefined}
                        >
                          {column.header}
                        </span>
                        {column.sortable && (
                          <button
                            type="button"
                            onClick={() => handleSort(column.key)}
                            className="ml-1 p-1 hover:bg-muted rounded focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1 transition-colors border border-transparent hover:border-muted-foreground/20"
                            title={`Sort by ${column.header} ${sortConfig?.key === column.key ? (sortConfig.direction === 'asc' ? '(ascending)' : '(descending)') : ''}`}
                          >
                            {sortConfig?.key === column.key ? (
                              sortConfig.direction === 'asc' ? (
                                <ArrowUp className="h-4 w-4 text-primary font-bold" />
                              ) : (
                                <ArrowDown className="h-4 w-4 text-primary font-bold" />
                              )
                            ) : (
                              <div className="h-4 w-4 opacity-40 hover:opacity-70 transition-opacity">
                                <ArrowUp className="h-4 w-4 text-muted-foreground" />
                              </div>
                            )}
                          </button>
                        )}
                      </div>

                      {column.filterable && (
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-7 w-7">
                              <Filter className="h-3.5 w-3.5" />
                              {filters[column.key] && (
                                <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-primary"></span>
                              )}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-60 p-3" align="end">
                            <div className="space-y-2">
                              <h4 className="font-medium text-sm">Filter {column.header}</h4>
                              {column.filterType === 'select' ? (
                                <Select
                                  onValueChange={(value) => handleFilterChange(column.key, value)}
                                  value={filters[column.key] || "all"}
                                >
                                  <SelectTrigger className="w-full">
                                    <SelectValue placeholder={`Select ${column.header}`} />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">All</SelectItem>
                                    {column.filterOptions?.map((option) => (
                                      <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              ) : (
                                <Input
                                  placeholder={`Filter by ${column.header.toLowerCase()}`}
                                  value={filters[column.key] || ''}
                                  onChange={(e) => handleFilterChange(column.key, e.target.value)}
                                />
                              )}
                              {filters[column.key] && (
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="w-full mt-2"
                                  onClick={() => handleFilterChange(column.key, '')}
                                >
                                  Clear filter
                                </Button>
                              )}
                            </div>
                          </PopoverContent>
                        </Popover>
                      )}
                    </div>
                  </th>
                ))}
                {showActions && (
                  <th
                    className="px-4 py-3 text-center"
                    style={{ width: `${Math.max(100, (2 + customActions.length) * 40)}px` }}
                  >
                    Actions
                  </th>
                )}
              </tr>
            </thead>
            <tbody>
              {paginatedData.length > 0 ? (
                paginatedData.map((row, index) => {
                  const rowId = row[rowKeyField]?.toString() || index.toString();

                  return (
                    <tr
                      key={rowId}
                      data-lov-id={rowId}
                      className={cn(
                        "table-row transition-colors",
                        highlightOnHover && "hover:bg-muted/20",
                        striped && index % 2 === 1 && "bg-muted/5"
                      )}
                      onClick={() => onRowClick && onRowClick(row)}
                    >
                      {columns.map((column) => (
                        <td
                          key={`${rowId}-${column.key}`}
                          className="table-cell"
                          style={{
                            width: column.width ? undefined : `${100 / (columns.length + (showActions ? 1 : 0))}%`,
                            display: 'table-cell' // Ensure cell is visible
                          }}
                        >
                          {column.render ? column.render(row[column.key], row) : row[column.key]}
                        </td>
                      ))}
                      {showActions && (
                        <td
                          className="table-cell text-center"
                          style={{ width: `${Math.max(100, (2 + customActions.length) * 40)}px` }}
                        >
                          <div className="flex justify-center gap-1">
                            {customActions.map((action, actionIndex) => (
                              <Button
                                key={actionIndex}
                                type="button"
                                variant="ghost"
                                size="icon"
                                className={action.className || "h-8 w-8 text-blue-500 hover:text-blue-700 hover:bg-blue-50"}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  action.onClick(row);
                                }}
                                title={action.title}
                              >
                                {action.icon}
                              </Button>
                            ))}
                            {onEdit && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-primary hover:text-primary-dark hover:bg-primary-50"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onEdit(row);
                                }}
                                title="Edit"
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                            )}
                            {onDelete && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-danger-500 hover:text-danger-700 hover:bg-danger-50"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onDelete(row);
                                }}
                                title="Delete"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </td>
                      )}
                    </tr>
                  );
                })
              ) : (
                <tr className="table-row">
                  <td
                    colSpan={columns.length + (showActions ? 1 : 0)}
                    className="table-cell py-8 text-center text-gray-500"
                  >
                    No data found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        {(showAddButton || showExportButton) && (
          <div className="flex items-center gap-2">
            {showAddButton && (
              <Button type="button" onClick={onAdd} size="sm">
                <Plus className="h-4 w-4 mr-1" /> {addButtonText}
              </Button>
            )}
            {showExportButton && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={onExport}
                className="bg-blue-50 hover:bg-blue-100 border-blue-300 text-blue-700"
              >
                <Download className="h-4 w-4 mr-1" /> Export
              </Button>
            )}
          </div>
        )}

        {totalPages > 1 && (
          <div className="flex items-center gap-2 ml-auto">
            <Button
              type="button"
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            >
              Previous
            </Button>
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              // Show pages around current page
              let pageNum: number;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <Button
                  type="button"
                  key={pageNum}
                  variant={currentPage === pageNum ? "default" : "outline"}
                  size="sm"
                  className="w-10"
                  onClick={() => setCurrentPage(pageNum)}
                >
                  {pageNum}
                </Button>
              );
            })}
            <Button
              type="button"
              variant="outline"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExpandableDataTable;
