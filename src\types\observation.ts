export interface MyAction {
  id: string;
  location: string;
  description: string;
  submittedBy: string;
  dueDate: string;
  status?: string;
}

export interface Attachment {
  name: string;
  type: string;
  size: number;
  url: string;
}

export interface FullLocation {
  country: string;
  region: string;
  site: string;
  level: string;
}

export interface ObservationAction {
  id: string;
  actionType: string;
  status: string;
  actionToBeTaken?: string;
  actionTaken?: string;
  dueDate?: string;
  createdDate?: string;
  assignedToId?: string[];
  uploads?: string[];
}

export interface Observation {
  id: string;
  maskId?: string;
  location: string;
  fullLocation: FullLocation;
  category: string;
  type: string;
  observationType?: string;
  actionCondition: string;
  reportedBy: string;
  reportedDate: Date;
  actionAssignee: string | null;
  reviewedBy: string | null;
  status: string;
  description?: string;
  attachments: string[] | Attachment[];
  dueDate?: Date | null;
  rectifiedOnSpot?: boolean | string | null;
  actionTaken?: string | null;
  evidenceImages?: string[] | Attachment[];
  evidence?: string[];
  needsReviewer?: boolean | string | null;
  actionToBeTaken?: string | null;
  comments?: string;
  uploads?: string[];
  submittedBy?: string;
  observationActions?: ObservationAction[];
}
