import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { X, Plus } from 'lucide-react';
import { CreateTrainingRolloutData } from '@/types/changeManagement';

const trainingRolloutSchema = z.object({
  changeRequestId: z.string().min(1, 'Change request is required'),
  trainingTitle: z.string().min(1, 'Training title is required'),
  trainingDescription: z.string().min(1, 'Training description is required'),
  trainingType: z.enum(['Online', 'Classroom', 'On-the-job', 'Workshop', 'Webinar']),
  priority: z.enum(['Low', 'Medium', 'High', 'Critical']),
  scheduledStartDate: z.string().optional(),
  scheduledEndDate: z.string().optional(),
  duration: z.number().min(1, 'Duration must be at least 1 hour'),
  maxParticipants: z.number().optional(),
  location: z.string().optional(),
  isVirtual: z.boolean(),
  meetingLink: z.string().optional(),
  assessmentRequired: z.boolean(),
  passScore: z.number().optional(),
});

interface CreateTrainingRolloutModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreateTrainingRolloutData) => void;
  changeRequests: Array<{ id: string; maskId: string; title: string }>;
  instructors: Array<{ id: string; firstName: string; lastName?: string; email: string }>;
}

const CreateTrainingRolloutModal: React.FC<CreateTrainingRolloutModalProps> = ({
  open,
  onOpenChange,
  onSubmit,
  changeRequests,
  instructors,
}) => {
  const [targetAudience, setTargetAudience] = useState<string[]>([]);
  const [affectedTeams, setAffectedTeams] = useState<string[]>([]);
  const [prerequisites, setPrerequisites] = useState<string[]>([]);
  const [learningObjectives, setLearningObjectives] = useState<string[]>([]);
  const [newAudience, setNewAudience] = useState('');
  const [newTeam, setNewTeam] = useState('');
  const [newPrerequisite, setNewPrerequisite] = useState('');
  const [newObjective, setNewObjective] = useState('');

  const form = useForm<z.infer<typeof trainingRolloutSchema>>({
    resolver: zodResolver(trainingRolloutSchema),
    defaultValues: {
      changeRequestId: '',
      trainingTitle: '',
      trainingDescription: '',
      trainingType: 'Classroom',
      priority: 'Medium',
      scheduledStartDate: '',
      scheduledEndDate: '',
      duration: 1,
      maxParticipants: undefined,
      location: '',
      isVirtual: false,
      meetingLink: '',
      assessmentRequired: false,
      passScore: undefined,
    },
  });

  const handleSubmit = (values: z.infer<typeof trainingRolloutSchema>) => {
    const trainingData: CreateTrainingRolloutData = {
      ...values,
      targetAudience,
      affectedTeams,
      prerequisites,
      learningObjectives,
    };
    onSubmit(trainingData);
    form.reset();
    setTargetAudience([]);
    setAffectedTeams([]);
    setPrerequisites([]);
    setLearningObjectives([]);
    onOpenChange(false);
  };

  const addItem = (item: string, list: string[], setList: (list: string[]) => void, setInput: (value: string) => void) => {
    if (item.trim() && !list.includes(item.trim())) {
      setList([...list, item.trim()]);
      setInput('');
    }
  };

  const removeItem = (item: string, list: string[], setList: (list: string[]) => void) => {
    setList(list.filter(i => i !== item));
  };

  const isVirtual = form.watch('isVirtual');
  const assessmentRequired = form.watch('assessmentRequired');

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Training Rollout</DialogTitle>
          <DialogDescription>
            Set up a new training session for change management implementation.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="changeRequestId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Change Request</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select change request" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {changeRequests.map((cr) => (
                          <SelectItem key={cr.id} value={cr.id}>
                            {cr.maskId} - {cr.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="trainingType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Training Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select training type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Online">Online</SelectItem>
                        <SelectItem value="Classroom">Classroom</SelectItem>
                        <SelectItem value="On-the-job">On-the-job</SelectItem>
                        <SelectItem value="Workshop">Workshop</SelectItem>
                        <SelectItem value="Webinar">Webinar</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Low">Low</SelectItem>
                        <SelectItem value="Medium">Medium</SelectItem>
                        <SelectItem value="High">High</SelectItem>
                        <SelectItem value="Critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Duration (Hours)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="Enter duration in hours"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="scheduledStartDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Scheduled Start Date</FormLabel>
                    <FormControl>
                      <Input type="datetime-local" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="scheduledEndDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Scheduled End Date</FormLabel>
                    <FormControl>
                      <Input type="datetime-local" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="maxParticipants"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Max Participants (Optional)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="Enter max participants"
                        {...field}
                        onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex items-center space-x-2">
                <FormField
                  control={form.control}
                  name="isVirtual"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Virtual Training</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="trainingTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Training Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter training title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {!isVirtual && (
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter training location" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {isVirtual && (
                <FormField
                  control={form.control}
                  name="meetingLink"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meeting Link</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter meeting link" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            <FormField
              control={form.control}
              name="trainingDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Training Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Provide a detailed description of the training"
                      className="min-h-[100px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Target Audience */}
            <div className="space-y-3">
              <FormLabel>Target Audience</FormLabel>
              <div className="flex gap-2">
                <Input
                  placeholder="Add target audience"
                  value={newAudience}
                  onChange={(e) => setNewAudience(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addItem(newAudience, targetAudience, setTargetAudience, setNewAudience))}
                />
                <Button type="button" onClick={() => addItem(newAudience, targetAudience, setTargetAudience, setNewAudience)} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {targetAudience.map((audience) => (
                  <Badge key={audience} variant="secondary" className="flex items-center gap-1">
                    {audience}
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => removeItem(audience, targetAudience, setTargetAudience)}
                    />
                  </Badge>
                ))}
              </div>
            </div>

            {/* Affected Teams */}
            <div className="space-y-3">
              <FormLabel>Affected Teams</FormLabel>
              <div className="flex gap-2">
                <Input
                  placeholder="Add affected team"
                  value={newTeam}
                  onChange={(e) => setNewTeam(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addItem(newTeam, affectedTeams, setAffectedTeams, setNewTeam))}
                />
                <Button type="button" onClick={() => addItem(newTeam, affectedTeams, setAffectedTeams, setNewTeam)} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {affectedTeams.map((team) => (
                  <Badge key={team} variant="secondary" className="flex items-center gap-1">
                    {team}
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => removeItem(team, affectedTeams, setAffectedTeams)}
                    />
                  </Badge>
                ))}
              </div>
            </div>

            {/* Prerequisites */}
            <div className="space-y-3">
              <FormLabel>Prerequisites</FormLabel>
              <div className="flex gap-2">
                <Input
                  placeholder="Add prerequisite"
                  value={newPrerequisite}
                  onChange={(e) => setNewPrerequisite(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addItem(newPrerequisite, prerequisites, setPrerequisites, setNewPrerequisite))}
                />
                <Button type="button" onClick={() => addItem(newPrerequisite, prerequisites, setPrerequisites, setNewPrerequisite)} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {prerequisites.map((prereq) => (
                  <Badge key={prereq} variant="secondary" className="flex items-center gap-1">
                    {prereq}
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => removeItem(prereq, prerequisites, setPrerequisites)}
                    />
                  </Badge>
                ))}
              </div>
            </div>

            {/* Learning Objectives */}
            <div className="space-y-3">
              <FormLabel>Learning Objectives</FormLabel>
              <div className="flex gap-2">
                <Input
                  placeholder="Add learning objective"
                  value={newObjective}
                  onChange={(e) => setNewObjective(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addItem(newObjective, learningObjectives, setLearningObjectives, setNewObjective))}
                />
                <Button type="button" onClick={() => addItem(newObjective, learningObjectives, setLearningObjectives, setNewObjective)} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {learningObjectives.map((objective) => (
                  <Badge key={objective} variant="secondary" className="flex items-center gap-1">
                    {objective}
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => removeItem(objective, learningObjectives, setLearningObjectives)}
                    />
                  </Badge>
                ))}
              </div>
            </div>

            {/* Assessment Section */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="assessmentRequired"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Assessment Required</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              {assessmentRequired && (
                <FormField
                  control={form.control}
                  name="passScore"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pass Score (%)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          placeholder="Enter pass score percentage"
                          min="0"
                          max="100"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit">
                Create Training Rollout
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateTrainingRolloutModal;
