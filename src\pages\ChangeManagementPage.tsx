import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import PageHeader from '@/components/common/PageHeader';
import TabsContainer from '@/components/common/TabsContainer';
import ExpandableDataTable from '@/components/common/ExpandableDataTable';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Search,
  Plus,
  Download,
  Filter,
  CheckCircle,
  Clock
} from 'lucide-react';

import { formatDate } from '@/utils/dateUtils';
import {
  ChangeRequest,
  ImpactAssessment,
  ApprovalWorkflow,
  TrainingRollout,
  ChangeManagementDashboard,
  CreateChangeRequestData,
  CreateImpactAssessmentData,
  CreateTrainingRolloutData
} from '@/types/changeManagement';
import { Action } from '@/services/api';
import CreateChangeRequestModal from '@/components/change-management/CreateChangeRequestModal';
import CreateImpactAssessmentModal from '@/components/change-management/CreateImpactAssessmentModal';
import CreateTrainingRolloutModal from '@/components/change-management/CreateTrainingRolloutModal';
import ChangeManagementDetailsModal from '@/components/change-management/ChangeManagementDetailsModal';

// Extended Action interface for Change Management with UI ID
interface ChangeManagementAction extends Action {
  uiId: string;
}

// Mock data for development
const mockChangeRequests: ChangeRequest[] = [
  {
    id: '1',
    maskId: 'CHG-001',
    title: 'Upgrade Safety Management System',
    description: 'Implement new safety management software to improve incident tracking and reporting',
    changeType: 'Technical',
    priority: 'High',
    status: 'Under Review',
    requestedBy: {
      id: '1',
      firstName: 'John',
      lastName: 'Smith',
      email: '<EMAIL>',
      department: 'IT'
    },
    requestDate: '2024-01-15T10:00:00Z',
    proposedImplementationDate: '2024-02-15T09:00:00Z',
    businessJustification: 'Improve safety compliance and reduce incident response time',
    impactDescription: 'System downtime during migration, user training required',
    rollbackPlan: 'Restore from backup if issues occur',
    affectedSystems: ['Safety Management', 'Incident Reporting'],
    affectedDepartments: ['Safety', 'Operations', 'IT'],
    estimatedCost: 50000,
    estimatedDuration: 40,
    created: '2024-01-15T10:00:00Z',
    updated: '2024-01-16T14:30:00Z'
  },
  {
    id: '2',
    maskId: 'CHG-002',
    title: 'New Emergency Response Protocol',
    description: 'Update emergency response procedures for chemical spill incidents',
    changeType: 'Operational',
    priority: 'Critical',
    status: 'Approved',
    requestedBy: {
      id: '2',
      firstName: 'Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      department: 'Safety'
    },
    requestDate: '2024-01-10T08:00:00Z',
    proposedImplementationDate: '2024-01-25T00:00:00Z',
    businessJustification: 'Comply with new safety regulations and improve response time',
    impactDescription: 'All staff require training on new procedures',
    rollbackPlan: 'Revert to previous protocol if needed',
    affectedSystems: ['Emergency Response'],
    affectedDepartments: ['Safety', 'Operations', 'Maintenance'],
    estimatedDuration: 16,
    created: '2024-01-10T08:00:00Z',
    updated: '2024-01-18T11:15:00Z'
  }
];

const mockImpactAssessments: ImpactAssessment[] = [
  {
    id: '1',
    maskId: 'IA-2024-001',
    changeRequestId: '1',
    assessorId: '3',
    assessor: {
      id: '3',
      firstName: 'Mike',
      lastName: 'Wilson',
      email: '<EMAIL>',
      department: 'Risk Management'
    },
    assessmentDate: '2024-01-16T09:00:00Z',
    riskLevel: 'Medium',
    businessImpact: 'Medium',
    technicalImpact: 'High',
    operationalImpact: 'Medium',
    safetyImpact: 'Low',
    environmentalImpact: 'None',
    complianceImpact: 'Low',
    riskMitigationMeasures: 'Comprehensive testing, phased rollout, backup systems ready',
    recommendations: 'Proceed with implementation during planned maintenance window',
    additionalRequirements: 'Additional IT support during implementation',
    approvalRequired: true,
    stakeholdersToNotify: ['Safety Manager', 'IT Director', 'Operations Manager'],
    estimatedDowntime: 4,
    resourceRequirements: '2 IT specialists, 1 safety coordinator',
    testingRequirements: 'Full system testing in staging environment',
    communicationPlan: 'Email notifications, training sessions, user guides',
    status: 'Completed',
    created: '2024-01-16T09:00:00Z',
    updated: '2024-01-16T16:00:00Z'
  },
  {
    id: '2',
    maskId: 'IA-2024-002',
    changeRequestId: '2',
    assessorId: '4',
    assessor: {
      id: '4',
      firstName: 'Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      department: 'Safety'
    },
    assessmentDate: '2024-01-12T10:30:00Z',
    riskLevel: 'High',
    businessImpact: 'Low',
    technicalImpact: 'Medium',
    operationalImpact: 'High',
    safetyImpact: 'High',
    environmentalImpact: 'Medium',
    complianceImpact: 'High',
    riskMitigationMeasures: 'Emergency response training, safety equipment updates',
    recommendations: 'Implement with enhanced safety protocols',
    additionalRequirements: 'Safety team oversight, emergency response plan',
    approvalRequired: true,
    stakeholdersToNotify: ['Safety Director', 'Emergency Response Team', 'Operations Manager'],
    estimatedDowntime: 2,
    resourceRequirements: '1 safety specialist, 2 operations staff',
    testingRequirements: 'Emergency drill simulation',
    communicationPlan: 'Safety briefings, emergency procedure updates',
    status: 'Reviewed',
    created: '2024-01-12T10:30:00Z',
    updated: '2024-01-15T14:20:00Z'
  }
];

const mockTrainingRollouts: TrainingRollout[] = [
  {
    id: '1',
    maskId: 'TR-2024-001',
    changeRequestId: '2',
    trainingTitle: 'Emergency Response Protocol Training',
    trainingDescription: 'Training on new chemical spill response procedures',
    trainingType: 'Classroom',
    priority: 'High',
    status: 'Scheduled',
    targetAudience: ['All Operations Staff', 'Safety Personnel', 'Maintenance Team'],
    affectedTeams: ['Operations', 'Safety', 'Maintenance'],
    trainingMaterials: ['Protocol Manual', 'Video Demonstrations', 'Practice Scenarios'],
    instructorId: '4',
    instructor: {
      id: '4',
      firstName: 'Lisa',
      lastName: 'Brown',
      email: '<EMAIL>'
    },
    scheduledStartDate: '2024-01-22T09:00:00Z',
    scheduledEndDate: '2024-01-24T17:00:00Z',
    duration: 8,
    maxParticipants: 25,
    location: 'Training Room A',
    isVirtual: false,
    prerequisites: ['Basic Safety Training'],
    learningObjectives: ['Understand new protocol steps', 'Practice emergency response', 'Pass assessment'],
    assessmentRequired: true,
    passScore: 80,
    created: '2024-01-18T10:00:00Z',
    updated: '2024-01-19T14:00:00Z'
  },
  {
    id: '2',
    maskId: 'TR-2024-002',
    changeRequestId: '1',
    trainingTitle: 'Safety System Upgrade Training',
    trainingDescription: 'Training on new safety monitoring system features',
    trainingType: 'Online',
    priority: 'Medium',
    status: 'In Progress',
    targetAudience: ['Safety Personnel', 'System Operators'],
    affectedTeams: ['Safety', 'Operations'],
    trainingMaterials: ['Online Modules', 'System Manual', 'Quick Reference Guide'],
    instructorId: '5',
    instructor: {
      id: '5',
      firstName: 'David',
      lastName: 'Chen',
      email: '<EMAIL>'
    },
    scheduledStartDate: '2024-01-20T08:00:00Z',
    scheduledEndDate: '2024-01-22T16:00:00Z',
    duration: 4,
    maxParticipants: 50,
    location: 'Online Platform',
    isVirtual: true,
    prerequisites: ['System Access', 'Basic Computer Skills'],
    learningObjectives: ['Navigate new interface', 'Configure alerts', 'Generate reports'],
    assessmentRequired: true,
    passScore: 75,
    created: '2024-01-15T11:00:00Z',
    updated: '2024-01-20T09:30:00Z'
  }
];

const mockDashboard: ChangeManagementDashboard = {
  totalChanges: 15,
  pendingApprovals: 3,
  activeTrainings: 2,
  completedChanges: 8,
  changesByStatus: [
    { status: 'Draft', count: 2 },
    { status: 'Under Review', count: 3 },
    { status: 'Approved', count: 4 },
    { status: 'Implemented', count: 6 }
  ],
  changesByType: [
    { type: 'Technical', count: 6 },
    { type: 'Operational', count: 5 },
    { type: 'Organizational', count: 3 },
    { type: 'Emergency', count: 1 }
  ],
  riskDistribution: [
    { riskLevel: 'Low', count: 4 },
    { riskLevel: 'Medium', count: 7 },
    { riskLevel: 'High', count: 3 },
    { riskLevel: 'Critical', count: 1 }
  ],
  upcomingDeadlines: [
    { id: '1', title: 'Safety System Upgrade', type: 'Change', dueDate: '2024-02-15T09:00:00Z' },
    { id: '2', title: 'Emergency Protocol Training', type: 'Training', dueDate: '2024-01-24T17:00:00Z' }
  ]
};

// Mock actions data for Change Management
const mockChangeManagementActions: ChangeManagementAction[] = [
  {
    id: '1',
    uiId: 'CGM-ACT-001',
    title: 'Review Safety System Upgrade Change Request',
    description: 'Conduct technical review of the proposed safety management system upgrade',
    status: 'Pending',
    priority: 'High',
    dueDate: '2024-02-10T17:00:00Z',
    application: 'Change Management',
    serviceId: 'cgm-001',
    assignedTo: 'Current User',
    createdBy: 'John Smith',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-16T14:30:00Z'
  },
  {
    id: '2',
    uiId: 'CGM-ACT-002',
    title: 'Complete Impact Assessment for Emergency Protocol',
    description: 'Evaluate the impact of new emergency response procedures on operations',
    status: 'In Progress',
    priority: 'Critical',
    dueDate: '2024-01-25T12:00:00Z',
    application: 'Change Management',
    serviceId: 'cgm-002',
    assignedTo: 'Current User',
    createdBy: 'Sarah Johnson',
    createdAt: '2024-01-10T08:00:00Z',
    updatedAt: '2024-01-18T11:15:00Z'
  },
  {
    id: '3',
    uiId: 'CGM-ACT-003',
    title: 'Approve Training Rollout Plan',
    description: 'Review and approve the training plan for emergency response protocol changes',
    status: 'Pending',
    priority: 'Medium',
    dueDate: '2024-01-30T16:00:00Z',
    application: 'Change Management',
    serviceId: 'cgm-003',
    assignedTo: 'Current User',
    createdBy: 'Mike Wilson',
    createdAt: '2024-01-18T09:00:00Z',
    updatedAt: '2024-01-19T10:30:00Z'
  },
  {
    id: '4',
    uiId: 'CGM-ACT-004',
    title: 'Conduct Risk Assessment for Equipment Upgrade',
    description: 'Assess risks associated with upgrading manufacturing equipment',
    status: 'Completed',
    priority: 'High',
    dueDate: '2024-01-20T15:00:00Z',
    application: 'Change Management',
    serviceId: 'cgm-004',
    assignedTo: 'Current User',
    createdBy: 'Lisa Brown',
    createdAt: '2024-01-12T14:00:00Z',
    updatedAt: '2024-01-20T14:45:00Z'
  },
  {
    id: '5',
    uiId: 'CGM-ACT-005',
    title: 'Validate Change Implementation Documentation',
    description: 'Review and validate documentation for implemented process changes',
    status: 'Pending',
    priority: 'Low',
    dueDate: '2024-02-05T10:00:00Z',
    application: 'Change Management',
    serviceId: 'cgm-005',
    assignedTo: 'Current User',
    createdBy: 'David Chen',
    createdAt: '2024-01-22T11:00:00Z',
    updatedAt: '2024-01-22T11:00:00Z'
  },
  {
    id: '6',
    uiId: 'CGM-ACT-006',
    title: 'Stakeholder Approval for IT Infrastructure Change',
    description: 'Provide stakeholder approval for proposed IT infrastructure modifications',
    status: 'Overdue',
    priority: 'Critical',
    dueDate: '2024-01-18T09:00:00Z',
    application: 'Change Management',
    serviceId: 'cgm-006',
    assignedTo: 'Current User',
    createdBy: 'Emma Davis',
    createdAt: '2024-01-08T13:00:00Z',
    updatedAt: '2024-01-15T16:20:00Z'
  }
];

// Mock approval workflows data
const mockApprovalWorkflows: ApprovalWorkflow[] = [
  {
    id: '1',
    maskId: 'AW-2024-001',
    changeRequestId: '1',
    workflowName: 'Safety System Upgrade Approval',
    currentStage: 2,
    totalStages: 4,
    status: 'In Progress',
    created: '2024-01-15T10:00:00Z',
    updated: '2024-01-18T14:30:00Z'
  },
  {
    id: '2',
    maskId: 'AW-2024-002',
    changeRequestId: '2',
    workflowName: 'Emergency Protocol Approval',
    currentStage: 4,
    totalStages: 4,
    status: 'Approved',
    created: '2024-01-10T08:00:00Z',
    updated: '2024-01-20T16:45:00Z'
  },
  {
    id: '3',
    maskId: 'AW-2024-003',
    changeRequestId: '3',
    workflowName: 'Equipment Maintenance Approval',
    currentStage: 1,
    totalStages: 3,
    status: 'Pending',
    created: '2024-01-19T14:00:00Z',
    updated: '2024-01-19T14:00:00Z'
  }
];

const ChangeManagementPage = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("my-actions");
  const [changeRequests, setChangeRequests] = useState<ChangeRequest[]>(mockChangeRequests);
  const [impactAssessments, setImpactAssessments] = useState<ImpactAssessment[]>(mockImpactAssessments);
  const [approvalWorkflows, setApprovalWorkflows] = useState<ApprovalWorkflow[]>(mockApprovalWorkflows);
  const [trainingRollouts, setTrainingRollouts] = useState<TrainingRollout[]>(mockTrainingRollouts);
  const [actions, setActions] = useState<ChangeManagementAction[]>(mockChangeManagementActions);
  const [dashboard, setDashboard] = useState<ChangeManagementDashboard>(mockDashboard);
  const [loading, setLoading] = useState({
    actions: false,
    changes: false,
    assessments: false,
    trainings: false,
    dashboard: false,
  });
  const [searchTerms, setSearchTerms] = useState({
    actions: '',
    changes: '',
    assessments: '',
    trainings: '',
  });

  // Modal states
  const [showCreateChangeModal, setShowCreateChangeModal] = useState(false);
  const [showCreateAssessmentModal, setShowCreateAssessmentModal] = useState(false);
  const [showCreateTrainingModal, setShowCreateTrainingModal] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedItemType, setSelectedItemType] = useState<'change-request' | 'impact-assessment' | 'approval-workflow' | 'training-rollout' | null>(null);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [selectedItemData, setSelectedItemData] = useState<ChangeRequest | ImpactAssessment | ApprovalWorkflow | TrainingRollout | null>(null);

  // Create unified My Actions data from all tabs
  const getMyActionsData = () => {
    const myActions: any[] = [];

    // Add Change Requests that need action (not Completed, Closed, or Rejected)
    changeRequests
      .filter(cr => !['Implemented', 'Closed', 'Rejected'].includes(cr.status))
      .forEach(cr => {
        myActions.push({
          id: cr.id,
          maskId: cr.maskId,
          title: `Review Change Request: ${cr.title}`,
          description: cr.description,
          type: 'Change Request',
          status: cr.status === 'Draft' ? 'Pending' : cr.status === 'Under Review' ? 'In Progress' : cr.status,
          priority: cr.priority,
          dueDate: cr.proposedImplementationDate,
          createdBy: `${cr.requestedBy.firstName} ${cr.requestedBy.lastName}`,
          createdAt: cr.created,
          originalData: cr,
          actionType: 'change-request'
        });
      });

    // Add Impact Assessments that need action (not Completed or Approved)
    impactAssessments
      .filter(ia => !['Completed', 'Approved'].includes(ia.status))
      .forEach(ia => {
        myActions.push({
          id: ia.id,
          maskId: ia.maskId,
          title: `Complete Impact Assessment`,
          description: `Risk assessment for change request`,
          type: 'Impact Assessment',
          status: ia.status === 'Draft' ? 'Pending' : ia.status === 'Reviewed' ? 'In Progress' : ia.status,
          priority: ia.riskLevel,
          dueDate: ia.assessmentDate,
          createdBy: `${ia.assessor.firstName} ${ia.assessor.lastName}`,
          createdAt: ia.created,
          originalData: ia,
          actionType: 'impact-assessment'
        });
      });

    // Add Approval Workflows that need action (not Approved, Rejected, or Cancelled)
    approvalWorkflows
      .filter(aw => !['Approved', 'Rejected', 'Cancelled'].includes(aw.status))
      .forEach(aw => {
        myActions.push({
          id: aw.id,
          maskId: aw.maskId,
          title: `Process Approval Workflow: ${aw.workflowName}`,
          description: `Stage ${aw.currentStage} of ${aw.totalStages}`,
          type: 'Approval Workflow',
          status: aw.status === 'Pending' ? 'Pending' : 'In Progress',
          priority: 'Medium', // Default priority for workflows
          dueDate: aw.updated, // Use updated date as reference
          createdBy: 'System',
          createdAt: aw.created,
          originalData: aw,
          actionType: 'approval-workflow'
        });
      });

    // Add Training Rollouts that need action (not Completed or Cancelled)
    trainingRollouts
      .filter(tr => !['Completed', 'Cancelled'].includes(tr.status))
      .forEach(tr => {
        myActions.push({
          id: tr.id,
          maskId: tr.maskId,
          title: `Manage Training: ${tr.trainingTitle}`,
          description: tr.trainingDescription,
          type: 'Training Rollout',
          status: tr.status === 'Planned' ? 'Pending' : tr.status === 'Scheduled' ? 'Pending' : 'In Progress',
          priority: tr.priority,
          dueDate: tr.scheduledStartDate || tr.created,
          createdBy: tr.instructor ? `${tr.instructor.firstName} ${tr.instructor.lastName}` : 'System',
          createdAt: tr.created,
          originalData: tr,
          actionType: 'training-rollout'
        });
      });

    return myActions.sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());
  };

  const myActionsData = getMyActionsData();

  // Handlers
  const handleOpenDetailsModal = (
    type: 'change-request' | 'impact-assessment' | 'approval-workflow' | 'training-rollout',
    id: string,
    data: ChangeRequest | ImpactAssessment | ApprovalWorkflow | TrainingRollout
  ) => {
    setSelectedItemType(type);
    setSelectedItemId(id);
    setSelectedItemData(data);
    setIsDetailsModalOpen(true);
  };

  const handleCreateChangeRequest = (data: CreateChangeRequestData) => {
    console.log('Creating change request:', data);
    toast({
      title: "Success",
      description: "Change request created successfully",
    });
  };

  const handleCreateImpactAssessment = (data: CreateImpactAssessmentData) => {
    console.log('Creating impact assessment:', data);
    toast({
      title: "Success",
      description: "Impact assessment created successfully",
    });
  };

  const handleCreateTrainingRollout = (data: CreateTrainingRolloutData) => {
    console.log('Creating training rollout:', data);
    toast({
      title: "Success",
      description: "Training rollout created successfully",
    });
  };

  // Status badge styling
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'Draft': { variant: 'secondary' as const, color: 'bg-gray-100 text-gray-800' },
      'Submitted': { variant: 'default' as const, color: 'bg-blue-100 text-blue-800' },
      'Under Review': { variant: 'default' as const, color: 'bg-yellow-100 text-yellow-800' },
      'Approved': { variant: 'default' as const, color: 'bg-green-100 text-green-800' },
      'Rejected': { variant: 'destructive' as const, color: 'bg-red-100 text-red-800' },
      'Implemented': { variant: 'default' as const, color: 'bg-purple-100 text-purple-800' },
      'Closed': { variant: 'secondary' as const, color: 'bg-gray-100 text-gray-800' },
      'Pending': { variant: 'default' as const, color: 'bg-orange-100 text-orange-800' },
      'Completed': { variant: 'default' as const, color: 'bg-green-100 text-green-800' },
      'Scheduled': { variant: 'default' as const, color: 'bg-blue-100 text-blue-800' },
      'In Progress': { variant: 'default' as const, color: 'bg-yellow-100 text-yellow-800' },
      'Cancelled': { variant: 'secondary' as const, color: 'bg-gray-100 text-gray-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['Draft'];
    return (
      <Badge variant={config.variant} className={config.color}>
        {status}
      </Badge>
    );
  };

  // Priority badge styling
  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      'Low': { variant: 'secondary' as const, color: 'bg-gray-100 text-gray-800' },
      'Medium': { variant: 'default' as const, color: 'bg-yellow-100 text-yellow-800' },
      'High': { variant: 'default' as const, color: 'bg-orange-100 text-orange-800' },
      'Critical': { variant: 'destructive' as const, color: 'bg-red-100 text-red-800' },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig['Low'];
    return (
      <Badge variant={config.variant} className={config.color}>
        {priority}
      </Badge>
    );
  };

  // Risk level badge styling
  const getRiskBadge = (riskLevel: string) => {
    const riskConfig = {
      'Low': { variant: 'default' as const, color: 'bg-green-100 text-green-800' },
      'Medium': { variant: 'default' as const, color: 'bg-yellow-100 text-yellow-800' },
      'High': { variant: 'default' as const, color: 'bg-orange-100 text-orange-800' },
      'Critical': { variant: 'destructive' as const, color: 'bg-red-100 text-red-800' },
    };

    const config = riskConfig[riskLevel as keyof typeof riskConfig] || riskConfig['Low'];
    return (
      <Badge variant={config.variant} className={config.color}>
        {riskLevel}
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      <PageHeader
        title="Change Management"
        description="Safely implement operational changes with impact assessments, approval workflows, and training rollouts"
      />

      <TabsContainer
        tabs={[
          {
            value: "my-actions",
            label: `My Actions (${myActionsData.length})`,
            content: (
              <div className="space-y-4">
                <div className="flex items-center justify-between gap-4 mb-4">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search my actions..."
                      value={searchTerms.actions}
                      onChange={(e) => setSearchTerms(prev => ({ ...prev, actions: e.target.value }))}
                      className="pl-10"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>

                <ExpandableDataTable
                  data={myActionsData.filter(action =>
                    action.title.toLowerCase().includes(searchTerms.actions.toLowerCase()) ||
                    action.description.toLowerCase().includes(searchTerms.actions.toLowerCase()) ||
                    action.status.toLowerCase().includes(searchTerms.actions.toLowerCase()) ||
                    action.type.toLowerCase().includes(searchTerms.actions.toLowerCase())
                  )}
                  columns={[
                    {
                      key: "maskId",
                      header: "ID",
                      sortable: true,
                      render: (value, row) => (
                        <span
                          className="font-medium text-blue-600 hover:text-blue-800 cursor-pointer hover:underline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenDetailsModal(row.actionType, row.id, row.originalData);
                          }}
                        >
                          {value}
                        </span>
                      ),
                    },
                    {
                      key: "type",
                      header: "Type",
                      sortable: true,
                      filterable: true,
                      filterType: 'select',
                      filterOptions: [
                        { label: 'Change Request', value: 'Change Request' },
                        { label: 'Impact Assessment', value: 'Impact Assessment' },
                        { label: 'Approval Workflow', value: 'Approval Workflow' },
                        { label: 'Training Rollout', value: 'Training Rollout' }
                      ],
                      render: (value) => (
                        <Badge variant="outline" className="text-xs">
                          {value}
                        </Badge>
                      ),
                    },
                    {
                      key: "title",
                      header: "Action Required",
                      sortable: true,
                      render: (value, row) => (
                        <div>
                          <p className="font-medium">{value}</p>
                          <p className="text-sm text-muted-foreground truncate max-w-xs">
                            {row.description}
                          </p>
                        </div>
                      ),
                    },
                    {
                      key: "status",
                      header: "Status",
                      sortable: true,
                      filterable: true,
                      filterType: 'select',
                      filterOptions: [
                        { label: 'Pending', value: 'Pending' },
                        { label: 'In Progress', value: 'In Progress' },
                        { label: 'Completed', value: 'Completed' },
                        { label: 'Overdue', value: 'Overdue' }
                      ],
                      render: (value) => {
                        const statusConfig = {
                          'Pending': { variant: 'default' as const, color: 'bg-yellow-100 text-yellow-800' },
                          'In Progress': { variant: 'default' as const, color: 'bg-blue-100 text-blue-800' },
                          'Completed': { variant: 'default' as const, color: 'bg-green-100 text-green-800' },
                          'Overdue': { variant: 'destructive' as const, color: 'bg-red-100 text-red-800' },
                        };
                        const config = statusConfig[value as keyof typeof statusConfig] || statusConfig['Pending'];
                        return (
                          <Badge variant={config.variant} className={config.color}>
                            {value}
                          </Badge>
                        );
                      },
                    },
                    {
                      key: "priority",
                      header: "Priority",
                      sortable: true,
                      filterable: true,
                      filterType: 'select',
                      filterOptions: [
                        { label: 'Low', value: 'Low' },
                        { label: 'Medium', value: 'Medium' },
                        { label: 'High', value: 'High' },
                        { label: 'Critical', value: 'Critical' }
                      ],
                      render: (value) => getPriorityBadge(value),
                    },
                    {
                      key: "dueDate",
                      header: "Due Date",
                      sortable: true,
                      render: (value) => (
                        <div>
                          <p className="text-sm">{formatDate(value)}</p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(value) < new Date() ? 'Overdue' : 'Due'}
                          </p>
                        </div>
                      ),
                    },
                    {
                      key: "createdBy",
                      header: "Created By",
                      render: (value) => (
                        <div>
                          <p className="font-medium">{value}</p>
                          <p className="text-sm text-muted-foreground">Change Management</p>
                        </div>
                      ),
                    },
                    {
                      key: "createdAt",
                      header: "Created Date",
                      sortable: true,
                      render: (value) => (
                        <div>
                          <p className="text-sm">{formatDate(value)}</p>
                        </div>
                      ),
                    },
                  ]}
                  onRowClick={(row) => {
                    handleOpenDetailsModal(row.actionType, row.id, row.originalData);
                  }}
                  showActions={false}
                />
              </div>
            )
          },
          {
            value: "change-requests",
            label: `Change Requests (${changeRequests.length})`,
            content: (
              <div className="space-y-4">
                <div className="flex items-center justify-between gap-4 mb-4">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search change requests..."
                      value={searchTerms.changes}
                      onChange={(e) => setSearchTerms(prev => ({ ...prev, changes: e.target.value }))}
                      className="pl-10"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => setShowCreateChangeModal(true)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      New Change Request
                    </Button>
                  </div>
                </div>

                <ExpandableDataTable
                  data={changeRequests.filter(change =>
                    change.title.toLowerCase().includes(searchTerms.changes.toLowerCase()) ||
                    change.maskId.toLowerCase().includes(searchTerms.changes.toLowerCase()) ||
                    change.description.toLowerCase().includes(searchTerms.changes.toLowerCase())
                  )}
                  columns={[
                    {
                      key: "maskId",
                      header: "Change ID",
                      sortable: true,
                      render: (value, row) => (
                        <span
                          className="font-medium text-blue-600 hover:text-blue-800 cursor-pointer hover:underline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenDetailsModal('change-request', row.id, row);
                          }}
                        >
                          {value}
                        </span>
                      ),
                    },
                    {
                      key: "title",
                      header: "Title",
                      sortable: true,
                      render: (value, row) => (
                        <div>
                          <p className="font-medium">{value}</p>
                          <p className="text-sm text-muted-foreground truncate max-w-xs">
                            {row.description}
                          </p>
                        </div>
                      ),
                    },
                    {
                      key: "changeType",
                      header: "Type",
                      sortable: true,
                      filterable: true,
                      filterType: 'select',
                      filterOptions: [
                        { label: 'Technical', value: 'Technical' },
                        { label: 'Operational', value: 'Operational' },
                        { label: 'Organizational', value: 'Organizational' },
                        { label: 'Emergency', value: 'Emergency' }
                      ],
                      render: (value) => (
                        <Badge variant="outline">{value}</Badge>
                      ),
                    },
                    {
                      key: "priority",
                      header: "Priority",
                      sortable: true,
                      filterable: true,
                      filterType: 'select',
                      filterOptions: [
                        { label: 'Low', value: 'Low' },
                        { label: 'Medium', value: 'Medium' },
                        { label: 'High', value: 'High' },
                        { label: 'Critical', value: 'Critical' }
                      ],
                      render: (value) => getPriorityBadge(value),
                    },
                    {
                      key: "status",
                      header: "Status",
                      sortable: true,
                      filterable: true,
                      filterType: 'select',
                      filterOptions: [
                        { label: 'Draft', value: 'Draft' },
                        { label: 'Submitted', value: 'Submitted' },
                        { label: 'Under Review', value: 'Under Review' },
                        { label: 'Approved', value: 'Approved' },
                        { label: 'Rejected', value: 'Rejected' },
                        { label: 'Implemented', value: 'Implemented' },
                        { label: 'Closed', value: 'Closed' }
                      ],
                      render: (value) => getStatusBadge(value),
                    },
                    {
                      key: "requestedBy",
                      header: "Requested By",
                      render: (value, row) => (
                        <div>
                          <p className="font-medium">
                            {value.firstName} {value.lastName}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {value.department}
                          </p>
                        </div>
                      ),
                    },
                    {
                      key: "proposedImplementationDate",
                      header: "Implementation Date",
                      sortable: true,
                      render: (value) => (
                        <div>
                          <p className="text-sm">{formatDate(value)}</p>
                          <p className="text-xs text-muted-foreground">Proposed</p>
                        </div>
                      ),
                    },
                  ]}
                  onRowClick={(row) => {
                    handleOpenDetailsModal('change-request', row.id, row);
                  }}
                  showActions={true}
                  onEdit={(item) => {
                    console.log('Edit change request:', item);
                  }}
                  onDelete={(item) => {
                    console.log('Delete change request:', item);
                  }}
                />
              </div>
            )
          },
          {
            value: "impact-assessments",
            label: `Impact Assessments (${impactAssessments.length})`,
            content: (
              <div className="space-y-4">
                <div className="flex items-center justify-between gap-4 mb-4">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search impact assessments..."
                      value={searchTerms.assessments}
                      onChange={(e) => setSearchTerms(prev => ({ ...prev, assessments: e.target.value }))}
                      className="pl-10"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => setShowCreateAssessmentModal(true)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      New Assessment
                    </Button>
                  </div>
                </div>

                <ExpandableDataTable
                  data={impactAssessments.filter(assessment =>
                    assessment.assessor.firstName.toLowerCase().includes(searchTerms.assessments.toLowerCase()) ||
                    assessment.assessor.lastName?.toLowerCase().includes(searchTerms.assessments.toLowerCase()) ||
                    assessment.riskLevel.toLowerCase().includes(searchTerms.assessments.toLowerCase())
                  )}
                  columns={[
                    {
                      key: "maskId",
                      header: "Assessment ID",
                      sortable: true,
                      render: (value, row) => (
                        <span
                          className="font-medium text-blue-600 hover:text-blue-800 cursor-pointer hover:underline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenDetailsModal('impact-assessment', row.id, row);
                          }}
                        >
                          {value}
                        </span>
                      ),
                    },
                    {
                      key: "changeRequestId",
                      header: "Change Request",
                      render: (value, row) => {
                        const changeRequest = changeRequests.find(cr => cr.id === value);
                        return (
                          <div>
                            <p className="font-medium text-blue-600">
                              {changeRequest?.maskId || 'N/A'}
                            </p>
                            <p className="text-sm text-muted-foreground truncate max-w-xs">
                              {changeRequest?.title || 'Unknown Change'}
                            </p>
                          </div>
                        );
                      },
                    },
                    {
                      key: "assessor",
                      header: "Assessor",
                      render: (value) => (
                        <div>
                          <p className="font-medium">
                            {value.firstName} {value.lastName}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {value.department}
                          </p>
                        </div>
                      ),
                    },
                    {
                      key: "assessmentDate",
                      header: "Assessment Date",
                      sortable: true,
                      render: (value) => (
                        <div>
                          <p className="text-sm">{formatDate(value)}</p>
                        </div>
                      ),
                    },
                    {
                      key: "riskLevel",
                      header: "Risk Level",
                      sortable: true,
                      filterable: true,
                      filterType: 'select',
                      filterOptions: [
                        { label: 'Low', value: 'Low' },
                        { label: 'Medium', value: 'Medium' },
                        { label: 'High', value: 'High' },
                        { label: 'Critical', value: 'Critical' }
                      ],
                      render: (value) => getRiskBadge(value),
                    },
                    {
                      key: "businessImpact",
                      header: "Business Impact",
                      render: (value) => getRiskBadge(value),
                    },
                    {
                      key: "safetyImpact",
                      header: "Safety Impact",
                      render: (value) => (
                        <Badge variant={value === 'None' ? 'secondary' : 'default'}
                               className={value === 'Critical' ? 'bg-red-100 text-red-800' :
                                         value === 'High' ? 'bg-orange-100 text-orange-800' :
                                         value === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                         value === 'Low' ? 'bg-green-100 text-green-800' :
                                         'bg-gray-100 text-gray-800'}>
                          {value}
                        </Badge>
                      ),
                    },
                    {
                      key: "status",
                      header: "Status",
                      sortable: true,
                      filterable: true,
                      filterType: 'select',
                      filterOptions: [
                        { label: 'Draft', value: 'Draft' },
                        { label: 'Completed', value: 'Completed' },
                        { label: 'Reviewed', value: 'Reviewed' },
                        { label: 'Approved', value: 'Approved' }
                      ],
                      render: (value) => getStatusBadge(value),
                    },
                  ]}
                  onRowClick={(row) => {
                    handleOpenDetailsModal('impact-assessment', row.id, row);
                  }}
                  showActions={true}
                  onEdit={(item) => {
                    console.log('Edit impact assessment:', item);
                  }}
                  onDelete={(item) => {
                    console.log('Delete impact assessment:', item);
                  }}
                />
              </div>
            )
          },
          {
            value: "approval-workflows",
            label: `Approval Workflows (${approvalWorkflows.length})`,
            content: (
              <div className="space-y-4">
                <div className="flex items-center justify-between gap-4 mb-4">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search approval workflows..."
                      className="pl-10"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>

                <ExpandableDataTable
                  data={approvalWorkflows}
                  columns={[
                    {
                      key: "maskId",
                      header: "Workflow ID",
                      sortable: true,
                      render: (value, row) => (
                        <span
                          className="font-medium text-blue-600 hover:text-blue-800 cursor-pointer hover:underline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenDetailsModal('approval-workflow', row.id, row);
                          }}
                        >
                          {value}
                        </span>
                      ),
                    },
                    {
                      key: "changeRequestId",
                      header: "Change Request",
                      render: (value) => {
                        const changeRequest = changeRequests.find(cr => cr.id === value);
                        return (
                          <div>
                            <p className="font-medium text-blue-600">
                              {changeRequest?.maskId || 'N/A'}
                            </p>
                            <p className="text-sm text-muted-foreground truncate max-w-xs">
                              {changeRequest?.title || 'Unknown Change'}
                            </p>
                          </div>
                        );
                      },
                    },
                    {
                      key: "workflowName",
                      header: "Workflow Name",
                      sortable: true,
                      render: (value) => (
                        <p className="font-medium">{value}</p>
                      ),
                    },
                    {
                      key: "currentStage",
                      header: "Progress",
                      render: (value, row) => (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span>Stage {value} of {row.totalStages}</span>
                            <span className="text-muted-foreground">
                              {Math.round((value / row.totalStages) * 100)}%
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${(value / row.totalStages) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                      ),
                    },
                    {
                      key: "status",
                      header: "Status",
                      sortable: true,
                      filterable: true,
                      filterType: 'select',
                      filterOptions: [
                        { label: 'Pending', value: 'Pending' },
                        { label: 'In Progress', value: 'In Progress' },
                        { label: 'Approved', value: 'Approved' },
                        { label: 'Rejected', value: 'Rejected' },
                        { label: 'Cancelled', value: 'Cancelled' }
                      ],
                      render: (value) => getStatusBadge(value),
                    },
                    {
                      key: "created",
                      header: "Created Date",
                      sortable: true,
                      render: (value) => (
                        <div>
                          <p className="text-sm">{formatDate(value)}</p>
                        </div>
                      ),
                    },
                    {
                      key: "updated",
                      header: "Last Updated",
                      sortable: true,
                      render: (value) => (
                        <div>
                          <p className="text-sm">{formatDate(value)}</p>
                        </div>
                      ),
                    },
                  ]}
                  onRowClick={(row) => {
                    handleOpenDetailsModal('approval-workflow', row.id, row);
                  }}
                  showActions={true}
                  onEdit={(item) => {
                    console.log('Edit approval workflow:', item);
                  }}
                  onDelete={(item) => {
                    console.log('Delete approval workflow:', item);
                  }}
                />
              </div>
            )
          },
          {
            value: "training-rollouts",
            label: `Training Rollouts (${trainingRollouts.length})`,
            content: (
              <div className="space-y-4">
                <div className="flex items-center justify-between gap-4 mb-4">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search training rollouts..."
                      value={searchTerms.trainings}
                      onChange={(e) => setSearchTerms(prev => ({ ...prev, trainings: e.target.value }))}
                      className="pl-10"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => setShowCreateTrainingModal(true)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      New Training
                    </Button>
                  </div>
                </div>

                <ExpandableDataTable
                  data={trainingRollouts.filter(training =>
                    training.trainingTitle.toLowerCase().includes(searchTerms.trainings.toLowerCase()) ||
                    training.trainingDescription.toLowerCase().includes(searchTerms.trainings.toLowerCase()) ||
                    training.instructor?.firstName.toLowerCase().includes(searchTerms.trainings.toLowerCase())
                  )}
                  columns={[
                    {
                      key: "maskId",
                      header: "Training ID",
                      sortable: true,
                      render: (value, row) => (
                        <span
                          className="font-medium text-blue-600 hover:text-blue-800 cursor-pointer hover:underline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenDetailsModal('training-rollout', row.id, row);
                          }}
                        >
                          {value}
                        </span>
                      ),
                    },
                    {
                      key: "changeRequestId",
                      header: "Change Request",
                      render: (value, row) => {
                        const changeRequest = changeRequests.find(cr => cr.id === value);
                        return (
                          <div>
                            <p className="font-medium text-blue-600">
                              {changeRequest?.maskId || 'N/A'}
                            </p>
                            <p className="text-sm text-muted-foreground truncate max-w-xs">
                              {changeRequest?.title || 'Unknown Change'}
                            </p>
                          </div>
                        );
                      },
                    },
                    {
                      key: "trainingTitle",
                      header: "Training Title",
                      sortable: true,
                      render: (value, row) => (
                        <div>
                          <p className="font-medium">{value}</p>
                          <p className="text-sm text-muted-foreground truncate max-w-xs">
                            {row.trainingDescription}
                          </p>
                        </div>
                      ),
                    },
                    {
                      key: "trainingType",
                      header: "Type",
                      sortable: true,
                      filterable: true,
                      filterType: 'select',
                      filterOptions: [
                        { label: 'Online', value: 'Online' },
                        { label: 'Classroom', value: 'Classroom' },
                        { label: 'On-the-job', value: 'On-the-job' },
                        { label: 'Workshop', value: 'Workshop' },
                        { label: 'Webinar', value: 'Webinar' }
                      ],
                      render: (value) => (
                        <Badge variant="outline">{value}</Badge>
                      ),
                    },
                    {
                      key: "priority",
                      header: "Priority",
                      sortable: true,
                      filterable: true,
                      filterType: 'select',
                      filterOptions: [
                        { label: 'Low', value: 'Low' },
                        { label: 'Medium', value: 'Medium' },
                        { label: 'High', value: 'High' },
                        { label: 'Critical', value: 'Critical' }
                      ],
                      render: (value) => getPriorityBadge(value),
                    },
                    {
                      key: "status",
                      header: "Status",
                      sortable: true,
                      filterable: true,
                      filterType: 'select',
                      filterOptions: [
                        { label: 'Planned', value: 'Planned' },
                        { label: 'Scheduled', value: 'Scheduled' },
                        { label: 'In Progress', value: 'In Progress' },
                        { label: 'Completed', value: 'Completed' },
                        { label: 'Cancelled', value: 'Cancelled' }
                      ],
                      render: (value) => getStatusBadge(value),
                    },
                    {
                      key: "instructor",
                      header: "Instructor",
                      render: (value) => (
                        <div>
                          {value ? (
                            <>
                              <p className="font-medium">
                                {value.firstName} {value.lastName}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {value.email}
                              </p>
                            </>
                          ) : (
                            <span className="text-muted-foreground">Not assigned</span>
                          )}
                        </div>
                      ),
                    },
                    {
                      key: "scheduledStartDate",
                      header: "Start Date",
                      sortable: true,
                      render: (value) => (
                        <div>
                          {value ? (
                            <>
                              <p className="text-sm">{formatDate(value)}</p>
                              <p className="text-xs text-muted-foreground">Scheduled</p>
                            </>
                          ) : (
                            <span className="text-muted-foreground">Not scheduled</span>
                          )}
                        </div>
                      ),
                    },
                    {
                      key: "duration",
                      header: "Duration",
                      render: (value) => (
                        <span className="text-sm">{value} hours</span>
                      ),
                    },
                    {
                      key: "targetAudience",
                      header: "Target Audience",
                      render: (value) => (
                        <div className="flex flex-wrap gap-1">
                          {value.slice(0, 2).map((audience: string, index: number) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {audience}
                            </Badge>
                          ))}
                          {value.length > 2 && (
                            <Badge variant="secondary" className="text-xs">
                              +{value.length - 2} more
                            </Badge>
                          )}
                        </div>
                      ),
                    },
                  ]}
                  onRowClick={(row) => {
                    handleOpenDetailsModal('training-rollout', row.id, row);
                  }}
                  showActions={true}
                  onEdit={(item) => {
                    console.log('Edit training rollout:', item);
                  }}
                  onDelete={(item) => {
                    console.log('Delete training rollout:', item);
                  }}
                />
              </div>
            )
          }
        ]}
        defaultValue="my-actions"
        onValueChange={(value) => setActiveTab(value)}
      />

      {/* Modals */}
      <CreateChangeRequestModal
        open={showCreateChangeModal}
        onOpenChange={setShowCreateChangeModal}
        onSubmit={handleCreateChangeRequest}
      />

      <CreateImpactAssessmentModal
        open={showCreateAssessmentModal}
        onOpenChange={setShowCreateAssessmentModal}
        onSubmit={handleCreateImpactAssessment}
        changeRequests={changeRequests.map(cr => ({
          id: cr.id,
          maskId: cr.maskId,
          title: cr.title
        }))}
      />

      <CreateTrainingRolloutModal
        open={showCreateTrainingModal}
        onOpenChange={setShowCreateTrainingModal}
        onSubmit={handleCreateTrainingRollout}
        changeRequests={changeRequests.map(cr => ({
          id: cr.id,
          maskId: cr.maskId,
          title: cr.title
        }))}
        instructors={[
          { id: '1', firstName: 'Lisa', lastName: 'Brown', email: '<EMAIL>' },
          { id: '2', firstName: 'Mike', lastName: 'Wilson', email: '<EMAIL>' },
          { id: '3', firstName: 'Sarah', lastName: 'Johnson', email: '<EMAIL>' }
        ]}
      />

      {/* Change Management Details Modal */}
      <ChangeManagementDetailsModal
        open={isDetailsModalOpen}
        onOpenChange={setIsDetailsModalOpen}
        itemType={selectedItemType}
        itemId={selectedItemId}
        data={selectedItemData}
      />
    </div>
  );
};

export default ChangeManagementPage;
