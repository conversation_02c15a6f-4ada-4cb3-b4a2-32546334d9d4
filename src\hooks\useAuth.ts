import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store';
import { setLogin, setLogout } from '@/store/slices/authSlice';

export const useAuth = () => {
  const dispatch = useDispatch();
  const { isAuthenticated, accessToken, refreshToken } = useSelector((state: RootState) => state.auth);

  // Validate tokens on mount and when tokens change
  useEffect(() => {
    const validateTokens = () => {
      try {
        // Check if tokens exist
        if (!accessToken || !refreshToken) {
          dispatch(setLogout());
          return;
        }

        // If we have tokens, ensure the auth state is set to authenticated
        if (!isAuthenticated) {
          dispatch(setLogin());
        }

        // In a real application, you might want to validate the token with the server
        // or check its expiration date
      } catch (error) {
        console.error('Error validating tokens:', error);
        dispatch(setLogout());
      }
    };

    validateTokens();
  }, [accessToken, refreshToken, isAuthenticated, dispatch]);

  return {
    isAuthenticated,
    accessToken,
    refreshToken,
    logout: () => dispatch(setLogout()),
  };
};

export default useAuth;
