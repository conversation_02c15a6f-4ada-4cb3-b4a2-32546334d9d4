import { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { Observation } from '@/types/observation';
import { fetchObservations, ObservationResponse } from '@/services/api';
import { parseISO } from 'date-fns';

export const useObservationData = () => {
  const [observations, setObservations] = useState<Observation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const { accessToken } = useSelector((state: RootState) => state.auth);

  // Helper function to format location from API response
  const formatLocation = useCallback((response: ObservationResponse): string => {
    const locationParts = [
      response.locationOne?.name,
      response.locationTwo?.name,
      response.locationThree?.name,
      response.locationFour?.name,
      response.locationFive?.name,
      response.locationSix?.name
    ].filter(Boolean);
    
    return locationParts.length > 0 ? locationParts.join(', ') : 'Unknown Location';
  }, []);

  // Helper function to map API response to Observation format
  const mapApiResponseToObservation = useCallback((response: ObservationResponse): Observation => {
    return {
      id: response.id,
      maskId: response.maskId,
      location: formatLocation(response),
      fullLocation: {
        country: response.locationOne?.name || 'Unknown',
        region: response.locationTwo?.name || 'Unknown',
        site: response.locationThree?.name || 'Unknown',
        level: response.locationFour?.name || 'Unknown'
      },
      category: response.observationCategory,
      type: response.observationType,
      observationType: response.observationType,
      actionCondition: response.observationActOrCondition || '',
      reportedBy: response.reporter ? `${response.reporter.firstName} ${response.reporter.lastName || ''}`.trim() : 'Unknown',
      reportedDate: parseISO(response.created),
      actionAssignee: response.actionOwner ? `${response.actionOwner.firstName} ${response.actionOwner.lastName || ''}`.trim() : null,
      reviewedBy: response.reviewer ? `${response.reviewer.firstName} ${response.reviewer.lastName || ''}`.trim() : null,
      status: response.status,
      description: response.description || '',
      attachments: response.uploads || [],
      uploads: response.uploads || [],
      dueDate: response.dueDate ? parseISO(response.dueDate) : null,
      rectifiedOnSpot: response.rectifiedOnSpot,
      actionTaken: response.actionTaken || null,
      evidenceImages: response.evidence || [],
      evidence: response.evidence || [],
      needsReviewer: response.isReviewerRequired,
      actionToBeTaken: response.actionToBeTaken || null,
      comments: response.comments || '',
      submittedBy: response.reporter ? `${response.reporter.firstName} ${response.reporter.lastName || ''}`.trim() : 'Unknown',
      observationActions: response.observationActions?.map(action => ({
        id: action.id,
        actionType: action.actionType,
        status: action.status,
        actionToBeTaken: action.actionToBeTaken,
        actionTaken: action.actionTaken,
        dueDate: action.dueDate,
        createdDate: action.created,
        assignedToId: action.assignedToId,
        uploads: action.uploads || [],
        application: action.application,
        applicationId: action.applicationId,
        maskId: action.maskId,
        trackId: action.trackId,
        description: action.description,
        sequence: action.sequence,
        prefix: action.prefix,
        objectId: action.objectId,
        submitURL: action.submitURL,
        serviceId: action.serviceId,
        submittedById: action.submittedById,
        submittedBy: action.submittedBy
      }))
    };
  }, [formatLocation]);

  // Function to fetch observation data
  const fetchObservationData = useCallback(async () => {
    if (!accessToken) {
      console.warn('No access token available - skipping observation data fetch');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const response = await fetchObservations(accessToken);

      // Map API response to Observation format
      const mappedObservations = response.map(mapApiResponseToObservation);

      setObservations(mappedObservations);
    } catch (err) {
      console.error('Error fetching observation data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch observation data');
      setObservations([]);
    } finally {
      setIsLoading(false);
    }
  }, [accessToken, mapApiResponseToObservation]);

  // Fetch observations when component mounts and when accessToken changes
  useEffect(() => {
    if (accessToken) {
      fetchObservationData();
    } else {
      // Reset data when no token is available
      setObservations([]);
      setIsLoading(false);
      setError(null);
    }
  }, [accessToken, fetchObservationData]);

  return {
    observations,
    isLoading,
    error,
    refetch: fetchObservationData
  };
};
