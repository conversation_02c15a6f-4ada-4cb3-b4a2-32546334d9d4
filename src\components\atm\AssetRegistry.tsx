import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import ExpandableDataTable from '@/components/common/ExpandableDataTable';
import {
  Plus,
  Filter,
  Download,
  Upload,
  Settings,
  MapPin,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  Edit,
  Trash2,
  Eye,
  QrCode
} from 'lucide-react';
import { Asset, AssetFilter, AssetStatus, AssetCondition, AssetCriticality } from '@/types/atm';
import { fetchAssets, fetchAssetCategories } from '@/services/atmApi';
import IncidentIntegration from './IncidentIntegration';

interface AssetRegistryProps {
  searchQuery: string;
}

const AssetRegistry = ({ searchQuery }: AssetRegistryProps) => {
  const [assets, setAssets] = useState<Asset[]>([]);
  const [filteredAssets, setFilteredAssets] = useState<Asset[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<AssetFilter>({});
  const [showFilters, setShowFilters] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadAssets();
    loadCategories();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [assets, searchQuery, filter]);

  const loadAssets = async () => {
    try {
      setIsLoading(true);
      const data = await fetchAssets();
      setAssets(data);
    } catch (error) {
      console.error('Error loading assets:', error);
      // Use mock data for demo
      setAssets(getMockAssets());
      toast({
        title: "Demo Mode",
        description: "Using mock asset data for demonstration",
        variant: "default"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const data = await fetchAssetCategories();
      setCategories(data);
    } catch (error) {
      console.error('Error loading categories:', error);
      setCategories(getMockCategories());
    }
  };

  const getMockAssets = (): Asset[] => [
    {
      id: '1',
      name: 'HVAC Unit A1',
      description: 'Main building air conditioning unit',
      assetTag: 'HVAC-001',
      category: { id: '1', name: 'HVAC Systems', description: '', icon: 'wind', color: 'blue' },
      location: 'Building A - Roof',
      department: 'Facilities',
      manufacturer: 'Carrier',
      model: 'AquaEdge 19DV',
      serialNumber: 'CV19DV001234',
      purchaseDate: '2022-03-15',
      warrantyExpiry: '2025-03-15',
      status: AssetStatus.ACTIVE,
      condition: AssetCondition.GOOD,
      criticality: AssetCriticality.HIGH,
      specifications: { capacity: '50 tons', refrigerant: 'R-410A' },
      images: [],
      created: '2022-03-15T10:00:00Z',
      updated: '2024-01-15T14:30:00Z',
      createdBy: 'admin',
      updatedBy: 'maintenance_team'
    },
    {
      id: '2',
      name: 'Pressure Vessel PV-101',
      description: 'Steam pressure vessel for process heating',
      assetTag: 'PV-101',
      category: { id: '2', name: 'Pressure Vessels', description: '', icon: 'gauge', color: 'red' },
      location: 'Plant Floor - Section B',
      department: 'Production',
      manufacturer: 'Babcock & Wilcox',
      model: 'BWD-500',
      serialNumber: 'BW500-789',
      purchaseDate: '2021-08-20',
      warrantyExpiry: '2026-08-20',
      status: AssetStatus.ACTIVE,
      condition: AssetCondition.EXCELLENT,
      criticality: AssetCriticality.CRITICAL,
      specifications: { pressure: '150 PSI', volume: '500 gallons' },
      images: [],
      created: '2021-08-20T09:00:00Z',
      updated: '2024-01-10T11:15:00Z',
      createdBy: 'admin',
      updatedBy: 'inspector'
    },
    {
      id: '3',
      name: 'Centrifugal Pump CP-205',
      description: 'Main water circulation pump',
      assetTag: 'CP-205',
      category: { id: '3', name: 'Pumps & Motors', description: '', icon: 'rotate-cw', color: 'green' },
      location: 'Pump House',
      department: 'Utilities',
      manufacturer: 'Grundfos',
      model: 'CR 64-2',
      serialNumber: 'GF64-456',
      purchaseDate: '2023-01-10',
      warrantyExpiry: '2026-01-10',
      status: AssetStatus.MAINTENANCE,
      condition: AssetCondition.FAIR,
      criticality: AssetCriticality.MEDIUM,
      specifications: { flow: '500 GPM', head: '200 ft' },
      images: [],
      created: '2023-01-10T08:00:00Z',
      updated: '2024-01-20T16:45:00Z',
      createdBy: 'admin',
      updatedBy: 'technician'
    }
  ];

  const getMockCategories = () => [
    { id: '1', name: 'HVAC Systems', description: 'Heating, ventilation, and air conditioning', icon: 'wind', color: 'blue' },
    { id: '2', name: 'Pressure Vessels', description: 'Steam and pressure equipment', icon: 'gauge', color: 'red' },
    { id: '3', name: 'Pumps & Motors', description: 'Rotating equipment', icon: 'rotate-cw', color: 'green' },
    { id: '4', name: 'Safety Equipment', description: 'Safety and emergency systems', icon: 'shield', color: 'orange' },
    { id: '5', name: 'Instrumentation', description: 'Measurement and control devices', icon: 'activity', color: 'purple' }
  ];

  const applyFilters = () => {
    let filtered = assets;

    // Apply search query
    if (searchQuery) {
      filtered = filtered.filter(asset =>
        asset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        asset.assetTag.toLowerCase().includes(searchQuery.toLowerCase()) ||
        asset.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        asset.location.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply filters
    if (filter.category) {
      filtered = filtered.filter(asset => asset.category.id === filter.category);
    }
    if (filter.status) {
      filtered = filtered.filter(asset => asset.status === filter.status);
    }
    if (filter.condition) {
      filtered = filtered.filter(asset => asset.condition === filter.condition);
    }
    if (filter.location) {
      filtered = filtered.filter(asset => asset.location.toLowerCase().includes(filter.location!.toLowerCase()));
    }
    if (filter.department) {
      filtered = filtered.filter(asset => asset.department.toLowerCase().includes(filter.department!.toLowerCase()));
    }

    setFilteredAssets(filtered);
  };

  const getStatusBadge = (status: AssetStatus) => {
    const variants = {
      [AssetStatus.ACTIVE]: 'default',
      [AssetStatus.INACTIVE]: 'secondary',
      [AssetStatus.MAINTENANCE]: 'destructive',
      [AssetStatus.RETIRED]: 'outline',
      [AssetStatus.DISPOSED]: 'outline'
    };
    return <Badge variant={variants[status] as any}>{status}</Badge>;
  };

  const getConditionBadge = (condition: AssetCondition) => {
    const colors = {
      [AssetCondition.EXCELLENT]: 'text-green-600 bg-green-50',
      [AssetCondition.GOOD]: 'text-blue-600 bg-blue-50',
      [AssetCondition.FAIR]: 'text-yellow-600 bg-yellow-50',
      [AssetCondition.POOR]: 'text-orange-600 bg-orange-50',
      [AssetCondition.CRITICAL]: 'text-red-600 bg-red-50'
    };
    return <Badge className={colors[condition]}>{condition}</Badge>;
  };

  const getCriticalityBadge = (criticality: AssetCriticality) => {
    const variants = {
      [AssetCriticality.LOW]: 'outline',
      [AssetCriticality.MEDIUM]: 'secondary',
      [AssetCriticality.HIGH]: 'destructive',
      [AssetCriticality.CRITICAL]: 'destructive'
    };
    return <Badge variant={variants[criticality] as any}>{criticality}</Badge>;
  };

  const columns = [
    {
      key: 'assetTag',
      label: 'Asset Tag',
      sortable: true,
      render: (asset: Asset) => (
        <div className="font-medium">{asset.assetTag}</div>
      )
    },
    {
      key: 'name',
      label: 'Asset Name',
      sortable: true,
      render: (asset: Asset) => (
        <div>
          <div className="font-medium">{asset.name}</div>
          <div className="text-sm text-muted-foreground">{asset.description}</div>
        </div>
      )
    },
    {
      key: 'category',
      label: 'Category',
      sortable: true,
      render: (asset: Asset) => (
        <Badge variant="outline">{asset.category.name}</Badge>
      )
    },
    {
      key: 'location',
      label: 'Location',
      sortable: true,
      render: (asset: Asset) => (
        <div className="flex items-center gap-1">
          <MapPin className="h-3 w-3 text-muted-foreground" />
          <span className="text-sm">{asset.location}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (asset: Asset) => getStatusBadge(asset.status)
    },
    {
      key: 'condition',
      label: 'Condition',
      sortable: true,
      render: (asset: Asset) => getConditionBadge(asset.condition)
    },
    {
      key: 'criticality',
      label: 'Criticality',
      sortable: true,
      render: (asset: Asset) => getCriticalityBadge(asset.criticality)
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (asset: Asset) => (
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Eye className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <QrCode className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  const expandedRowRender = (asset: Asset) => (
    <div className="p-4 bg-muted/50 rounded-lg space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div>
          <h4 className="font-medium mb-2">Asset Details</h4>
          <div className="space-y-1 text-sm">
            <div><span className="font-medium">Manufacturer:</span> {asset.manufacturer}</div>
            <div><span className="font-medium">Model:</span> {asset.model}</div>
            <div><span className="font-medium">Serial Number:</span> {asset.serialNumber}</div>
            <div><span className="font-medium">Department:</span> {asset.department}</div>
          </div>
        </div>
        <div>
          <h4 className="font-medium mb-2">Dates</h4>
          <div className="space-y-1 text-sm">
            <div><span className="font-medium">Purchase Date:</span> {new Date(asset.purchaseDate).toLocaleDateString()}</div>
            {asset.warrantyExpiry && (
              <div><span className="font-medium">Warranty Expiry:</span> {new Date(asset.warrantyExpiry).toLocaleDateString()}</div>
            )}
            <div><span className="font-medium">Last Updated:</span> {new Date(asset.updated).toLocaleDateString()}</div>
          </div>
        </div>
        <div>
          <h4 className="font-medium mb-2">Specifications</h4>
          <div className="space-y-1 text-sm">
            {Object.entries(asset.specifications).map(([key, value]) => (
              <div key={key}>
                <span className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1')}:</span> {value}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Incident Integration */}
      <div className="mt-4 pt-4 border-t">
        <IncidentIntegration assetId={asset.id} />
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button onClick={() => setShowFilters(!showFilters)} variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Asset
        </Button>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Select value={filter.category || ''} onValueChange={(value) => setFilter({...filter, category: value || undefined})}>
                <SelectTrigger>
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Categories</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.id}>{category.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={filter.status || ''} onValueChange={(value) => setFilter({...filter, status: value as AssetStatus || undefined})}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Statuses</SelectItem>
                  {Object.values(AssetStatus).map(status => (
                    <SelectItem key={status} value={status}>{status}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={filter.condition || ''} onValueChange={(value) => setFilter({...filter, condition: value as AssetCondition || undefined})}>
                <SelectTrigger>
                  <SelectValue placeholder="Condition" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Conditions</SelectItem>
                  {Object.values(AssetCondition).map(condition => (
                    <SelectItem key={condition} value={condition}>{condition}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Input
                placeholder="Location"
                value={filter.location || ''}
                onChange={(e) => setFilter({...filter, location: e.target.value || undefined})}
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Assets Table */}
      <Card>
        <CardHeader>
          <CardTitle>Asset Registry</CardTitle>
          <CardDescription>
            {filteredAssets.length} of {assets.length} assets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ExpandableDataTable
            data={filteredAssets}
            columns={columns}
            loading={isLoading}
            expandedRowRender={expandedRowRender}
            searchable={false} // We handle search externally
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default AssetRegistry;
