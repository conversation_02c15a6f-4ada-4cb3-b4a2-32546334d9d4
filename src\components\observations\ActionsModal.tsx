import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import {
  CalendarIcon,
  MapPin,
  User,
  FileText,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Eye,
  X,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { ObservationAction, ObservationResponse } from "@/services/api";
import { useToast } from "@/components/ui/use-toast";
import FileUploadComponent from "@/components/common/FileUploadComponent";
import { Form } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import ImageComponent from "@/components/common/ImageComponent";

import { API_BASE_URL } from "@/constants/index";

// Define the API endpoint for submitting actions
const OBS_ACTION_SUBMIT = (id: string) => {
  return `${API_BASE_URL}/observation-action-submit/${id}`;
};

// Define the API endpoint for fetching users by role
const GET_USER_ROLE_BY_MODE = `${API_BASE_URL}/users/get_users`;

interface ActionsModalProps {
  show: boolean;
  applicationDetails: ObservationResponse;
  showItem: ObservationAction;
  closeModal: () => void;
  accessToken: string;
  onActionSubmitted?: () => void;
}

const ActionsModal: React.FC<ActionsModalProps> = ({
  show,
  applicationDetails,
  showItem,
  closeModal,
  accessToken,
  onActionSubmitted,
}) => {
  const { toast } = useToast();
  const [apiStatus, setApiStatus] = useState<"Approve" | "Return" | "">("");
  const [comments, setComments] = useState("");
  const [showErrors, setShowErrors] = useState(false);
  const [assessors, setAssessors] = useState<
    { label: string; value: string }[]
  >([]);
  const [assessorId, setAssessorId] = useState("");
  const [actionTaken, setActionTaken] = useState("");
  const [actionToBeTaken, setActionToBeTaken] = useState("");
  const [evidence, setEvidence] = useState<string[]>([]);
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [previewImages, setPreviewImages] = useState<string[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Initialize form context for the FileUploadComponent
  const formMethods = useForm();

  useEffect(() => {
    if (show) {
      if (showItem.actionType === "review") {
        getCrewList("obsactionowner");
      } else if (
        showItem.actionType === "take_action" ||
        showItem.actionType === "reperform_action"
      ) {
        getCrewList("obsreviewer");
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [show, showItem]);

  const getCrewList = async (type: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(GET_USER_ROLE_BY_MODE, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          locationOneId: "",
          locationTwoId: "",
          locationThreeId: "",
          locationFourId: "",
          mode: type,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        const formattedData = data.map(
          (item: { firstName: string; id: string }) => ({
            label: item.firstName,
            value: item.id,
          })
        );
        setAssessors(formattedData);
      } else {
        throw new Error("Failed to fetch user list");
      }
    } catch (error) {
      console.error("Error fetching user list:", error);
      toast({
        title: "Error",
        description: "Failed to load users. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async () => {
    setShowErrors(true);
    let hasError = false;

    if (
      showItem.actionType === "take_action" ||
      showItem.actionType === "reperform_action"
    ) {
      if (!actionTaken) hasError = true;
      if (!assessorId) hasError = true;
      if (evidence.length === 0) hasError = true;
    } else if (showItem.actionType === "review") {
      if (!actionToBeTaken) hasError = true;
      if (!dueDate) hasError = true;
      if (!assessorId) hasError = true;
    } else if (showItem.actionType === "verify_action") {
      if (!apiStatus) hasError = true;
      if (apiStatus === "Return" && !comments) hasError = true;
    }

    if (hasError) {
      toast({
        title: "Validation Error",
        description: "Please fill all required fields before submitting!",
        variant: "destructive",
      });
      return;
    }

    interface FormDataType {
      actionTaken?: string;
      reviewerId?: string;
      evidence?: string[];
      actionToBeTaken?: string;
      dueDate?: string;
      actionOwnerId?: string;
      status?: string;
      comments?: string;
    }

    let formData: FormDataType = {};

    if (
      showItem.actionType === "take_action" ||
      showItem.actionType === "reperform_action"
    ) {
      formData = {
        actionTaken: actionTaken,
        reviewerId: assessorId,
        evidence: evidence,
      };
    } else if (showItem.actionType === "review") {
      formData = {
        actionToBeTaken: actionToBeTaken,
        dueDate: dueDate?.toISOString(),
        actionOwnerId: assessorId,
      };
    } else if (showItem.actionType === "verify_action") {
      formData = {
        status: apiStatus === "Approve" ? "Completed" : "Returned",
        comments: comments,
      };
    }

    try {
      setIsLoading(true);
      const response = await fetch(OBS_ACTION_SUBMIT(showItem.id), {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Action submitted successfully!",
          variant: "default",
        });
        if (onActionSubmitted) {
          onActionSubmitted();
        }
        closeModal();
      } else {
        throw new Error("Failed to submit action");
      }
    } catch (error) {
      console.error("Error submitting action:", error);
      toast({
        title: "Submission Failed",
        description: "Failed to submit action. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEvidenceUpload = (fileNames: string[]) => {
    setEvidence(fileNames);
  };

  // Image preview functions
  const openImagePreview = (images: string[], index: number) => {
    setPreviewImages(images);
    setCurrentImageIndex(index);
    setPreviewImage(images[index]);
  };

  const closeImagePreview = () => {
    setPreviewImage(null);
    setPreviewImages([]);
    setCurrentImageIndex(0);
  };

  const nextImage = () => {
    const nextIndex = (currentImageIndex + 1) % previewImages.length;
    setCurrentImageIndex(nextIndex);
    setPreviewImage(previewImages[nextIndex]);
  };

  const prevImage = () => {
    const prevIndex =
      (currentImageIndex - 1 + previewImages.length) % previewImages.length;
    setCurrentImageIndex(prevIndex);
    setPreviewImage(previewImages[prevIndex]);
  };

  const renderModalContent = () => {
    if (showItem.actionType === "review") {
      return (
        <div className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                <FileText className="h-4 w-4" />
                Action to be Taken
              </Label>
              <Textarea
                id="actionToBeTaken"
                value={actionToBeTaken}
                onChange={(e) => setActionToBeTaken(e.target.value)}
                className={cn(
                  "w-full min-h-[120px] resize-none",
                  showErrors && !actionToBeTaken
                    ? "border-red-500 focus:border-red-500"
                    : ""
                )}
                placeholder="Describe the action that needs to be taken to address this observation..."
                rows={5}
              />
              {showErrors && !actionToBeTaken && (
                <p className="text-red-500 text-xs mt-2 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Action to be Taken is required
                </p>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                <CalendarIcon className="h-4 w-4" />
                Due Date
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal h-11",
                      !dueDate && "text-muted-foreground",
                      showErrors && !dueDate
                        ? "border-red-500 focus:border-red-500"
                        : ""
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dueDate
                      ? format(dueDate, "PPP")
                      : "Select due date for this action"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={dueDate}
                    onSelect={setDueDate}
                    initialFocus
                    disabled={(date) => date < new Date()}
                  />
                </PopoverContent>
              </Popover>
              {showErrors && !dueDate && (
                <p className="text-red-500 text-xs mt-2 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Due date is required
                </p>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                <User className="h-4 w-4" />
                Action Owner
              </Label>
              <Select value={assessorId} onValueChange={setAssessorId}>
                <SelectTrigger
                  className={cn(
                    "h-11",
                    showErrors && !assessorId
                      ? "border-red-500 focus:border-red-500"
                      : ""
                  )}
                >
                  <SelectValue placeholder="Select who will be responsible for this action" />
                </SelectTrigger>
                <SelectContent>
                  {assessors.map((assessor) => (
                    <SelectItem key={assessor.value} value={assessor.value}>
                      {assessor.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {showErrors && !assessorId && (
                <p className="text-red-500 text-xs mt-2 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Action Owner is required
                </p>
              )}
            </div>
          </div>
        </div>
      );
    } else if (showItem.actionType === "take_action") {
      return (
        <div className="space-y-6">
          {/* Action Information Section */}
          <div className="space-y-6">
            {/* Action Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                {/* Action Assignee */}
                {applicationDetails.actionOwner && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                      <User className="h-3 w-3" />
                      Action Assignee
                    </Label>
                    <p className="text-sm font-medium mt-1">
                      {`${applicationDetails.actionOwner.firstName} ${
                        applicationDetails.actionOwner.lastName || ""
                      }`.trim()}
                    </p>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                {/* Due Date */}
                {(showItem.dueDate || applicationDetails.dueDate) && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                      <CalendarIcon className="h-3 w-3" />
                      Due Date
                    </Label>
                    <p className="text-sm font-medium mt-1">
                      {showItem.dueDate
                        ? new Date(showItem.dueDate).toLocaleDateString()
                        : applicationDetails.dueDate
                        ? new Date(
                            applicationDetails.dueDate
                          ).toLocaleDateString()
                        : "Not specified"}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Assigned Action Section */}
            {(showItem.actionToBeTaken ||
              applicationDetails.actionToBeTaken) && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                  <FileText className="h-4 w-4" />
                  Assigned Action
                </Label>
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-900 leading-relaxed">
                    {showItem.actionToBeTaken ||
                      applicationDetails.actionToBeTaken}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Action Taken Section */}
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                <CheckCircle className="h-4 w-4" />
                Action Taken
              </Label>
              <Textarea
                id="actionTaken"
                value={actionTaken}
                onChange={(e) => setActionTaken(e.target.value)}
                className={cn(
                  "w-full min-h-[120px] resize-none",
                  showErrors && !actionTaken
                    ? "border-red-500 focus:border-red-500"
                    : ""
                )}
                placeholder="Describe the action you have taken to address this observation..."
                rows={5}
              />
              {showErrors && !actionTaken && (
                <p className="text-red-500 text-xs mt-2 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Action Taken is required
                </p>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                <FileText className="h-4 w-4" />
                Evidence
              </Label>
              <div className="border rounded-lg p-4 bg-muted/20">
                <Form {...formMethods}>
                  <FileUploadComponent
                    onFileUpload={handleEvidenceUpload}
                    fieldName="evidence"
                    description="Upload photos as evidence. You can select multiple files."
                    accept="image/*"
                    multiple={true}
                    maxFiles={10}
                    initialFiles={evidence}
                  />
                </Form>
              </div>
              {showErrors && evidence.length === 0 && (
                <p className="text-red-500 text-xs mt-2 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  At least one evidence file is required
                </p>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                <User className="h-4 w-4" />
                Reviewer
              </Label>
              <Select value={assessorId} onValueChange={setAssessorId}>
                <SelectTrigger
                  className={cn(
                    "h-11",
                    showErrors && !assessorId
                      ? "border-red-500 focus:border-red-500"
                      : ""
                  )}
                >
                  <SelectValue placeholder="Select who will review this action" />
                </SelectTrigger>
                <SelectContent>
                  {assessors.map((assessor) => (
                    <SelectItem key={assessor.value} value={assessor.value}>
                      {assessor.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {showErrors && !assessorId && (
                <p className="text-red-500 text-xs mt-2 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Reviewer is required
                </p>
              )}
            </div>
          </div>
        </div>
      );
    } else if (showItem.actionType === "reperform_action") {
      return (
        <div className="space-y-6">
          {/* 1. Assigned Action Details Section */}
          <div className="space-y-4">
            <Label className="text-lg font-medium text-muted-foreground flex items-center gap-2 mb-3">
              <FileText className="h-5 w-5" />
              Assigned Action Details
            </Label>

            <div className="space-y-6 mb-4">
              {/* Action Taken */}
              {applicationDetails.actionTaken && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    Action Taken
                  </Label>
                  <div className="mt-1 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <p className="text-sm text-blue-900">
                      {applicationDetails.actionTaken}
                    </p>
                  </div>
                </div>
              )}

              {/* Action Taken By and Action Taken Date on the same line */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Action Taken By */}
                {(showItem.submittedBy || applicationDetails.actionOwner) && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                      <User className="h-3 w-3" />
                      Action Taken By
                    </Label>
                    <p className="text-sm font-medium mt-1">
                      {showItem.submittedBy?.firstName
                        ? showItem.submittedBy.firstName
                        : applicationDetails.actionOwner
                        ? `${applicationDetails.actionOwner.firstName} ${
                            applicationDetails.actionOwner.lastName || ""
                          }`.trim()
                        : "Not specified"}
                    </p>
                  </div>
                )}

                {/* Action Taken Date */}
                {(showItem.updated || applicationDetails.updated) && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      Action Taken Date & Time
                    </Label>
                    <p className="text-sm font-medium mt-1">
                      {showItem.updated
                        ? new Date(showItem.updated).toLocaleString()
                        : applicationDetails.updated
                        ? new Date(applicationDetails.updated).toLocaleString()
                        : "Not specified"}
                    </p>
                  </div>
                )}
              </div>

              {/* Due Date */}
              {(showItem.dueDate || applicationDetails.dueDate) && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <CalendarIcon className="h-3 w-3" />
                    Due Date
                  </Label>
                  <p className="text-sm font-medium mt-1">
                    {showItem.dueDate
                      ? new Date(showItem.dueDate).toLocaleDateString()
                      : applicationDetails.dueDate
                      ? new Date(
                          applicationDetails.dueDate
                        ).toLocaleDateString()
                      : "Not specified"}
                  </p>
                </div>
              )}
            </div>

            {/* Evidence Images from previous action */}
            {applicationDetails.evidence &&
              applicationDetails.evidence.length > 0 && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                    <FileText className="h-3 w-3" />
                    Evidence Images ({applicationDetails.evidence.length})
                  </Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    {applicationDetails.evidence.map((fileName, index) => (
                      <div
                        key={index}
                        className="relative group cursor-pointer"
                       
                      >
                        <div className="aspect-square bg-green-50 rounded-lg border border-green-200 overflow-hidden hover:border-green-500 transition-colors">
                          <ImageComponent fileName={fileName} />
                        </div>
                        <p className="text-xs text-center mt-1 truncate text-green-700">
                          {fileName.split("/").pop() || `Evidence ${index + 1}`}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
          </div>

          <Separator />

          {/* 2. Verification Information Section */}
          {applicationDetails.reviewer && (
            <div className="space-y-4">
              <Label className="text-lg font-medium text-muted-foreground flex items-center gap-2 mb-3">
                <Eye className="h-5 w-5" />
                Verification Information
              </Label>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Action Verified By */}
                <div>
                  <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <User className="h-3 w-3" />
                    Action Verified By
                  </Label>
                  <p className="text-sm font-medium mt-1">
                    {`${applicationDetails.reviewer.firstName} ${
                      applicationDetails.reviewer.lastName || ""
                    }`.trim()}
                  </p>
                </div>

                {/* Verified Date & Time */}
                {applicationDetails.updated && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      Verified Date & Time
                    </Label>
                    <p className="text-sm font-medium mt-1">
                      {new Date(applicationDetails.updated).toLocaleString()}
                    </p>
                  </div>
                )}
              </div>

              {/* Due Date */}
              {(showItem.dueDate || applicationDetails.dueDate) && (
                <div className="mt-4">
                  <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <CalendarIcon className="h-3 w-3" />
                    Due Date
                  </Label>
                  <p className="text-sm font-medium mt-1">
                    {showItem.dueDate
                      ? new Date(showItem.dueDate).toLocaleDateString()
                      : applicationDetails.dueDate
                      ? new Date(
                          applicationDetails.dueDate
                        ).toLocaleDateString()
                      : "Not specified"}
                  </p>
                </div>
              )}

              {/* Comments - Always display in Verification Information */}
              <div className="mt-4">
                <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  Comments
                </Label>
                <div className="mt-1 p-3 bg-muted/50 rounded-md border">
                  <p className="text-sm leading-relaxed">
                    {showItem.actionToBeTaken ||
                      applicationDetails.actionToBeTaken ||
                      "No comments provided"}
                  </p>
                </div>
              </div>
            </div>
          )}

          {applicationDetails.reviewer && <Separator />}

          {/* 3. Reassignment Details Section */}
          <div className="space-y-4">
            <Label className="text-lg font-medium text-muted-foreground flex items-center gap-2 mb-3">
              <FileText className="h-5 w-5" />
              Reassigned Action Details
            </Label>

            {/* Reassigned Action */}
            {(showItem.actionToBeTaken ||
              applicationDetails.actionToBeTaken) && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                  <FileText className="h-4 w-4" />
                  Assigned Action
                </Label>
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-900 leading-relaxed">
                    {showItem.actionToBeTaken ||
                      applicationDetails.actionToBeTaken}
                  </p>
                </div>
              </div>
            )}

            {/* Action Assignee */}
            {applicationDetails.actionOwner && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                  <User className="h-3 w-3" />
                  Action Assignee
                </Label>
                <p className="text-sm font-medium mt-1">
                  {`${applicationDetails.actionOwner.firstName} ${
                    applicationDetails.actionOwner.lastName || ""
                  }`.trim()}
                </p>
              </div>
            )}

            {/* Due Date */}
            {(showItem.dueDate || applicationDetails.dueDate) && (
              <div className="mt-4">
                <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                  <CalendarIcon className="h-3 w-3" />
                  Due Date
                </Label>
                <p className="text-sm font-medium mt-1">
                  {showItem.dueDate
                    ? new Date(showItem.dueDate).toLocaleDateString()
                    : applicationDetails.dueDate
                    ? new Date(applicationDetails.dueDate).toLocaleDateString()
                    : "Not specified"}
                </p>
              </div>
            )}
          </div>

          <Separator />

          {/* 4. Action Execution and Review Section */}
          <div className="space-y-4">
            <Label className="text-lg font-medium text-muted-foreground flex items-center gap-2 mb-3">
              <CheckCircle className="h-5 w-5" />
              Action Execution
            </Label>

            {/* Due Date */}
            {(showItem.dueDate || applicationDetails.dueDate) && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                  <CalendarIcon className="h-3 w-3" />
                  Due Date
                </Label>
                <p className="text-sm font-medium mt-1 mb-4">
                  {showItem.dueDate
                    ? new Date(showItem.dueDate).toLocaleDateString()
                    : applicationDetails.dueDate
                    ? new Date(applicationDetails.dueDate).toLocaleDateString()
                    : "Not specified"}
                </p>
              </div>
            )}

            {/* Action Taken Input */}
            <div>
              <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                <CheckCircle className="h-4 w-4" />
                Action Taken
              </Label>
              <Textarea
                id="actionTaken"
                value={actionTaken}
                onChange={(e) => setActionTaken(e.target.value)}
                className={cn(
                  "w-full min-h-[120px] resize-none",
                  showErrors && !actionTaken
                    ? "border-red-500 focus:border-red-500"
                    : ""
                )}
                placeholder="Describe the action you have taken to address this observation..."
                rows={5}
              />
              {showErrors && !actionTaken && (
                <p className="text-red-500 text-xs mt-2 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Action Taken is required
                </p>
              )}
            </div>

            {/* Evidence Upload */}
            <div>
              <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                <FileText className="h-4 w-4" />
                Evidence
              </Label>
              <div className="border rounded-lg p-4 bg-muted/20">
                <Form {...formMethods}>
                  <FileUploadComponent
                    onFileUpload={handleEvidenceUpload}
                    fieldName="evidence"
                    description="Upload photos as evidence. You can select multiple files."
                    accept="image/*"
                    multiple={true}
                    maxFiles={10}
                    initialFiles={evidence}
                  />
                </Form>
              </div>
              {showErrors && evidence.length === 0 && (
                <p className="text-red-500 text-xs mt-2 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  At least one evidence file is required
                </p>
              )}
            </div>

            {/* Reviewer Selection */}
            <div>
              <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                <User className="h-4 w-4" />
                Reviewer
              </Label>
              <Select value={assessorId} onValueChange={setAssessorId}>
                <SelectTrigger
                  className={cn(
                    "h-11",
                    showErrors && !assessorId
                      ? "border-red-500 focus:border-red-500"
                      : ""
                  )}
                >
                  <SelectValue placeholder="Select who will review this action" />
                </SelectTrigger>
                <SelectContent>
                  {assessors.map((assessor) => (
                    <SelectItem key={assessor.value} value={assessor.value}>
                      {assessor.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {showErrors && !assessorId && (
                <p className="text-red-500 text-xs mt-2 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Reviewer is required
                </p>
              )}
            </div>
          </div>
        </div>
      );
    } else if (showItem.actionType === "verify_action") {
      return (
        <div className="space-y-6">
          {/* Action Taken Details Section */}
          <div className="space-y-6">
            <div>
              <Label className="text-lg font-medium text-muted-foreground flex items-center gap-2 mb-4">
                <CheckCircle className="h-5 w-5" />
                Action Taken Summary
              </Label>

              {/* Action Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="space-y-4">
                  {/* Assigned Action */}
                  {(showItem.actionToBeTaken ||
                    applicationDetails.actionToBeTaken) && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                        <FileText className="h-3 w-3" />
                        Assigned Action
                      </Label>
                      <div className="mt-1 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <p className="text-sm text-blue-900">
                          {showItem.actionToBeTaken ||
                            applicationDetails.actionToBeTaken}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Due Date */}
                  {(showItem.dueDate || applicationDetails.dueDate) && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                        <CalendarIcon className="h-3 w-3" />
                        Due Date
                      </Label>
                      <p className="text-sm font-medium mt-1">
                        {showItem.dueDate
                          ? new Date(showItem.dueDate).toLocaleDateString()
                          : applicationDetails.dueDate
                          ? new Date(
                              applicationDetails.dueDate
                            ).toLocaleDateString()
                          : "Not specified"}
                      </p>
                    </div>
                  )}

                  {/* Action Taken By */}
                  {(showItem.submittedBy || applicationDetails.actionOwner) && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                        <User className="h-3 w-3" />
                        Action Taken By
                      </Label>
                      <p className="text-sm font-medium mt-1">
                        {showItem.submittedBy?.firstName
                          ? showItem.submittedBy.firstName
                          : applicationDetails.actionOwner
                          ? `${applicationDetails.actionOwner.firstName} ${
                              applicationDetails.actionOwner.lastName || ""
                            }`.trim()
                          : "Not specified"}
                      </p>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  {/* Action Taken Date */}
                  {(showItem.updated || applicationDetails.updated) && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        Action Taken Date & Time
                      </Label>
                      <p className="text-sm font-medium mt-1">
                        {showItem.updated
                          ? new Date(showItem.updated).toLocaleString()
                          : applicationDetails.updated
                          ? new Date(
                              applicationDetails.updated
                            ).toLocaleString()
                          : "Not specified"}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Action Taken Details */}
              {applicationDetails.actionTaken && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                    <FileText className="h-3 w-3" />
                    Action Taken Details
                  </Label>
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-sm text-green-900 leading-relaxed">
                      {applicationDetails.actionTaken}
                    </p>
                  </div>
                </div>
              )}

              {/* Evidence Images */}
              {applicationDetails.evidence &&
                applicationDetails.evidence.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                      <FileText className="h-3 w-3" />
                      Evidence Images ({applicationDetails.evidence.length})
                    </Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                      {applicationDetails.evidence.map((fileName, index) => (
                        <div
                          key={index}
                          className="relative group cursor-pointer"
                         
                        >
                          <div className="aspect-square bg-green-50 rounded-lg border border-green-200 overflow-hidden hover:border-green-500 transition-colors">
                            <ImageComponent fileName={fileName} />
                          </div>
                          <p className="text-xs text-center mt-1 truncate text-green-700">
                            {fileName.split("/").pop() ||
                              `Evidence ${index + 1}`}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
            </div>
          </div>

          {/* Action Decision Section */}
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-4">
                <Eye className="h-4 w-4" />
                Action Decision
              </Label>
              <div className="grid grid-cols-2 gap-4">
                <Button
                  type="button"
                  size="lg"
                  variant={apiStatus === "Approve" ? "default" : "outline"}
                  className={cn(
                    "h-16 transition-all duration-200 flex flex-col gap-1",
                    apiStatus === "Approve"
                      ? "bg-green-600 hover:bg-green-700 text-white shadow-lg"
                      : "hover:border-green-500 hover:text-green-600 hover:bg-green-50"
                  )}
                  onClick={() => setApiStatus("Approve")}
                >
                  <CheckCircle className="h-5 w-5" />
                  <span className="font-medium">Approve</span>
                </Button>

                <Button
                  type="button"
                  size="lg"
                  variant={apiStatus === "Return" ? "destructive" : "outline"}
                  className={cn(
                    "h-16 transition-all duration-200 flex flex-col gap-1",
                    apiStatus === "Return"
                      ? "shadow-lg"
                      : "hover:border-red-500 hover:text-red-600 hover:bg-red-50"
                  )}
                  onClick={() => setApiStatus("Return")}
                >
                  <XCircle className="h-5 w-5" />
                  <span className="font-medium">Return</span>
                </Button>
              </div>
              {showErrors && !apiStatus && (
                <p className="text-red-500 text-xs mt-3 text-center flex items-center justify-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Please select Approve or Return
                </p>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                <FileText className="h-4 w-4" />
                Comments
                {apiStatus === "Return" && (
                  <span className="text-red-500 text-xs ml-1">*</span>
                )}
              </Label>
              <Textarea
                id="comments"
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                className={cn(
                  "w-full min-h-[100px] resize-none",
                  showErrors && apiStatus === "Return" && !comments
                    ? "border-red-500 focus:border-red-500"
                    : ""
                )}
                placeholder={
                  apiStatus === "Return"
                    ? "Please provide detailed comments explaining why this action is being returned..."
                    : "Add any additional comments or feedback (optional)..."
                }
                rows={4}
              />
              {showErrors && apiStatus === "Return" && !comments && (
                <p className="text-red-500 text-xs mt-2 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Comments are required when returning an action
                </p>
              )}
            </div>
          </div>
        </div>
      );
    }

    return null;
  };

  const getActionTypeDisplay = () => {
    switch (showItem.actionType) {
      case "take_action":
        return "Take Action";
      case "verify_action":
        return "Verify Action";
      case "review":
        return "Review Action";
      case "reperform_action":
        return "Re-perform Action";
      default:
        return showItem.actionToBeTaken;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status?.toLowerCase()) {
      case "completed":
      case "approved":
        return "default";
      case "pending":
      case "in progress":
        return "secondary";
      case "returned":
      case "rejected":
        return "destructive";
      default:
        return "outline";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case "completed":
      case "approved":
        return <CheckCircle className="h-3 w-3" />;
      case "pending":
      case "in progress":
        return <Clock className="h-3 w-3" />;
      case "returned":
      case "rejected":
        return <XCircle className="h-3 w-3" />;
      default:
        return <AlertCircle className="h-3 w-3" />;
    }
  };

  return (
    <>
      {/* Image Preview Modal */}
      {previewImage && (
        <Dialog open={!!previewImage} onOpenChange={closeImagePreview}>
          <DialogContent className="max-w-6xl h-[90vh] p-0 bg-black/90">
            <div className="relative w-full h-full flex items-center justify-center">
              {/* Close Button */}
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-4 right-4 z-50 text-white hover:bg-white/20"
                onClick={closeImagePreview}
              >
                <X className="h-6 w-6" />
              </Button>

              {/* Navigation Buttons */}
              {previewImages.length > 1 && (
                <>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute left-4 top-1/2 -translate-y-1/2 z-50 text-white hover:bg-white/20"
                    onClick={prevImage}
                  >
                    <ChevronLeft className="h-8 w-8" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-4 top-1/2 -translate-y-1/2 z-50 text-white hover:bg-white/20"
                    onClick={nextImage}
                  >
                    <ChevronRight className="h-8 w-8" />
                  </Button>
                </>
              )}

              {/* Image */}
              <div className="w-full h-full flex items-center justify-center p-8">
                <div className="max-w-full max-h-full bg-white rounded-lg overflow-hidden">
                  <ImageComponent fileName={previewImage} />
                </div>
              </div>

              {/* Image Counter */}
              {previewImages.length > 1 && (
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                  {currentImageIndex + 1} of {previewImages.length}
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Main Modal */}
      <Dialog open={show} onOpenChange={closeModal}>
        <DialogContent className="max-w-4xl h-[90vh] flex flex-col p-0">
          {/* Fixed Header */}
          <DialogHeader className="px-6 pt-6 pb-4 border-b bg-white">
            <DialogTitle className="text-xl font-semibold flex items-center gap-2">
              <Eye className="h-5 w-5 text-primary" />
              {getActionTypeDisplay()}
            </DialogTitle>
          </DialogHeader>

          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto px-6 py-4">
            <div className="space-y-6">
              {/* Observation Details Section */}
              <Card className="bg-gradient-card">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FileText className="h-5 w-5 text-primary" />
                      Observation Details
                    </CardTitle>
                    <Badge
                      variant={getStatusBadgeVariant(applicationDetails.status)}
                      className="flex items-center gap-1 px-3 py-1"
                    >
                      {getStatusIcon(applicationDetails.status)}
                      {applicationDetails.status}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Header Row with ID, Status, and Reported Date */}
                  <div className="flex items-start justify-between">
                    <div>
                      <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                        Observation ID
                      </Label>
                      <p className="text-lg font-semibold text-primary">
                        {applicationDetails.maskId}
                      </p>
                    </div>
                    <div>
                      <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        Reported Date & Time
                      </Label>
                      <p className="text-sm font-medium text-gray-700">
                        {applicationDetails.created
                          ? new Date(
                              applicationDetails.created
                            ).toLocaleString()
                          : "N/A"}
                      </p>
                    </div>
                  </div>

                  <Separator />

                  {/* Main Details Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          Category
                        </Label>
                        <p className="text-sm font-medium mt-1">
                          {applicationDetails.observationCategory}
                        </p>
                      </div>

                      <div>
                        <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                          <FileText className="h-3 w-3" />
                          Type
                        </Label>
                        <p className="text-sm font-medium mt-1">
                          {applicationDetails.observationType} -{" "}
                          {applicationDetails.observationActOrCondition ||
                            "Not specified"}
                        </p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          Location
                        </Label>
                        <p className="text-sm font-medium mt-1">
                          {[
                            applicationDetails.locationOne?.name,
                            applicationDetails.locationTwo?.name,
                            applicationDetails.locationThree?.name,
                            applicationDetails.locationFour?.name,
                          ]
                            .filter(Boolean)
                            .join(" > ")}
                        </p>
                      </div>

                      <div>
                        <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                          <User className="h-3 w-3" />
                          Reported By
                        </Label>
                        <p className="text-sm font-medium mt-1">
                          {applicationDetails.reporter?.firstName}{" "}
                          {applicationDetails.reporter?.lastName}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Description */}
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">
                      Description
                    </Label>
                    <div className="mt-2 p-3 bg-muted/50 rounded-md border">
                      <p className="text-sm leading-relaxed">
                        {applicationDetails.description ||
                          "No description provided"}
                      </p>
                    </div>
                  </div>

                  {/* Attachments Section */}
                  {applicationDetails.uploads &&
                    applicationDetails.uploads.length > 0 && (
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1 mb-3">
                          <FileText className="h-3 w-3" />
                          Attachments ({applicationDetails.uploads.length})
                        </Label>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                          {applicationDetails.uploads.map((fileName, index) => (
                            <div
                              key={index}
                              className="relative group cursor-pointer"
                              
                              
                            >
                              <div className="aspect-square bg-gray-50 rounded-lg border border-gray-200 overflow-hidden hover:border-primary transition-colors">
                                <ImageComponent fileName={fileName} />
                              </div>
                              <p className="text-xs text-center mt-1 truncate text-gray-600">
                                {fileName.split("/").pop() ||
                                  `Attachment ${index + 1}`}
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                </CardContent>
              </Card>

              {/* Action Details Section */}
              <Card className="bg-gradient-card">
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-primary" />
                    Action Details
                  </CardTitle>
                </CardHeader>
                <CardContent>{renderModalContent()}</CardContent>
              </Card>
            </div>
          </div>

          {/* Fixed Footer */}
          <DialogFooter className="px-6 py-4 border-t bg-white">
            <Button variant="outline" onClick={closeModal}>
              Cancel
            </Button>
            {showItem.actionType === "verify_action" ? (
              <Button
                onClick={handleSubmit}
                disabled={isLoading}
                variant="default"
              >
                {isLoading
                  ? "Submitting..."
                  : apiStatus === "Return"
                  ? "Return to Action Owner"
                  : "Approve"}
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={isLoading}
                variant="default"
              >
                {isLoading ? "Submitting..." : "Submit"}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ActionsModal;
