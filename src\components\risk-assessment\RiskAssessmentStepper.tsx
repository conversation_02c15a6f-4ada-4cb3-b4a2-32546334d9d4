import React from 'react';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export type Step = {
  id: number;
  label: string;
  description: string;
};

interface RiskAssessmentStepperProps {
  steps: Step[];
  currentStep: number;
  onStepClick?: (step: number) => void;
}

const RiskAssessmentStepper: React.FC<RiskAssessmentStepperProps> = ({
  steps,
  currentStep,
  onStepClick,
}) => {
  return (
    <div className="w-full py-6">
      <div className="stepper-container">
        {steps.map((step, index) => {
          const isCompleted = currentStep > step.id;
          const isCurrent = currentStep === step.id;
          const isClickable = onStepClick && (isCompleted || step.id === currentStep);

          return (
            <div key={step.id} className="stepper-item">
              <div className="flex flex-col items-center">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div
                        className={cn(
                          "stepper-step",
                          isCurrent && "stepper-step-active",
                          isCompleted && "stepper-step-completed",
                          isClickable ? "cursor-pointer" : "cursor-default"
                        )}
                        onClick={() => isClickable && onStepClick(step.id)}
                        aria-current={isCurrent ? "step" : undefined}
                      >
                        <div className="stepper-circle">
                          {isCompleted ? (
                            <Check className="h-5 w-5 animate-check" />
                          ) : (
                            <span className="text-sm font-medium">{step.id}</span>
                          )}
                        </div>
                        <span
                          className={cn(
                            "stepper-label",
                            isCurrent ? "text-foreground" : "text-muted-foreground"
                          )}
                        >
                          {step.label}
                        </span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent className="p-3 max-w-[200px]">
                      <p>{step.description}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              {index < steps.length - 1 && (
                <div
                  className={cn(
                    "stepper-connector",
                    isCompleted ? "stepper-connector-active" : "bg-muted-foreground/30"
                  )}
                />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default RiskAssessmentStepper;
