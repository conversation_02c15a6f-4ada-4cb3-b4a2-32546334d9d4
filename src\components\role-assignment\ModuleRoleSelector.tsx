import React from 'react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MultiSelect, OptionType } from '@/components/ui/multi-select';
import { UseFormReturn } from 'react-hook-form';
import { MODULES } from '@/types/roleAssignment';

interface ModuleRoleSelectorProps {
  form: UseFormReturn<any>;
  isLocationComplete: boolean;
}

const ModuleRoleSelector: React.FC<ModuleRoleSelectorProps> = ({ form, isLocationComplete }) => {
  const selectedModule = form.watch('module');
  
  // Get the roles for the selected module
  const moduleRoles = MODULES.find(m => m.id === selectedModule)?.roles || [];
  
  // Convert roles to options format for MultiSelect
  const roleOptions: OptionType[] = moduleRoles.map(role => ({
    label: role,
    value: role
  }));

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Module & Role Selection</h3>
      
      <Card className={!isLocationComplete ? "opacity-50" : ""}>
        <CardHeader className="pb-3">
          <CardTitle className="text-md">Select Module and Roles</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="module"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Module</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={!isLocationComplete}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select module" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {MODULES.map(module => (
                        <SelectItem key={module.id} value={module.id}>{module.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="roles"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Roles</FormLabel>
                  <FormControl>
                    <MultiSelect
                      options={roleOptions}
                      selected={field.value || []}
                      onChange={field.onChange}
                      placeholder="Select roles"
                      disabled={!isLocationComplete || !selectedModule}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ModuleRoleSelector;
