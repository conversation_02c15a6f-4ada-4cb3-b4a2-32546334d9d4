import { useState, useEffect } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import apiService from "@/services/apiService";

export interface LocationFilterState {
  country: string;
  region: string;
  site: string;
}

interface LocationItem {
  id: string;
  name: string;
}

interface CascadingLocationFilterProps {
  compact?: boolean;
  showTitle?: boolean;
  filterState?: LocationFilterState;
  onFilterChange?: (filterType: keyof LocationFilterState, value: string) => void;
}

const CascadingLocationFilter = ({
  compact = false,
  showTitle = true,
  filterState = { country: "all", region: "all", site: "all" },
  onFilterChange
}: CascadingLocationFilterProps) => {
  // Location data states
  const [countries, setCountries] = useState<LocationItem[]>([]);
  const [regions, setRegions] = useState<LocationItem[]>([]);
  const [sites, setSites] = useState<LocationItem[]>([]);

  // Loading states
  const [isLoadingCountries, setIsLoadingCountries] = useState(false);
  const [isLoadingRegions, setIsLoadingRegions] = useState(false);
  const [isLoadingSites, setIsLoadingSites] = useState(false);

  // Fetch countries on component mount
  useEffect(() => {
    const fetchCountries = async () => {
      setIsLoadingCountries(true);
      try {
        const data = await apiService.get('/location-ones');
        setCountries(data || []);
      } catch (error) {
        console.error('Failed to fetch countries:', error);
        setCountries([]);
      } finally {
        setIsLoadingCountries(false);
      }
    };

    fetchCountries();
  }, []);

  // Fetch regions when country changes
  useEffect(() => {
    const fetchRegions = async () => {
      if (!filterState.country || filterState.country === 'all') {
        setRegions([]);
        return;
      }

      setIsLoadingRegions(true);
      try {
        const data = await apiService.get(`/location-ones/${filterState.country}/location-twos`);
        setRegions(data || []);
      } catch (error) {
        console.error('Failed to fetch regions:', error);
        setRegions([]);
      } finally {
        setIsLoadingRegions(false);
      }
    };

    fetchRegions();
    // Reset dependent dropdowns
    setSites([]);
  }, [filterState.country]);

  // Fetch sites when region changes
  useEffect(() => {
    const fetchSites = async () => {
      if (!filterState.region || filterState.region === 'all') {
        setSites([]);
        return;
      }

      setIsLoadingSites(true);
      try {
        const data = await apiService.get(`/location-twos/${filterState.region}/location-threes`);
        setSites(data || []);
      } catch (error) {
        console.error('Failed to fetch sites:', error);
        setSites([]);
      } finally {
        setIsLoadingSites(false);
      }
    };

    fetchSites();
  }, [filterState.region]);

  const handleFilterChange = (filterType: keyof LocationFilterState, value: string) => {
    // Reset dependent filters when parent changes
    if (filterType === 'country') {
      onFilterChange?.('region', 'all');
      onFilterChange?.('site', 'all');
    } else if (filterType === 'region') {
      onFilterChange?.('site', 'all');
    }

    onFilterChange?.(filterType, value);
  };

  return (
    <div className={`bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200 ${compact ? 'p-4' : 'p-6'} mb-6`}>
      {showTitle && (
        <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">Location Filters</h3>
            <p className="text-sm text-gray-600">Filter data by location hierarchy</p>
          </div>
        </div>
      )}

      <div className="flex flex-wrap items-end gap-3">
        {/* Location hierarchy filters */}
        <div className="flex flex-wrap gap-4 flex-1">
          {/* Country */}
          <div className="min-w-[180px]">
            <label className="block text-xs font-medium text-gray-700 mb-1">Country</label>
            <Select value={filterState.country} onValueChange={(value) => handleFilterChange("country", value)}>
              <SelectTrigger className="h-9 text-sm bg-white/80 border-gray-300 hover:border-blue-400 focus:border-blue-500 transition-colors">
                <SelectValue placeholder="Select Country" />
                {isLoadingCountries && <Loader2 className="h-4 w-4 animate-spin ml-2" />}
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-xl z-50 rounded-lg">
                <SelectItem value="all">All Countries</SelectItem>
                {countries.map((country) => (
                  <SelectItem key={country.id} value={country.id}>
                    {country.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Region */}
          <div className="min-w-[180px]">
            <label className="block text-xs font-medium text-gray-700 mb-1">Region</label>
            <Select 
              value={filterState.region} 
              onValueChange={(value) => handleFilterChange("region", value)}
              disabled={!filterState.country || filterState.country === 'all' || isLoadingRegions}
            >
              <SelectTrigger className="h-9 text-sm bg-white/80 border-gray-300 hover:border-blue-400 focus:border-blue-500 transition-colors disabled:opacity-50">
                <SelectValue placeholder="Select Region" />
                {isLoadingRegions && <Loader2 className="h-4 w-4 animate-spin ml-2" />}
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-xl z-50 rounded-lg">
                <SelectItem value="all">All Regions</SelectItem>
                {regions.map((region) => (
                  <SelectItem key={region.id} value={region.id}>
                    {region.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Site */}
          <div className="min-w-[180px]">
            <label className="block text-xs font-medium text-gray-700 mb-1">Site</label>
            <Select 
              value={filterState.site} 
              onValueChange={(value) => handleFilterChange("site", value)}
              disabled={!filterState.region || filterState.region === 'all' || isLoadingSites}
            >
              <SelectTrigger className="h-9 text-sm bg-white/80 border-gray-300 hover:border-blue-400 focus:border-blue-500 transition-colors disabled:opacity-50">
                <SelectValue placeholder="Select Site" />
                {isLoadingSites && <Loader2 className="h-4 w-4 animate-spin ml-2" />}
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-xl z-50 rounded-lg">
                <SelectItem value="all">All Sites</SelectItem>
                {sites.map((site) => (
                  <SelectItem key={site.id} value={site.id}>
                    {site.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

        </div>
      </div>
    </div>
  );
};

export default CascadingLocationFilter;
