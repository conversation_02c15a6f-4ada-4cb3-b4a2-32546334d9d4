import React, { useState, useEffect } from 'react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { UseFormReturn } from 'react-hook-form';
import { COUNTRIES, REGIONS, SITES, LEVELS } from '@/types/roleAssignment';

interface LocationSelectorProps {
  form: UseFormReturn<any>;
}

const LocationSelector: React.FC<LocationSelectorProps> = ({ form }) => {
  const [availableRegions, setAvailableRegions] = useState<string[]>([]);
  const [availableSites, setAvailableSites] = useState<string[]>([]);
  const [availableLevels, setAvailableLevels] = useState<string[]>([]);

  // Watch for changes in the country, region, and site fields
  const country = form.watch('country');
  const region = form.watch('region');
  const site = form.watch('site');

  // Update available regions when country changes
  useEffect(() => {
    if (country) {
      const regions = REGIONS[country] || [];
      setAvailableRegions(regions);
      
      // Reset region, site, and level when country changes
      form.setValue('region', '');
      form.setValue('site', '');
      form.setValue('level', '');
    } else {
      setAvailableRegions([]);
    }
  }, [country, form]);

  // Update available sites when region changes
  useEffect(() => {
    if (region) {
      const sites = SITES[region] || [];
      setAvailableSites(sites);
      
      // Reset site and level when region changes
      form.setValue('site', '');
      form.setValue('level', '');
    } else {
      setAvailableSites([]);
    }
  }, [region, form]);

  // Update available levels when site changes
  useEffect(() => {
    if (site) {
      const levels = LEVELS[site] || LEVELS['default'] || [];
      setAvailableLevels(levels);
      
      // Reset level when site changes
      form.setValue('level', '');
    } else {
      setAvailableLevels([]);
    }
  }, [site, form]);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Location Selection</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="country"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Country</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select country" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {COUNTRIES.map(country => (
                    <SelectItem key={country} value={country}>{country}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="region"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Region</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value}
                disabled={!country || availableRegions.length === 0}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {availableRegions.map(region => (
                    <SelectItem key={region} value={region}>{region}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="site"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Site</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value}
                disabled={!region || availableSites.length === 0}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select site" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {availableSites.map(site => (
                    <SelectItem key={site} value={site}>{site}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="level"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Level</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value}
                disabled={!site || availableLevels.length === 0}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select level" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {availableLevels.map(level => (
                    <SelectItem key={level} value={level}>{level}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

export default LocationSelector;
