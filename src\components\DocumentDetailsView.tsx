import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Eye, FileText } from 'lucide-react';
import ImageComponent from '@/components/common/ImageComponent';
import apiService from '@/services/apiService';
import { useToast } from '@/components/ui/use-toast';

interface DocumentUser {
  id: string;
  firstName: string;
  lastName?: string;
  email?: string;
  company?: string;
}

interface DocumentCategory {
  id: string;
  name: string;
  level?: number;
}

interface DocumentComponent {
  id: string;
  type: string;
  content: any;
  position: number;
}

interface Document {
  id: string;
  name: string;
  type: string;
  size: string;
  uploadedBy: string;
  uploadedDate: string;
  category: string;
  tags: string[];
  description?: string;
  maskId?: string;
  scopeApplicability?: string;
  purpose?: string;
  keywords?: string;
  docId?: string;
  created?: string;
  updated?: string;
  creatorTargetDate?: string;
  reviewerTargetDate?: string;
  approverTargetDate?: string;
  initiatorId?: string;
  creatorId?: string;
  reviewerId?: string;
  approverId?: string;
  documentCategoryId?: string;
  version?: string;
  initiator?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  creator?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  reviewer?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  approver?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  documentCategory?: {
    id: string;
    name: string;
    level?: number;
  };
  files?: string;
  status?: string;
  value?: DocumentComponent[];
}

interface DocumentUpdate {
  id: string;
  reasonReview: string;
  changes: string;
  reasonChange: string;
  initiatedBy: string;
  approvedBy: string;
  reference: string;
  files?: string[];
  createdAt: string;
  updatedAt: string;
}

interface DocumentDetailsViewProps {
  document: Document | null;
  loading?: boolean;
}

const DocumentDetailsView: React.FC<DocumentDetailsViewProps> = ({ document, loading = false }) => {
  const { toast } = useToast();
  const [documentUpdates, setDocumentUpdates] = useState<DocumentUpdate[]>([]);
  const [loadingUpdates, setLoadingUpdates] = useState(false);
  const [selectedUpdate, setSelectedUpdate] = useState<DocumentUpdate | null>(null);
  const [isUpdateDetailsOpen, setIsUpdateDetailsOpen] = useState(false);

  // Fetch document updates when document changes
  useEffect(() => {
    if (document?.id) {
      fetchDocumentUpdates(document.id);
    }
  }, [document?.id]);

  const fetchDocumentUpdates = async (documentId: string) => {
    try {
      setLoadingUpdates(true);
      const response = await apiService.get(`/documents/${documentId}/document-updates`);
      setDocumentUpdates(response || []);
    } catch (error) {
      console.error('Error fetching document updates:', error);
      toast({
        title: "Error",
        description: "Failed to load document updates.",
        variant: "destructive"
      });
    } finally {
      setLoadingUpdates(false);
    }
  };

  const handleViewUpdate = (update: DocumentUpdate) => {
    setSelectedUpdate(update);
    setIsUpdateDetailsOpen(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-600">Loading document details...</p>
        </div>
      </div>
    );
  }

  if (!document) {
    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="font-medium text-gray-900 mb-4">Document Details</h3>
        <p className="text-gray-600">No document details available.</p>
      </div>
    );
  }

  if (!document) {
    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="font-medium text-gray-900 mb-4">Document Details</h3>
        <p className="text-gray-600">No document details available.</p>
      </div>
    );
  }

  return (
    <div className="py-6 space-y-8">
      {/* Basic Information */}
      <div className="bg-blue-50 rounded-lg p-6 space-y-6">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-2 h-6 bg-blue-500 rounded-full"></div>
          <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Document ID</Label>
            <div className="p-3 bg-white rounded-lg border">
              <span className="text-blue-600 font-medium">{document.maskId || 'N/A'}</span>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Doc ID</Label>
            <div className="p-3 bg-white rounded-lg border">
              <span className="text-gray-700 font-medium">{document.docId || 'N/A'}</span>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Document Name</Label>
            <div className="p-3 bg-white rounded-lg border">
              <span className="font-medium">{document.name}</span>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Category</Label>
            <div className="p-3 bg-white rounded-lg border">
              <span>{document.documentCategory?.name || document.category}</span>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Type</Label>
            <div className="p-3 bg-white rounded-lg border">
              <span className="capitalize">{document.type}</span>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Version</Label>
            <div className="p-3 bg-white rounded-lg border">
              <span className="font-medium text-blue-600">{document.version || 'N/A'}</span>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Created Date</Label>
            <div className="p-3 bg-white rounded-lg border">
              <span>{document.created ? format(new Date(document.created), 'MMM dd, yyyy HH:mm') : 'N/A'}</span>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Last Updated</Label>
            <div className="p-3 bg-white rounded-lg border">
              <span>{document.updated ? format(new Date(document.updated), 'MMM dd, yyyy HH:mm') : 'N/A'}</span>
            </div>
          </div>
        </div>

        {(document.keywords || (document.tags && document.tags.length > 0)) && (
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Keywords/Tags</Label>
            <div className="p-3 bg-white rounded-lg border">
              <div className="flex flex-wrap gap-2">
                {document.tags && document.tags.length > 0 ? (
                  document.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))
                ) : document.keywords ? (
                  document.keywords.split(',').map((keyword, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {keyword.trim()}
                    </Badge>
                  ))
                ) : null}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Content Information */}
      <div className="bg-green-50 rounded-lg p-6 space-y-6">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-2 h-6 bg-green-500 rounded-full"></div>
          <h3 className="text-lg font-semibold text-gray-900">Content Information</h3>
        </div>

        <div className="space-y-4">
          {document.scopeApplicability && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Scope & Applicability</Label>
              <div className="p-4 bg-white rounded-lg border">
                <p className="text-gray-800">{document.scopeApplicability}</p>
              </div>
            </div>
          )}

          {document.purpose && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Purpose</Label>
              <div className="p-4 bg-white rounded-lg border">
                <p className="text-gray-800">{document.purpose}</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* File Information - Only show for Existing documents */}
      {document.type === 'Existing' && document.files && (
        <div className="bg-orange-50 rounded-lg p-6 space-y-6">
          <div className="flex items-center gap-2 mb-4">
            <div className="w-2 h-6 bg-orange-500 rounded-full"></div>
            <h3 className="text-lg font-semibold text-gray-900">Attached File</h3>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Document File</Label>
              <div className="p-4 bg-white rounded-lg border">
                <ImageComponent
                  fileName={document.files}
                  size="32"
                  name={true}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Workflow Information */}
      <div className="bg-purple-50 rounded-lg p-6 space-y-6">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-2 h-6 bg-purple-500 rounded-full"></div>
          <h3 className="text-lg font-semibold text-gray-900">Workflow Information</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Initiator */}
          {document.initiator && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-semibold text-xs">I</span>
                </div>
                <Label className="text-sm font-medium text-gray-700">Initiator</Label>
              </div>
              <div className="p-4 bg-white rounded-lg border space-y-2">
                <p className="font-medium">{document.initiator.firstName}</p>
                {document.initiator.email && (
                  <p className="text-sm text-gray-600">{document.initiator.email}</p>
                )}
                {document.initiator.company && (
                  <p className="text-sm text-gray-600">{document.initiator.company}</p>
                )}
              </div>
            </div>
          )}

          {/* Creator */}
          {document.creator && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 font-semibold text-xs">C</span>
                </div>
                <Label className="text-sm font-medium text-gray-700">Creator</Label>
              </div>
              <div className="p-4 bg-white rounded-lg border space-y-2">
                <p className="font-medium">{document.creator.firstName}</p>
                {document.creator.email && (
                  <p className="text-sm text-gray-600">{document.creator.email}</p>
                )}
                {document.creatorTargetDate && (
                  <p className="text-sm text-amber-600 font-medium">
                    Target: {format(new Date(document.creatorTargetDate), 'MMM dd, yyyy')}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Reviewer */}
          {document.reviewer && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center">
                  <span className="text-orange-600 font-semibold text-xs">R</span>
                </div>
                <Label className="text-sm font-medium text-gray-700">Reviewer</Label>
              </div>
              <div className="p-4 bg-white rounded-lg border space-y-2">
                <p className="font-medium">{document.reviewer.firstName}</p>
                {document.reviewer.email && (
                  <p className="text-sm text-gray-600">{document.reviewer.email}</p>
                )}
                {document.reviewerTargetDate && (
                  <p className="text-sm text-amber-600 font-medium">
                    Target: {format(new Date(document.reviewerTargetDate), 'MMM dd, yyyy')}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Approver - Full width if exists */}
        {document.approver && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-purple-600 font-semibold text-xs">A</span>
              </div>
              <Label className="text-sm font-medium text-gray-700">Approver</Label>
            </div>
            <div className="p-4 bg-white rounded-lg border space-y-2 max-w-md">
              <p className="font-medium">{document.approver.firstName}</p>
              {document.approver.email && (
                <p className="text-sm text-gray-600">{document.approver.email}</p>
              )}
              {document.approverTargetDate && (
                <p className="text-sm text-amber-600 font-medium">
                  Target: {format(new Date(document.approverTargetDate), 'MMM dd, yyyy')}
                </p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Document Components - Show for documents with value field */}
      {document.value && Array.isArray(document.value) && document.value.length > 0 && (
        <div className="bg-indigo-50 rounded-lg p-6 space-y-6">
          <div className="flex items-center gap-2 mb-4">
            <div className="w-2 h-6 bg-indigo-500 rounded-full"></div>
            <h3 className="text-lg font-semibold text-gray-900">Document Components</h3>
            <Badge variant="secondary" className="ml-2">
              {document.value.length} components
            </Badge>
          </div>

          <div className="space-y-4 max-h-96 overflow-y-auto">
            {document.value
              .sort((a, b) => a.position - b.position)
              .map((component, index) => (
                <div key={component.id || index} className="p-4 bg-white rounded-lg border">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {component.type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Badge>
                      <span className="text-xs text-gray-500">Position: {component.position}</span>
                    </div>
                  </div>

                  {/* Component Content Preview */}
                  <div className="mt-2">
                    {component.type === 'document-header' && component.content?.text && (
                      <h4 className="font-semibold text-lg text-gray-900">{component.content.text}</h4>
                    )}
                    {component.type === 'section-header' && component.content?.text && (
                      <h5 className="font-medium text-base text-gray-800">{component.content.text}</h5>
                    )}
                    {component.type === 'paragraph' && component.content?.text && (
                      <p className="text-gray-700">{component.content.text}</p>
                    )}
                    {(component.type === 'bullet-list' || component.type === 'numbered-list') && component.content?.items && Array.isArray(component.content.items) && (
                      <ul className={component.type === 'numbered-list' ? 'list-decimal' : 'list-disc'} style={{ paddingLeft: '1.5rem' }}>
                        {component.content.items.map((item: string, idx: number) => (
                          <li key={idx} className="text-gray-700">{item}</li>
                        ))}
                      </ul>
                    )}
                    {component.type === 'quote' && component.content?.text && (
                      <blockquote className="border-l-4 border-gray-300 pl-4 italic text-gray-600">
                        {component.content.text}
                      </blockquote>
                    )}
                    {component.type === 'separator' && (
                      <hr className="border-gray-300" />
                    )}
                    {(component.type === 'image' || component.type === 'video' || component.type === 'file-attachment') && component.content?.filename && (
                      <div className="flex items-center gap-2">
                        <ImageComponent
                          fileName={component.content.filename}
                          size="24"
                          name={true}
                        />
                        {component.content.caption && (
                          <span className="text-sm text-gray-600 italic">"{component.content.caption}"</span>
                        )}
                      </div>
                    )}
                    {component.type === 'link' && component.content?.url && (
                      <a
                        href={component.content.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 underline"
                      >
                        {component.content.text || component.content.url}
                      </a>
                    )}
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Document Updates Section */}
      <div className="bg-purple-50 rounded-lg p-6 space-y-6">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-2 h-6 bg-purple-500 rounded-full"></div>
          <h3 className="text-lg font-semibold text-gray-900">Document Updates</h3>
        </div>

        {loadingUpdates ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 mx-auto mb-2"></div>
              <p className="text-gray-600">Loading updates...</p>
            </div>
          </div>
        ) : documentUpdates.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-600">No document updates available.</p>
          </div>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Reviews, Changes and Updates</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Reason for Review</TableHead>
                      <TableHead>Changes</TableHead>
                      <TableHead>Reason for Changes</TableHead>
                      <TableHead>Initiated By</TableHead>
                      <TableHead>Approved By</TableHead>
                      <TableHead>Reference</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {documentUpdates.map((update, index) => (
                      <TableRow key={update.id || index}>
                        <TableCell className="max-w-[200px]">
                          <div className="truncate" title={update.reasonReview}>
                            {update.reasonReview || 'N/A'}
                          </div>
                        </TableCell>
                        <TableCell className="max-w-[200px]">
                          <div className="truncate" title={update.changes}>
                            {update.changes || 'N/A'}
                          </div>
                        </TableCell>
                        <TableCell className="max-w-[200px]">
                          <div className="truncate" title={update.reasonChange}>
                            {update.reasonChange || 'N/A'}
                          </div>
                        </TableCell>
                        <TableCell>{update.initiatedBy || 'N/A'}</TableCell>
                        <TableCell>{update.approvedBy || 'N/A'}</TableCell>
                        <TableCell className="max-w-[150px]">
                          <div className="truncate" title={update.reference}>
                            {update.reference || 'N/A'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewUpdate(update)}
                            className="h-8 w-8 p-0"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Update Details Modal */}
      <Dialog open={isUpdateDetailsOpen} onOpenChange={setIsUpdateDetailsOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">Update Details</DialogTitle>
          </DialogHeader>

          {selectedUpdate && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Reason for Review</Label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    <p className="text-gray-900">{selectedUpdate.reasonReview || 'N/A'}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Changes</Label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    <p className="text-gray-900">{selectedUpdate.changes || 'N/A'}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Reason for Changes</Label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    <p className="text-gray-900">{selectedUpdate.reasonChange || 'N/A'}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Reference</Label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    <p className="text-gray-900">{selectedUpdate.reference || 'N/A'}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Initiated By</Label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    <p className="text-gray-900">{selectedUpdate.initiatedBy || 'N/A'}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Approved By</Label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    <p className="text-gray-900">{selectedUpdate.approvedBy || 'N/A'}</p>
                  </div>
                </div>
              </div>

              {/* Files Section */}
              {selectedUpdate.files && selectedUpdate.files.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Attached Files</Label>
                  <div className="p-4 bg-gray-50 rounded-lg border">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                      {selectedUpdate.files.map((fileName, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <ImageComponent
                            fileName={fileName}
                            size="24"
                            name={true}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Timestamps */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Created At</Label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    <p className="text-gray-900">
                      {selectedUpdate.createdAt ? format(new Date(selectedUpdate.createdAt), 'PPP p') : 'N/A'}
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Updated At</Label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    <p className="text-gray-900">
                      {selectedUpdate.updatedAt ? format(new Date(selectedUpdate.updatedAt), 'PPP p') : 'N/A'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DocumentDetailsView;
