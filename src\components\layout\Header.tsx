import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { User, Loader2 } from 'lucide-react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store';
import { fetchUserDetails } from '@/services/api';
import { setUser } from '@/store/slices/authSlice';
import LanguageSelector from '@/components/common/LanguageSelector';
import apiService from '@/services/apiService';

const API_URL = 'https://client-api.acuizen.com';
const FILE_DOWNLOAD = (file: string) => `${API_URL}/files/${file}/presigned-url`;

interface HeaderProps {
  companyName?: string;
  departmentName?: string;
  className?: string;
}

const Header: React.FC<HeaderProps> = ({
  companyName = 'AcuiZen WorkHub',
  departmentName = '',
  className
}) => {
  const dispatch = useDispatch();
  const { user, accessToken, loginConfig } = useSelector((state: RootState) => state.auth);
  const [logoUrl, setLogoUrl] = useState<string>('');
  const [configLoading, setConfigLoading] = useState<boolean>(true);

  // Load cached logo URL immediately on component mount
  useEffect(() => {
    const cachedLogoUrl = localStorage.getItem('headerLogoUrl');
    if (cachedLogoUrl) {
      setLogoUrl(cachedLogoUrl);
      setConfigLoading(false);
    }
  }, []);

  // Fetch user details when component mounts or when accessToken changes
  useEffect(() => {
    const loadUserDetails = async () => {
      if (!accessToken) {
        console.error('No access token available');
        return;
      }

      try {
        const userDetails = await fetchUserDetails(accessToken);
        dispatch(setUser(userDetails));
      } catch (error) {
        console.error('Failed to fetch user details:', error);
      }
    };

    if (accessToken && !user) {
      loadUserDetails();
    }
  }, [accessToken, dispatch, user]);

  // Fetch logo URL
  useEffect(() => {
    const fetchLogo = async () => {
      // First, try to use cached logo URL if available
      const cachedLogoUrl = localStorage.getItem('headerLogoUrl');
      if (cachedLogoUrl) {
        setLogoUrl(cachedLogoUrl);
        setConfigLoading(false);
      }

      // If no loginConfig yet, wait for it to be loaded
      if (!loginConfig?.LOGO) {
        if (!cachedLogoUrl) {
          setConfigLoading(false);
        }
        return;
      }

      setConfigLoading(true);
      try {
        const data = await apiService.get(FILE_DOWNLOAD(loginConfig.LOGO));

        if (data && data) {
          setLogoUrl(data);
          // Cache the logo URL
          localStorage.setItem('headerLogoUrl', data);
        }
      } catch (error) {
        console.error('Error fetching logo:', error);
        // If we don't already have a cached URL, try to get it again
        if (!cachedLogoUrl) {
          const fallbackCachedUrl = localStorage.getItem('headerLogoUrl');
          if (fallbackCachedUrl) {
            console.log('Using cached logo URL as fallback');
            setLogoUrl(fallbackCachedUrl);
          }
        }
      } finally {
        setConfigLoading(false);
      }
    };

    fetchLogo();
  }, [loginConfig]);

  return (
    <header className={cn(
      "w-full bg-white border-b border-gray-200 h-20 flex items-center shadow-sm transition-all duration-300 ease-in-out px-6",
      className
    )}>
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center gap-6">
          {/* Logo */}
          <div className="flex-shrink-0">
            {configLoading ? (
              <div className="h-20 w-20 flex items-center justify-center">
                <Loader2 className="h-10 w-10 animate-spin text-primary" />
              </div>
            ) : logoUrl ? (
              <img
                src={logoUrl}
                alt="Company Logo"
                className="h-20 object-contain"
                onError={(e) => {
                  console.error('Failed to load logo image:', e);
                  // Remove the broken image
                  setLogoUrl('');
                }}
                loading="eager"
              />
            ) : (
              <div className="h-20 w-20 bg-primary flex items-center justify-center text-white rounded-md">
                <span className="font-bold text-lg">A</span>
              </div>
            )}
          </div>

          {/* Divider */}
          <div className="h-14 w-px bg-gray-200 mx-1"></div>

          {/* Company and Department Names */}
          <div className="flex flex-col">
            <h1 className="text-lg font-semibold text-gray-900 leading-tight tracking-tight">
              {loginConfig?.COMPANY_NAME || companyName}
            </h1>
            <p className="text-sm text-gray-600">
              {loginConfig?.COMPANY_DESCRIPTION || departmentName}
            </p>
          </div>
        </div>

        {/* Right side: Language Selector and User Profile */}
        <div className="flex items-center gap-4">
          {/* Language Selector */}
          <div className="mr-2">
            <LanguageSelector variant="ghost" size="sm" />
          </div>

          {/* User Profile */}
          {user ? (
            <div className="flex items-center">
              <div className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center text-white">
                  {user.profileImage ? (
                    <img
                      src={user.profileImage}
                      alt={`${user.firstName} ${user.lastName}`}
                      className="h-8 w-8 rounded-full object-cover"
                    />
                  ) : (
                    <User size={16} />
                  )}
                </div>
                <div className="hidden md:block">
                  <p className="text-sm font-medium">
                    {user.firstName && user.lastName
                      ? `${user.firstName} ${user.lastName}`
                      : user.username || user.email || (user.signInDetails?.loginId ?? 'User')}
                  </p>
                  {user.role && (
                    <p className="text-xs text-gray-500">{user.role}</p>
                  )}
                </div>
              </div>
            </div>
          ) : accessToken ? (
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
              </div>
              <div className="hidden md:block">
                <p className="text-sm font-medium">Loading user...</p>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                <User size={16} className="text-gray-500" />
              </div>
              <div className="hidden md:block">
                <p className="text-sm font-medium">Guest User</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
