import { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import apiService from '@/services/apiService';
import { 
  User, 
  Calendar, 
  MapPin, 
  FileText, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Loader2,
  Building,
  Users
} from 'lucide-react';
import { formatDateTime } from '@/utils/dateUtils';
import SignatureCanvas from 'react-signature-canvas';

interface ActionDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  action: ActionDetails | null;
}

interface ActionDetails {
  id: string;
  maskId?: string;
  description: string;
  status: string;
  priority?: string;
  dueDate?: string;
  application: string;
  serviceId: string;
  assignedTo: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  objectId?: string;
  applicationId?: string;
  actionType?: string;
  submittedBy?: {
    id: string;
    firstName: string;
    lastName?: string;
    email: string;
    company: string;
    type: string;
    status: boolean;
    roles: string[];
    created: string;
    updated: string;
  };
  riskAssessment?: {
    id: string;
    maskId: string;
    type: string;
    status: string;
    department?: {
      id: string;
      name: string;
    };
    teamLeader?: {
      id: string;
      firstName: string;
      lastName?: string;
    };
    workActivity?: {
      id: string;
      name: string;
    };
    raTeamMembers?: Array<{
      id: string;
      user: {
        id: string;
        firstName: string;
        lastName?: string;
        email: string;
      };
    }>;
  };
}

const ActionDetailsModal: React.FC<ActionDetailsModalProps> = ({
  open,
  onOpenChange,
  action
}) => {
  const [actionDetails, setActionDetails] = useState<ActionDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const { toast } = useToast();
  const signRef = useRef<SignatureCanvas>(null);

  useEffect(() => {
    const fetchRiskAssessmentDetails = async () => {
      if (!action || !action.applicationId) {
        setActionDetails(action);
        return;
      }

      setLoading(true);
      try {
        // Get the related risk assessment details using applicationId (following test.js pattern)
        const uriString = {
          include: [
            { relation: "department" },
            { relation: "teamLeader" },
            { relation: "workActivity" },
            {
              relation: "raTeamMembers",
              scope: {
                include: [{ relation: "user" }]
              }
            }
          ]
        };

        const riskUrl = `/risk-assessments/${action.applicationId}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
        const riskResponse = await apiService.get(riskUrl);

        setActionDetails({
          ...action,
          riskAssessment: riskResponse
        });
      } catch (error) {
        console.error('Error fetching risk assessment details:', error);
        toast({
          title: "Error",
          description: "Failed to fetch risk assessment details. Please try again.",
          variant: "destructive"
        });
        // Still show action details even if risk assessment fetch fails
        setActionDetails(action);
      } finally {
        setLoading(false);
      }
    };

    if (open && action) {
      fetchRiskAssessmentDetails();
    } else if (open) {
      setActionDetails(null);
    }
  }, [open, action, toast]);

  // Convert dataURI to File (following test.js pattern)
  const dataURItoFile = (dataURI: string, filename: string) => {
    const byteString = atob(dataURI.split(',')[1]);
    const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
    const ab = new ArrayBuffer(byteString.length);
    const ia = new Uint8Array(ab);
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }
    return new File([ab], filename, { type: mimeString });
  };

  // Confirm action function (following test.js pattern)
  const onConfirm = async () => {
    const filename = new Date().getTime() + "action_sign.png";

    if (signRef.current && !signRef.current.isEmpty()) {
      const formData = new FormData();
      formData.append('file', dataURItoFile(signRef.current.getTrimmedCanvas().toDataURL("image/png"), filename));

      try {
        const response = await apiService.post('/files', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          }
        });

        if (response && response.files && response.files[0]) {
          // Update action with signature (you may need to adjust this API call based on your backend)
          const updateResponse = await apiService.patch(`/ra-team-member-submit-signature/${action?.id}`, {
            signature: response.files[0].originalname,
            signatureDate: new Date()
          });

          if (updateResponse) {
            toast({
              title: "Success",
              description: "Action confirmed successfully!",
              variant: "default"
            });
            onOpenChange(false);
          }
        }
      } catch (error) {
        console.error("Signature upload error: ", error);
        toast({
          title: "Error",
          description: "Failed to confirm action. Please try again.",
          variant: "destructive"
        });
      }
    } else {
      toast({
        title: "Signature Required",
        description: "Please provide your signature before confirming.",
        variant: "destructive"
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const statusLower = status?.toLowerCase();
    let badgeClass = '';
    switch(statusLower) {
      case 'pending': badgeClass = 'bg-warning-400 hover:bg-warning-500 text-black'; break;
      case 'in progress': badgeClass = 'bg-primary hover:bg-primary/90'; break;
      case 'completed': badgeClass = 'bg-success-500 hover:bg-success-600'; break;
      default: badgeClass = 'bg-muted hover:bg-muted/90';
    }
    return <Badge className={badgeClass}>{status}</Badge>;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-hidden bg-white">
        <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200 p-6 -m-6 mb-0">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                <FileText className="w-6 h-6 text-white" />
              </div>
              <div className="space-y-2">
                <DialogTitle className="text-2xl font-bold text-gray-800">
                  Action Details
                </DialogTitle>
                <p className="text-gray-600 text-lg font-medium">
                  {actionDetails?.maskId || action?.maskId || action?.id || 'Loading...'}
                </p>
                {/* <div className="flex items-center gap-2">
                  {actionDetails?.status && getStatusBadge(actionDetails.status)}
                </div> */}
              </div>
            </div>
          </div>
        </DialogHeader>

        <div className="overflow-y-auto max-h-[calc(95vh-120px)] p-6">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading action details...</span>
            </div>
          ) : actionDetails ? (
            <div className="space-y-6">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="overview" className="flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  Overview
                </TabsTrigger>

                <TabsTrigger value="team" className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Team & Timeline
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
            
          

                {/* Risk Assessment Details */}
                {actionDetails.riskAssessment && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <AlertTriangle className="w-5 h-5 text-orange-600" />
                        Related Risk Assessment
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <FileText className="w-4 h-4 text-gray-500" />
                            <h6 className="font-semibold text-gray-700">RA ID</h6>
                          </div>
                          <p className="text-blue-600 font-medium pl-6">{actionDetails.riskAssessment.maskId}</p>
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <AlertTriangle className="w-4 h-4 text-gray-500" />
                            <h6 className="font-semibold text-gray-700">Assessment Type</h6>
                          </div>
                          <p className="text-gray-600 pl-6">{actionDetails.riskAssessment.type}</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-gray-500" />
                            <h6 className="font-semibold text-gray-700">Assessment Status</h6>
                          </div>
                          <div className="pl-6">{getStatusBadge(actionDetails.riskAssessment.status)}</div>
                        </div>
                        {actionDetails.riskAssessment.department && (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Building className="w-4 h-4 text-gray-500" />
                              <h6 className="font-semibold text-gray-700">Department</h6>
                            </div>
                            <p className="text-gray-600 pl-6">{actionDetails.riskAssessment.department.name}</p>
                          </div>
                        )}
                      </div>

                      {actionDetails.riskAssessment.workActivity && (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <AlertTriangle className="w-4 h-4 text-gray-500" />
                            <h6 className="font-semibold text-gray-700">Work Activity</h6>
                          </div>
                          <p className="text-gray-600 pl-6">{actionDetails.riskAssessment.workActivity.name}</p>
                        </div>
                      )}

                      {actionDetails.riskAssessment.teamLeader && (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <User className="w-4 h-4 text-gray-500" />
                            <h6 className="font-semibold text-gray-700">Team Leader</h6>
                          </div>
                          <p className="text-gray-600 pl-6">
                            {`${actionDetails.riskAssessment.teamLeader.firstName}${actionDetails.riskAssessment.teamLeader.lastName ? ` ${actionDetails.riskAssessment.teamLeader.lastName}` : ''}`}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </TabsContent>


              <TabsContent value="team" className="space-y-6">
                {/* Submitted By Information */}
                {actionDetails.submittedBy && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <User className="w-5 h-5 text-blue-600" />
                        Submitted By
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <User className="w-4 h-4 text-gray-500" />
                            <h6 className="font-semibold text-gray-700">Name</h6>
                          </div>
                          <p className="text-gray-600 pl-6">
                            {`${actionDetails.submittedBy.firstName}${actionDetails.submittedBy.lastName ? ` ${actionDetails.submittedBy.lastName}` : ''}`}
                          </p>
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Building className="w-4 h-4 text-gray-500" />
                            <h6 className="font-semibold text-gray-700">Company</h6>
                          </div>
                          <p className="text-gray-600 pl-6">{actionDetails.submittedBy.company}</p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-gray-500" />
                          <h6 className="font-semibold text-gray-700">Email</h6>
                        </div>
                        <p className="text-gray-600 pl-6">{actionDetails.submittedBy.email}</p>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Timeline Information */}
            

                {/* Team Information */}
                {actionDetails.riskAssessment && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Users className="w-5 h-5 text-purple-600" />
                        Risk Assessment Team
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {actionDetails.riskAssessment.teamLeader && (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <User className="w-4 h-4 text-gray-500" />
                            <h6 className="font-semibold text-gray-700">Team Leader</h6>
                          </div>
                          <p className="text-gray-600 pl-6">
                            {`${actionDetails.riskAssessment.teamLeader.firstName}${actionDetails.riskAssessment.teamLeader.lastName ? ` ${actionDetails.riskAssessment.teamLeader.lastName}` : ''}`}
                          </p>
                        </div>
                      )}

                      {actionDetails.riskAssessment.raTeamMembers && actionDetails.riskAssessment.raTeamMembers.length > 0 && (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Users className="w-4 h-4 text-gray-500" />
                            <h6 className="font-semibold text-gray-700">Team Members</h6>
                          </div>
                          <div className="pl-6 space-y-1">
                            {actionDetails.riskAssessment.raTeamMembers.map((member, index) => (
                              <p key={index} className="text-gray-600">
                                • {`${member.user.firstName}${member.user.lastName ? ` ${member.user.lastName}` : ''}`}
                              </p>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
            </Tabs>

            {/* Action Confirmation Section - Bottom of Modal */}
            <div className="mt-6 pt-6 border-t">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    Action Confirmation
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="text-center">
                    <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <p className="text-gray-700 leading-relaxed">
                        I confirm my participation in this Risk Assessment as a team member.
                        The outcome reflects our shared professional judgment to the best of our abilities through consensus.
                      </p>
                    </div>

                    <div className="flex flex-col items-center space-y-4">
                      <div className="relative">
                        <SignatureCanvas
                          penColor="#1F3BB3"
                          canvasProps={{
                            width: 350,
                            height: 100,
                            className: "sigCanvas border rounded-lg",
                            style: {
                              boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                            },
                          }}
                          ref={signRef}
                        />
                        <button
                          type="button"
                          className="absolute -top-2 -right-2 w-8 h-8 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg transition-colors"
                          onClick={() => signRef.current?.clear()}
                          title="Clear signature"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>

                      <p className="text-sm text-gray-500">
                        Please sign above to confirm your participation
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3 pt-6 border-t mt-6">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Close
              </Button>
              <Button onClick={onConfirm}>
                <CheckCircle className="h-4 w-4 mr-2" />
                Done
              </Button>
            </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-64">
              <p className="text-muted-foreground">No details available</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ActionDetailsModal;
