
import React, { useState, useEffect } from 'react';
import Sidebar from './Sidebar';
import Header from './Header';
import { cn } from '@/lib/utils';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store';
import { fetchUserDetails } from '@/services/api';
import { setUser } from '@/store/slices/authSlice';

interface PageLayoutProps {
  children: React.ReactNode;
}

const PageLayout: React.FC<PageLayoutProps> = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true);
  const dispatch = useDispatch();
  const { accessToken, user } = useSelector((state: RootState) => state.auth);

  // Fetch user details when component mounts or when accessToken changes
  useEffect(() => {
    const loadUserDetails = async () => {
      if (!accessToken) {
        console.error('No access token available');
        return;
      }

      try {
        const userDetails = await fetchUserDetails(accessToken);
        dispatch(setUser(userDetails));
      } catch (error) {
        console.error('Failed to fetch user details:', error);
      }
    };

    if (accessToken && !user) {
      loadUserDetails();
    }
  }, [accessToken, dispatch, user]);

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header covers the full page width */}
      <Header />

      {/* Content area with sidebar and main content */}
      <div className="flex flex-1 overflow-hidden">
        <Sidebar collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
        <main
          className={cn(
            "flex-1 transition-all duration-300 ease-in-out overflow-auto"
          )}
        >
          <div className="w-full p-6 animate-fade-in">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default PageLayout;
