
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { Loader2 } from "lucide-react";
import { setLogout } from "@/store/slices/authSlice";

const LogoutPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  useEffect(() => {
    // Handle logout logic
    dispatch(setLogout());

    // Redirect to login page after a short delay
    const logoutTimer = setTimeout(() => {
      navigate("/login");
    }, 2000);

    return () => clearTimeout(logoutTimer);
  }, [navigate, dispatch]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center">
      <div className="text-center space-y-4">
        <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
        <h1 className="text-2xl font-bold">Logging you out...</h1>
        <p className="text-muted-foreground">Thank you for using SafeAction</p>
      </div>
    </div>
  );
};

export default LogoutPage;
