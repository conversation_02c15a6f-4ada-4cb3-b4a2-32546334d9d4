
import { But<PERSON> } from "@/components/ui/button";
import { 
  <PERSON>, 
  CardContent, 
  CardDescription, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON><PERSON>er, 
  CardTitle 
} from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";

interface ActionCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  count: number;
  link: string;
  tooltipText: string;
}

const ActionCard = ({ 
  title, 
  description, 
  icon, 
  count, 
  link,
  tooltipText
}: ActionCardProps) => {
  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-md hover:-translate-y-1">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <div className="p-1.5 rounded-md bg-primary/10 text-primary">
              {icon}
            </div>
            {title}
          </CardTitle>
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="badge badge-primary">{count}</div>
              </TooltipTrigger>
              <TooltipContent>{count} pending actions</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      {/* <CardContent>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <p className="text-sm text-muted-foreground">
                {tooltipText}
              </p>
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">{tooltipText}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </CardContent> */}
      <CardFooter>
        <Button asChild variant="ghost" className="ml-auto gap-1 hover:gap-2 transition-all">
          <Link to={link}>
            View <ArrowRight className="h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ActionCard;
