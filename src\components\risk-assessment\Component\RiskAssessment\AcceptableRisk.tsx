import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

interface TaskItem {
  accept: boolean;
  [key: number]: any;
}

interface AcceptableRiskProps {
  item: TaskItem[];
  onChangeReAss: (value: boolean) => void;
}

const AcceptableRisk: React.FC<AcceptableRiskProps> = ({ item, onChangeReAss }) => {
  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row items-center justify-center gap-6">
            <div className="text-center md:text-right">
              <h6 className="text-lg font-semibold">Is this Risk Level Acceptable?</h6>
            </div>
            <div className="flex gap-3">
              <Button
                variant={item[5]?.accept === true ? "default" : "outline"}
                onClick={() => onChangeReAss(true)}
                className={`px-8 py-2 ${
                  item[5]?.accept === true 
                    ? 'bg-green-600 hover:bg-green-700 text-white' 
                    : 'hover:bg-green-50 hover:text-green-700 hover:border-green-300'
                }`}
              >
                Yes
              </Button>
              <Button
                variant={item[5]?.accept === false ? "destructive" : "outline"}
                onClick={() => onChangeReAss(false)}
                className={`px-8 py-2 ${
                  item[5]?.accept === false 
                    ? 'bg-red-600 hover:bg-red-700 text-white' 
                    : 'hover:bg-red-50 hover:text-red-700 hover:border-red-300'
                }`}
              >
                No
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {item[5]?.accept === false && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-center gap-3 text-orange-800">
              <AlertTriangle className="h-5 w-5" />
              <p className="font-semibold text-center">
                Go to Section 5 and add "Additional Controls" to reduce the risk from this sub-activity to acceptable levels.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AcceptableRisk;
