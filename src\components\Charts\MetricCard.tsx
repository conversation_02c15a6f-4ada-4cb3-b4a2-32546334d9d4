import { useMemo } from "react";
import { ArrowUp, ArrowDown, Zap, Droplets, Leaf, Users, Shield, BarChart3, Factory, Recycle, ChevronDown, ChevronUp, CheckCircle, Clock, AlertTriangle, RotateCcw, MoreHorizontal, Download, Share2, Maximize2, RefreshCw } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/Charts/ui/dropdown-menu";

interface TrendDataPoint {
  date: string;
  value: number;
}

interface MetricCardProps {
  id?: string;
  title: string;
  value: string;
  unit: string;
  target?: number;
  targetPercentage: number;
  trend: number[] | TrendDataPoint[];
  isImproving: boolean;
  mtdVariationFromPY?: number;
  mtdCurrentValue?: number;
  mtdPreviousYearValue?: number;
  showDetails?: boolean;
  onToggleDetails?: () => void;
  selectedPeriod?: string;
  onPeriodChange?: (period: string) => void;
}

const MetricCard = ({
  title,
  value,
  unit,
  targetPercentage,
  trend,
  isImproving,
  mtdVariationFromPY,
  mtdCurrentValue,
  mtdPreviousYearValue,
  showDetails = false,
  onToggleDetails,
  selectedPeriod = '1Y',
  onPeriodChange
}: MetricCardProps) => {
  const getIcon = () => {
    // Observation-specific icons
    if (title.toLowerCase().includes('rectified on-the-spot')) return CheckCircle;
    if (title.toLowerCase().includes('closed within stipulated time')) return Clock;
    if (title.toLowerCase().includes('overdue')) return AlertTriangle;
    if (title.toLowerCase().includes('repetitive')) return RotateCcw;

    // Legacy icons for other metrics
    if (title.toLowerCase().includes('emission')) return Factory;
    if (title.toLowerCase().includes('energy')) return Zap;
    if (title.toLowerCase().includes('water')) return Droplets;
    if (title.toLowerCase().includes('waste')) return Recycle;
    if (title.toLowerCase().includes('renewable')) return Leaf;
    if (title.toLowerCase().includes('satisfaction') || title.toLowerCase().includes('training') || title.toLowerCase().includes('diversity') || title.toLowerCase().includes('safety')) return Users;
    if (title.toLowerCase().includes('compliance') || title.toLowerCase().includes('audit') || title.toLowerCase().includes('policy') || title.toLowerCase().includes('risk')) return Shield;
    return BarChart3;
  };

  const getCategory = () => {
    // Observation-specific categories
    if (title.toLowerCase().includes('observations')) return 'Observations';

    // Legacy categories for other metrics
    if (title.toLowerCase().includes('emission') || title.toLowerCase().includes('energy') || title.toLowerCase().includes('water') || title.toLowerCase().includes('waste') || title.toLowerCase().includes('renewable')) return 'Environmental';
    if (title.toLowerCase().includes('satisfaction') || title.toLowerCase().includes('training') || title.toLowerCase().includes('diversity') || title.toLowerCase().includes('safety')) return 'Social';
    if (title.toLowerCase().includes('compliance') || title.toLowerCase().includes('audit') || title.toLowerCase().includes('policy') || title.toLowerCase().includes('risk')) return 'Governance';
    return 'Overview';
  };

  const getIconColor = () => {
    const category = getCategory();
    if (category === 'Observations') return 'bg-indigo-500';
    if (category === 'Environmental') return 'bg-green-500';
    if (category === 'Social') return 'bg-purple-500';
    if (category === 'Governance') return 'bg-orange-500';
    return 'bg-blue-500';
  };

  const Icon = getIcon();
  const category = getCategory();
  const iconColor = getIconColor();

  const calculatedMTDVariation = mtdVariationFromPY !== undefined
    ? mtdVariationFromPY
    : (mtdCurrentValue !== undefined && mtdPreviousYearValue !== undefined && mtdPreviousYearValue !== 0)
      ? ((mtdCurrentValue - mtdPreviousYearValue) / mtdPreviousYearValue) * 100
      : null;

  const isMTDImproving = calculatedMTDVariation !== null
    ? (title.toLowerCase().includes('emission') || title.toLowerCase().includes('energy') || title.toLowerCase().includes('water') || title.toLowerCase().includes('waste'))
      ? calculatedMTDVariation < 0
      : calculatedMTDVariation > 0
    : isImproving;

  const changeColor = isImproving ? 'text-emerald-600' : 'text-rose-600';
  const changeBgColor = isImproving ? 'bg-emerald-50' : 'bg-rose-50';

  const getTrendColor = () => {
    if (isImproving) {
      return '#10b981';
    } else {
      return '#ef4444';
    }
  };
  const trendColor = getTrendColor();

  const mtdChangeColor = isMTDImproving ? 'text-emerald-600' : 'text-rose-600';
  const mtdChangeBgColor = isMTDImproving ? 'bg-emerald-50' : 'bg-rose-50';

  const timePeriods = ['3M', '6M', '1Y', 'Max'];

  const filteredTrendData = useMemo(() => {
    let trendData: TrendDataPoint[];

    if (typeof trend[0] === 'number') {
      const now = new Date();
      trendData = (trend as number[]).map((value, index) => {
        const date = new Date(now);
        date.setMonth(date.getMonth() - (trend.length - 1 - index));
        return {
          date: date.toISOString().slice(0, 7),
          value: value
        };
      });
    } else {
      trendData = trend as TrendDataPoint[];
    }

    const now = new Date();
    let cutoffDate: Date;

    switch (selectedPeriod) {
      case '3M':
        cutoffDate = new Date(now);
        cutoffDate.setMonth(cutoffDate.getMonth() - 3);
        break;
      case '6M':
        cutoffDate = new Date(now);
        cutoffDate.setMonth(cutoffDate.getMonth() - 6);
        break;
      case '1Y':
        cutoffDate = new Date(now);
        cutoffDate.setFullYear(cutoffDate.getFullYear() - 1);
        break;
      case 'Max':
      default:
        return trendData;
    }

    return trendData.filter(point => new Date(point.date) >= cutoffDate);
  }, [trend, selectedPeriod]);

  const trendValues = filteredTrendData.map(point => point.value);

  if (trendValues.length === 0) {
    return (
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-4">
        <div className="text-center text-gray-500 py-8">
          No data available for selected time period
        </div>
      </div>
    );
  }

  const maxTrend = Math.max(...trendValues);
  const minTrend = Math.min(...trendValues);

  const dataRange = maxTrend - minTrend;
  const padding_percent = 0.1;

  const yAxisMin = dataRange < minTrend * 0.2
    ? Math.max(0, minTrend - dataRange * padding_percent)
    : Math.max(0, minTrend * 0.9);

  const yAxisMax = dataRange < minTrend * 0.2
    ? maxTrend + dataRange * padding_percent
    : maxTrend * 1.1;

  const range = yAxisMax - yAxisMin;

  const svgWidth = 450;
  const svgHeight = 200;
  const padding = 20;
  const topPadding = 25; // Extra space for Y-axis label
  const chartWidth = svgWidth - padding * 2;

  const pointSpacing = filteredTrendData.length > 1 ? chartWidth / (filteredTrendData.length - 1) : 0;

  return (
    <div className={`bg-white rounded-2xl shadow-sm border-2 p-4 hover:shadow-md transition-all duration-200 hover:-translate-y-0.5 relative ${
      showDetails
        ? 'border-blue-500 shadow-lg bg-blue-50/30'
        : 'border-gray-200'
    }`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-xl ${iconColor} text-white shadow-sm`}>
            <Icon className="w-4 h-4" />
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900 leading-tight font-sans">{title}</h3>
            <p className="text-xs text-gray-500 font-medium">{category}</p>
          </div>
        </div>

        {/* Chart Context Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-gray-500 hover:text-gray-700">
              <MoreHorizontal className="w-4 h-4" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem className="flex items-center space-x-2">
              <RefreshCw className="w-4 h-4" />
              <span>Refresh Data</span>
            </DropdownMenuItem>
            <DropdownMenuItem className="flex items-center space-x-2">
              <Maximize2 className="w-4 h-4" />
              <span>View Full Screen</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Export as PNG</span>
            </DropdownMenuItem>
            <DropdownMenuItem className="flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Export as PDF</span>
            </DropdownMenuItem>
            <DropdownMenuItem className="flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Export Data (CSV)</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="flex items-center space-x-2">
              <Share2 className="w-4 h-4" />
              <span>Share Chart</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="mb-4">
        <div className="flex items-center justify-between text-3xl font-bold text-gray-900 tracking-tight font-sans">
          <span>
            {value}
            <span className="text-base font-medium text-gray-500 ml-2">{unit}</span>
          </span>
          <span className={`flex items-center space-x-1 text-xs font-semibold px-2 py-1 rounded-full ${changeColor} ${changeBgColor}`}
            style={{ whiteSpace: 'nowrap' }}>
            {isImproving ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />}
            <span>{isImproving ? '+' : ''}{targetPercentage}% vs Target</span>
          </span>
        </div>

        {/* Previous Month Text */}
        <div className="text-xs text-gray-500 mt-1">
          From the last month of {(() => {
            const now = new Date();
            const previousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            return previousMonth.toLocaleDateString('en-US', {
              month: 'long',
              year: 'numeric'
            });
          })()}
        </div>
      </div>

      {/* Only show MTD variation if present */}
      {calculatedMTDVariation !== null && (
        <div className="flex items-center justify-between mb-4">
          <div className={`flex items-center space-x-2 ${mtdChangeColor} ${mtdChangeBgColor} px-3 py-1.5 rounded-full`}>
            <span className="text-sm font-semibold">
              {calculatedMTDVariation > 0 ? '↑' : '↓'} {calculatedMTDVariation > 0 ? '+' : ''}{calculatedMTDVariation.toFixed(1)}%
            </span>
          </div>
          <div className="text-xs text-gray-400 font-medium">
            MTD vs PY
          </div>
        </div>
      )}

      <div className="mb-4">
        <div className="flex gap-1 bg-gray-100 rounded-lg p-1">
          {timePeriods.map((period) => (
            <button
              key={period}
              onClick={() => onPeriodChange?.(period)}
              className={`flex-1 px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ${
                selectedPeriod === period
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              {period}
            </button>
          ))}
        </div>
      </div>

      {/* Modern Trend Chart */}
      <div className="bg-gradient-to-br from-gray-50 to-white rounded-xl p-4 border border-gray-100 relative">
        <svg width="100%" height={svgHeight} className="w-full h-auto" viewBox={`0 0 ${svgWidth} ${svgHeight}`}>
          <defs>
            <pattern id="modernGrid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f8fafc" strokeWidth="0.5"/>
            </pattern>

            <linearGradient id="improvingGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#10b981" stopOpacity="0.3"/>
              <stop offset="50%" stopColor="#10b981" stopOpacity="0.15"/>
              <stop offset="100%" stopColor="#10b981" stopOpacity="0.05"/>
            </linearGradient>

            <linearGradient id="decliningGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#ef4444" stopOpacity="0.3"/>
              <stop offset="50%" stopColor="#ef4444" stopOpacity="0.15"/>
              <stop offset="100%" stopColor="#ef4444" stopOpacity="0.05"/>
            </linearGradient>
          </defs>

          <rect width="100%" height="100%" fill="url(#modernGrid)" />

          {[0, 0.5, 1].map((ratio, index) => {
            const y = (svgHeight - 25) - (ratio * (svgHeight - topPadding - 25));
            return (
              <line
                key={`h-grid-${index}`}
                x1={padding}
                y1={y}
                x2={svgWidth - 25}
                y2={y}
                stroke="#f1f5f9"
                strokeWidth="1"
                opacity="0.5"
              />
            );
          })}

          <line
            x1={padding}
            y1={topPadding}
            x2={padding}
            y2={svgHeight - 25}
            stroke="#e2e8f0"
            strokeWidth="1"
          />

          <line
            x1={padding}
            y1={svgHeight - 25}
            x2={svgWidth - 25}
            y2={svgHeight - 25}
            stroke="#e2e8f0"
            strokeWidth="1"
          />

          {[0, 0.5, 1].map((ratio, index) => {
            const value = yAxisMin + (yAxisMax - yAxisMin) * ratio;
            const y = (svgHeight - 25) - (ratio * (svgHeight - topPadding - 25));
            return (
              <text
                key={index}
                x={padding - 8}
                y={y + 4}
                className="text-xs fill-gray-500 font-medium"
                textAnchor="end"
              >
                {value > 1000 ? `${(value/1000).toFixed(1)}k` : Math.round(value)}
              </text>
            );
          })}

          {/* Y-axis label above the Y-axis line */}
          <text
            x={padding}
            y={15}
            className="text-xs fill-gray-600 font-medium"
            textAnchor="start"
          >
            Count
          </text>

          {(() => {
            const points = trendValues.map((value: number, index: number) => {
              const x = padding + (index * pointSpacing);
              const y = range > 0
                ? (svgHeight - 25) - ((value - yAxisMin) / range) * (svgHeight - topPadding - 25)
                : (svgHeight - 25) / 2;
              return { x, y };
            });

            let pathData = `M ${points[0].x} ${points[0].y}`;

            for (let i = 1; i < points.length; i++) {
              const prevPoint = points[i - 1];
              const currentPoint = points[i];
              const nextPoint = points[i + 1];

              const cp1x = prevPoint.x + (currentPoint.x - prevPoint.x) * 0.3;
              const cp1y = prevPoint.y;
              const cp2x = currentPoint.x - (nextPoint ? (nextPoint.x - currentPoint.x) * 0.3 : 0);
              const cp2y = currentPoint.y;

              pathData += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${currentPoint.x} ${currentPoint.y}`;
            }

            const areaPathData = pathData + ` L ${points[points.length - 1].x} ${svgHeight - 25} L ${points[0].x} ${svgHeight - 25} Z`;

            return (
              <>
                <path
                  d={areaPathData}
                  fill={isImproving ? "url(#improvingGradient)" : "url(#decliningGradient)"}
                  stroke="none"
                />

                <path
                  d={pathData}
                  fill="none"
                  stroke={trendColor}
                  strokeWidth="1.2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  style={{
                    filter: 'drop-shadow(0 1px 3px rgba(0,0,0,0.1))'
                  }}
                />

                {points.map((point, index) => (
                  <circle
                    key={index}
                    cx={point.x}
                    cy={point.y}
                    r="1"
                    fill={trendColor}
                    opacity="0.6"
                  />
                ))}

                {/* X-axis labels inside SVG */}
                {filteredTrendData.map((point, idx) => {
                  const x = padding + (idx * pointSpacing);
                  const date = new Date(point.date);
                  return (
                    <text
                      key={idx}
                      x={x}
                      y={svgHeight - 8}
                      textAnchor="middle"
                      className="text-[10px] fill-gray-400 font-medium"
                    >
                      {date.toLocaleString('default', { month: 'short' })}
                    </text>
                  );
                })}
              </>
            );
          })()}
        </svg>
      </div>

      {/* Last Updated Footer and Show Details Button */}
      <div className="mt-3 pt-2 border-t border-gray-100 flex justify-between items-center">
        {/* Last Updated Text */}
        <div className="text-xs text-gray-500">
          Last Updated: {new Date().toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
          })}
        </div>

        {/* Show Details Button */}
        <button
          onClick={onToggleDetails}
          className={`flex items-center space-x-1 text-xs transition-all duration-200 rounded-lg px-3 py-1.5 font-medium ${
            showDetails
              ? 'text-blue-700 bg-blue-100 hover:bg-blue-200 border border-blue-300'
              : 'text-slate-500 hover:text-slate-700 bg-slate-50 hover:bg-slate-100 border border-transparent'
          }`}
        >
          <span className="font-medium">
            {showDetails ? 'Hide Details' : 'Show Details'}
          </span>
          {showDetails ? (
            <ChevronUp className="w-3 h-3" />
          ) : (
            <ChevronDown className="w-3 h-3" />
          )}
        </button>
      </div>
    </div>
  );
};

export default MetricCard;
