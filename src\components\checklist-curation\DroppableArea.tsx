
import React, { useState } from "react";
import { ContentComponent, DroppedItem } from "@/types/draggable";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { v4 as uuidv4 } from "uuid";
import ComponentRenderer from "./ComponentRenderer";
import AddComponentDialog from "./AddComponentDialog";
import MobilePreview from "./MobilePreview";

interface DroppableAreaProps {
  items: DroppedItem[];
  onAddItem: (item: DroppedItem) => void;
  onRemoveItem: (id: string) => void;
  onUpdateItem: (id: string, data: ContentComponent) => void;
  onReorderItems?: (sourceIndex: number, targetIndex: number) => void;
  invalidComponentIds?: string[];
}

const DroppableArea: React.FC<DroppableAreaProps> = ({
  items,
  onAddItem,
  onRemoveItem,
  onUpdateItem,
  onReorderItems,
  invalidComponentIds = [],
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const [isComponentDragging, setIsComponentDragging] = useState(false);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();

    // Show drag over state for any drag operation
    if (!isDragOver) setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const calculateDropIndex = (e: React.DragEvent): number => {
    const dropY = e.clientY;
    const container = e.currentTarget as HTMLElement;
    const componentElements = container.querySelectorAll('[data-component-index]');

    for (let i = 0; i < componentElements.length; i++) {
      const element = componentElements[i] as HTMLElement;
      const rect = element.getBoundingClientRect();
      const midPoint = rect.top + rect.height / 2;

      if (dropY < midPoint) {
        return i;
      }
    }

    return componentElements.length;
  };

  const handleDrop = (e: React.DragEvent) => {
    // Check if the drop occurred on a DropZone
    const target = e.target as HTMLElement;
    const isDropZone = target.closest('[data-drop-zone]');

    if (isDropZone) {
      // Let the DropZone handle this
      console.log("Drop occurred on DropZone, letting DropZone handle it");
      return;
    }

    e.preventDefault();
    setIsDragOver(false);

    // Check if it's a new component from sidebar
    const componentType = e.dataTransfer.getData("componentType");

    // Check if it's an existing component being reordered
    const componentId = e.dataTransfer.getData("componentId");

    console.log("Main area drop - component type:", componentType, "componentId:", componentId); // Debug log

    if (componentType) {
      // Handle new component from sidebar
      handleAddComponent(componentType);

      // Show success feedback
      toast({
        title: "Component Added",
        description: `${componentType} component has been added to your checklist.`,
      });
    } else if (componentId) {
      // Handle component reordering by calculating drop position
      const sourceIndex = items.findIndex(item => item.id === componentId);
      if (sourceIndex !== -1 && onReorderItems) {
        const targetIndex = calculateDropIndex(e);
        const adjustedTargetIndex = targetIndex > sourceIndex ? targetIndex - 1 : targetIndex;

        console.log("Main area reordering:", { sourceIndex, targetIndex, adjustedTargetIndex });

        if (sourceIndex !== adjustedTargetIndex) {
          onReorderItems(sourceIndex, adjustedTargetIndex);
          toast({
            title: "Component Moved",
            description: `Component moved from position ${sourceIndex + 1} to ${adjustedTargetIndex + 1}.`,
          });
        } else {
          console.log("No reordering needed - same position");
        }
      }
      console.log("Component reordering handled by main area");
      return;
    } else {
      console.log("No component type or ID found in drag data"); // Debug log
      toast({
        title: "Drop Failed",
        description: "Could not identify the component type. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleAddComponent = (componentType: string) => {
    const newItemId = uuidv4();
    const position = items.length;

    // Create appropriate initial structure based on component type
    let newComponent: ContentComponent;

    switch (componentType) {
      default:
        // Generic component creation for other types
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          required: ["sign", "checkpoint", "checkpoint-group", "date", "text-input"].includes(componentType),
        } as ContentComponent;
    }

    const newItem: DroppedItem = {
      id: newItemId,
      type: componentType,
      data: newComponent,
    };

    onAddItem(newItem);
  };

  const handleSave = () => {
    // In a real application, this would save to a database or API
    // For now, we'll just show a success message
    toast({
      title: "Content Saved",
      description: `${items.length} components saved successfully.`,
      variant: "default",
    });
  };

  const handlePreview = () => {
    setPreviewOpen(true);
  };

  const handleDropBetween = (index: number, componentType: string, componentData?: Partial<ContentComponent>) => {
    const newComponent: ContentComponent = {
      type: componentType,
      position: index,
      ...getDefaultComponentData(componentType),
      ...componentData
    } as ContentComponent;

    const newItem: DroppedItem = {
      id: uuidv4(),
      type: componentType,
      data: newComponent,
    };

    // Insert item at specific position
    const newItems = [...items];
    newItems.splice(index, 0, newItem);

    // Update positions for all items
    const updatedItems = newItems.map((item, idx) => ({
      ...item,
      data: { ...item.data, position: idx }
    }));

    // Clear the items and add all updated items
    items.forEach(item => onRemoveItem(item.id));
    updatedItems.forEach(item => onAddItem(item));

    setDragOverIndex(null);

    toast({
      title: "Component Added",
      description: `${componentType} component has been added.`,
    });
  };

  const getDefaultComponentData = (type: string): Partial<ContentComponent> => {
    switch (type) {
      case 'header':
        return { text: 'New Header', level: 1 };
      case 'section-header':
        return { text: 'New Section', level: 2 };
      case 'text-body':
        return { content: 'Enter your text content here...' };
      default:
        return {};
    }
  };

  return (
    <div className="flex-1 bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 h-full overflow-y-auto">
      {/* Canvas Area - Scrollable content */}
      <div
        className={`min-h-full p-6 transition-all duration-300 ${
          isDragOver ? "bg-blue-50 dark:bg-blue-900/20" : ""
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {items.length === 0 ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center shadow-lg">
                <Plus className="h-12 w-12 text-blue-500 dark:text-blue-400" />
              </div>
              <h3 className="text-2xl font-bold mb-3 bg-gradient-to-r from-slate-700 to-slate-900 dark:from-slate-200 dark:to-slate-400 bg-clip-text text-transparent">
                Start Building Your Checklist
              </h3>
              <p className="text-slate-600 dark:text-slate-400 mb-4">
                Drag components from the sidebar to create your custom checklist
              </p>
              <div className="flex gap-3 items-center justify-center">
                <Button
                  variant="outline"
                  className="px-6 py-3 h-auto hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200"
                  onClick={() => setDialogOpen(true)}
                >
                  <Plus className="h-5 w-5 mr-2" />
                  Browse Components
                </Button>
                {/* <Button
                  className="px-6 py-3 h-auto bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                  onClick={() => toast({
                    title: "💡 Tip",
                    description: "Drag components from the sidebar or click 'Browse Components' to get started!"
                  })}
                >
                  <Plus className="h-5 w-5 mr-2" />
                  Get Started
                </Button> */}
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-2 max-w-4xl mx-auto">
            {/* Drop zone at the beginning */}
            <DropZone
              index={0}
              onDrop={handleDropBetween}
              isDragOver={dragOverIndex === 0}
              items={items}
              onReorderItems={onReorderItems}
              setDragOverIndex={setDragOverIndex}
              toast={toast}
            />

            {items.map((item, index) => {
              const isInvalid = invalidComponentIds.includes(item.id);
              return (
                <div key={item.id}>
                  <div
                    className={`transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg ${
                      isInvalid
                        ? "ring-2 ring-red-500 ring-offset-2 bg-red-50 dark:bg-red-900/20 rounded-lg"
                        : ""
                    }`}
                    style={{ animationDelay: `${index * 100}ms` }}
                    data-component-index={index}
                    data-component-id={item.id}
                  >
                    {isInvalid && (
                      <div className="absolute -top-2 -right-2 z-10">
                        <div className="bg-red-500 text-white text-xs px-2 py-1 rounded-full shadow-lg animate-pulse">
                          ⚠️ Fix Required
                        </div>
                      </div>
                    )}
                    <ComponentRenderer
                      item={item}
                      index={index}
                      onRemove={onRemoveItem}
                      onUpdate={onUpdateItem}
                      onReorder={onReorderItems}
                      isDraggable={true}
                      isInvalid={isInvalid}
                    />
                  </div>

                  {/* Drop zone after each component */}
                  <DropZone
                    index={index + 1}
                    onDrop={handleDropBetween}
                    isDragOver={dragOverIndex === index + 1}
                    items={items}
                    onReorderItems={onReorderItems}
                    setDragOverIndex={setDragOverIndex}
                    toast={toast}
                  />
                </div>
              );
            })}

            {/* Add Component Button */}
            <Button
              variant="outline"
              className="w-full mt-8 border-dashed border-2 py-8 h-auto hover:border-blue-400 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 transition-all duration-300 transform hover:scale-[1.02] shadow-md hover:shadow-lg"
              onClick={() => setDialogOpen(true)}
            >
              <Plus className="h-6 w-6 mr-3 text-blue-500" />
              <span className="text-lg font-medium">Add Another Component</span>
            </Button>

            {/* Extra padding at bottom for better scrolling */}
            <div className="h-32"></div>
          </div>
        )}
      </div>

      {/* Add Component Dialog */}
      <AddComponentDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onSelectComponent={handleAddComponent}
      />

      {/* Mobile Preview */}
      <MobilePreview
        open={previewOpen}
        onOpenChange={setPreviewOpen}
        items={items}
      />

      {/* Footer Info */}
      {items.length > 0 && (
        <div className="mx-6 mb-4 text-center text-sm text-slate-600 dark:text-slate-400 bg-gradient-to-r from-slate-100 via-white to-slate-100 dark:from-slate-800 dark:via-slate-750 dark:to-slate-800 p-4 rounded-xl shadow-md border border-slate-200 dark:border-slate-700">
          <div className="flex items-center justify-center gap-2 mb-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <p className="font-medium">Workspace Active</p>
          </div>
          <p className="mb-1">Switch modes in the sidebar to access different component types.</p>
          {items.length > 1 && (
            <p className="mt-2 font-medium text-blue-600 dark:text-blue-400">
              💡 Drag and drop components to reorder them in the workspace.
            </p>
          )}
        </div>
      )}
    </div>
  );
};

// DropZone component for inserting components between existing ones
interface DropZoneProps {
  index: number;
  onDrop: (index: number, componentType: string, componentData?: Partial<ContentComponent>) => void;
  isDragOver: boolean;
  items: DroppedItem[];
  onReorderItems?: (sourceIndex: number, targetIndex: number) => void;
  setDragOverIndex: (index: number | null) => void;
  toast: typeof toast;
}

const DropZone: React.FC<DropZoneProps> = ({ index, onDrop, isDragOver, items, onReorderItems, setDragOverIndex, toast }) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
    setIsHovered(true);
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setIsHovered(false);
    setDragOverIndex(null);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsHovered(false);
    setDragOverIndex(null);

    console.log("DropZone handleDrop called at index:", index);

    // Check if it's a component from sidebar
    const componentType = e.dataTransfer.getData("componentType");
    if (componentType) {
      console.log("DropZone: Adding new component", componentType, "at index", index);
      onDrop(index, componentType);
      return;
    }

    // Check if it's a component being reordered
    const componentId = e.dataTransfer.getData("componentId");
    if (componentId) {
      console.log("DropZone: Reordering component", componentId, "to index", index);
      // Handle reordering by finding the source component and moving it
      const sourceIndex = items.findIndex(item => item.id === componentId);
      if (sourceIndex !== -1 && onReorderItems) {
        // Calculate target index (before the current position)
        const targetIndex = index > sourceIndex ? index - 1 : index;
        console.log("DropZone: Moving from", sourceIndex, "to", targetIndex);
        if (sourceIndex !== targetIndex) {
          onReorderItems(sourceIndex, targetIndex);
          // Add toast notification for DropZone reordering
          toast({
            title: "Component Reordered",
            description: `Component moved from position ${sourceIndex + 1} to ${targetIndex + 1}.`,
          });
        }
      }
      return;
    }
  };

  return (
    <div
      className={`min-h-[8px] transition-all duration-200 ${
        isDragOver || isHovered
          ? "h-12 bg-blue-100 dark:bg-blue-900/30 border-2 border-dashed border-blue-400 rounded-md"
          : "h-2 bg-transparent hover:bg-slate-100 dark:hover:bg-slate-800 hover:h-4"
      }`}
      data-drop-zone="true"
      data-drop-index={index}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {(isDragOver || isHovered) && (
        <div className="flex items-center justify-center h-full">
          <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
            Drop component here to reorder
          </span>
        </div>
      )}
    </div>
  );
};

export default DroppableArea;
