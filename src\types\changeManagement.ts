// Change Management Data Types

export interface ChangeRequest {
  id: string;
  maskId: string;
  title: string;
  description: string;
  changeType: 'Operational' | 'Technical' | 'Organizational' | 'Emergency';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  status: 'Draft' | 'Submitted' | 'Under Review' | 'Approved' | 'Rejected' | 'Implemented' | 'Closed';
  requestedBy: {
    id: string;
    firstName: string;
    lastName?: string;
    email: string;
    department?: string;
  };
  requestDate: string;
  proposedImplementationDate: string;
  actualImplementationDate?: string;
  businessJustification: string;
  impactDescription: string;
  rollbackPlan: string;
  affectedSystems: string[];
  affectedDepartments: string[];
  estimatedCost?: number;
  estimatedDuration?: number; // in hours
  attachments?: string[];
  created: string;
  updated: string;
}

export interface ImpactAssessment {
  id: string;
  maskId: string;
  changeRequestId: string;
  assessorId: string;
  assessor: {
    id: string;
    firstName: string;
    lastName?: string;
    email: string;
    department?: string;
  };
  assessmentDate: string;
  riskLevel: 'Low' | 'Medium' | 'High' | 'Critical';
  businessImpact: 'Minimal' | 'Low' | 'Medium' | 'High' | 'Critical';
  technicalImpact: 'Minimal' | 'Low' | 'Medium' | 'High' | 'Critical';
  operationalImpact: 'Minimal' | 'Low' | 'Medium' | 'High' | 'Critical';
  safetyImpact: 'None' | 'Low' | 'Medium' | 'High' | 'Critical';
  environmentalImpact: 'None' | 'Low' | 'Medium' | 'High' | 'Critical';
  complianceImpact: 'None' | 'Low' | 'Medium' | 'High' | 'Critical';
  riskMitigationMeasures: string;
  recommendations: string;
  additionalRequirements: string;
  approvalRequired: boolean;
  stakeholdersToNotify: string[];
  estimatedDowntime?: number; // in hours
  resourceRequirements: string;
  testingRequirements: string;
  communicationPlan: string;
  status: 'Draft' | 'Completed' | 'Reviewed' | 'Approved';
  created: string;
  updated: string;
}

export interface ApprovalWorkflow {
  id: string;
  maskId: string;
  changeRequestId: string;
  workflowName: string;
  currentStage: number;
  totalStages: number;
  status: 'Pending' | 'In Progress' | 'Approved' | 'Rejected' | 'Cancelled';
  created: string;
  updated: string;
}

export interface ApprovalStage {
  id: string;
  workflowId: string;
  stageNumber: number;
  stageName: string;
  approverType: 'Individual' | 'Group' | 'Role';
  requiredApprovers: ApprovalStakeholder[];
  approvalType: 'Any' | 'All' | 'Majority';
  status: 'Pending' | 'In Progress' | 'Approved' | 'Rejected' | 'Skipped';
  dueDate?: string;
  completedDate?: string;
  comments?: string;
  created: string;
  updated: string;
}

export interface ApprovalStakeholder {
  id: string;
  stageId: string;
  stakeholderType: 'Safety' | 'Legal' | 'IT' | 'Operations' | 'Management' | 'Quality' | 'Environmental' | 'HR';
  userId?: string;
  roleId?: string;
  groupId?: string;
  user?: {
    id: string;
    firstName: string;
    lastName?: string;
    email: string;
    department?: string;
  };
  status: 'Pending' | 'Approved' | 'Rejected' | 'Delegated';
  approvalDate?: string;
  comments?: string;
  created: string;
  updated: string;
}

export interface TrainingRollout {
  id: string;
  maskId: string;
  changeRequestId: string;
  trainingTitle: string;
  trainingDescription: string;
  trainingType: 'Online' | 'Classroom' | 'On-the-job' | 'Workshop' | 'Webinar';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  status: 'Planned' | 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled';
  targetAudience: string[];
  affectedTeams: string[];
  trainingMaterials: string[];
  instructorId?: string;
  instructor?: {
    id: string;
    firstName: string;
    lastName?: string;
    email: string;
  };
  scheduledStartDate?: string;
  scheduledEndDate?: string;
  actualStartDate?: string;
  actualEndDate?: string;
  duration: number; // in hours
  maxParticipants?: number;
  location?: string;
  isVirtual: boolean;
  meetingLink?: string;
  prerequisites: string[];
  learningObjectives: string[];
  assessmentRequired: boolean;
  passScore?: number;
  created: string;
  updated: string;
}

export interface TrainingAssignment {
  id: string;
  trainingRolloutId: string;
  assigneeId: string;
  assignee: {
    id: string;
    firstName: string;
    lastName?: string;
    email: string;
    department?: string;
  };
  assignedDate: string;
  dueDate: string;
  status: 'Assigned' | 'In Progress' | 'Completed' | 'Overdue' | 'Exempted';
  completionDate?: string;
  score?: number;
  attempts: number;
  maxAttempts: number;
  feedback?: string;
  certificateIssued: boolean;
  certificateUrl?: string;
  created: string;
  updated: string;
}

export interface ChangeManagementDashboard {
  totalChanges: number;
  pendingApprovals: number;
  activeTrainings: number;
  completedChanges: number;
  changesByStatus: {
    status: string;
    count: number;
  }[];
  changesByType: {
    type: string;
    count: number;
  }[];
  riskDistribution: {
    riskLevel: string;
    count: number;
  }[];
  upcomingDeadlines: {
    id: string;
    title: string;
    type: 'Change' | 'Approval' | 'Training';
    dueDate: string;
  }[];
}

// API Response types
export interface ChangeRequestResponse extends ChangeRequest {}
export interface ImpactAssessmentResponse extends ImpactAssessment {}
export interface ApprovalWorkflowResponse extends ApprovalWorkflow {
  stages: ApprovalStage[];
}
export interface TrainingRolloutResponse extends TrainingRollout {
  assignments: TrainingAssignment[];
}

// Form data types
export interface CreateChangeRequestData {
  title: string;
  description: string;
  changeType: ChangeRequest['changeType'];
  priority: ChangeRequest['priority'];
  proposedImplementationDate: string;
  businessJustification: string;
  impactDescription: string;
  rollbackPlan: string;
  affectedSystems: string[];
  affectedDepartments: string[];
  estimatedCost?: number;
  estimatedDuration?: number;
  attachments?: File[];
}

export interface CreateImpactAssessmentData {
  changeRequestId: string;
  riskLevel: ImpactAssessment['riskLevel'];
  businessImpact: ImpactAssessment['businessImpact'];
  technicalImpact: ImpactAssessment['technicalImpact'];
  operationalImpact: ImpactAssessment['operationalImpact'];
  safetyImpact: ImpactAssessment['safetyImpact'];
  environmentalImpact: ImpactAssessment['environmentalImpact'];
  complianceImpact: ImpactAssessment['complianceImpact'];
  riskMitigationMeasures: string;
  recommendations: string;
  additionalRequirements: string;
  stakeholdersToNotify: string[];
  estimatedDowntime?: number;
  resourceRequirements: string;
  testingRequirements: string;
  communicationPlan: string;
}

export interface CreateTrainingRolloutData {
  changeRequestId: string;
  trainingTitle: string;
  trainingDescription: string;
  trainingType: TrainingRollout['trainingType'];
  priority: TrainingRollout['priority'];
  targetAudience: string[];
  affectedTeams: string[];
  instructorId?: string;
  scheduledStartDate?: string;
  scheduledEndDate?: string;
  duration: number;
  maxParticipants?: number;
  location?: string;
  isVirtual: boolean;
  meetingLink?: string;
  prerequisites: string[];
  learningObjectives: string[];
  assessmentRequired: boolean;
  passScore?: number;
  trainingMaterials?: File[];
}
