import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import PageHeader from '@/components/common/PageHeader';
import TabsContainer from '@/components/common/TabsContainer';
import ExpandableDataTable from '@/components/common/ExpandableDataTable';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  FileText,
  Plus,
  Search,
  Download,
  Upload,
  File,
  Image,
  Video,
  Loader2,
  Calendar as CalendarIcon,
  Trash2,
  Users,
  FileCheck,
  X,
  Edit
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format, differenceInDays } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import apiService from '@/services/apiService';
import { API_BASE_URL } from '@/constants/index';
import { ASSIGNED_ACTION_URL } from '@/services/api';
import DocumentDetailsView from '@/components/DocumentDetailsView';
import ImageComponent from '@/components/common/ImageComponent';
import { MultiSelect } from '@/components/ui/multi-select';

// API Configuration
const ADMINDROPDOWNS = `${API_BASE_URL}/dropdowns`;
const GET_USER_ROLE_BY_MODE = `${API_BASE_URL}/users/get_users`;
const DOCUMENTS_API = `${API_BASE_URL}/documents`;
const MY_DOCUMENTS_API = `${API_BASE_URL}/my-documents`;

// Function to calculate timeline status
const getTimelineStatus = (dueDate: string | undefined): string => {
  if (!dueDate) return 'No Due Date';

  try {
    const due = new Date(dueDate);
    const today = new Date();

    // Reset time to compare only dates
    today.setHours(0, 0, 0, 0);
    due.setHours(0, 0, 0, 0);

    const diffInDays = differenceInDays(due, today);

    if (diffInDays < 0) {
      return 'Overdue';
    } else if (diffInDays === 0) {
      return 'Due Now';
    } else {
      return 'Upcoming';
    }
  } catch (error) {
    console.error('Error calculating timeline status:', error);
    return 'Invalid Date';
  }
};

interface MyAction {
  id: string;
  maskId: string;
  actionType: string;
  description: string;
  submittedBy: string;
  dueDate: string;
  status: string;
  timeline?: string;
  applicationId?: string;
  comments?: string;
}

interface DocumentComponent {
  id: string;
  type: string;
  content: any;
  position: number;
}

interface Document {
  id: string;
  name: string;
  type: string;
  size: string;
  uploadedBy: string;
  uploadedDate: string;
  category: string;
  tags: string[];
  description?: string;
  maskId?: string;
  scopeApplicability?: string;
  purpose?: string;
  keywords?: string;
  docId?: string;
  created?: string;
  updated?: string;
  creatorTargetDate?: string;
  reviewerTargetDate?: string;
  approverTargetDate?: string;
  initiatorId?: string;
  creatorId?: string;
  reviewerId?: string;
  approverId?: string;
  documentCategoryId?: string;
  initiator?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  creator?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  reviewer?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  approver?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  documentCategory?: {
    id: string;
    name: string;
    level?: number;
  };
  files?: string;
  status?: string;
  value?: DocumentComponent[];
}

interface Assignment {
  id: string;
  maskId: string;
  title: string;
  assignedTo: string;
  assignedBy: string;
  assignedDate: string;
  dueDate: string;
  status: string;
  priority: string;
}

interface DocumentInitiation {
  type: string;
  documentCategoryId: string;
  docId: string;
  name: string;
  scopeApplicability: string;
  purpose: string;
  creatorId: string;
  creatorTargetDate: Date | undefined;
  reviewerId: string;
  reviewerTargetDate: Date | undefined;
  approverId: string;
  approverTargetDate: Date | undefined;
  keywords: string;
  version: string;
}

interface ExistingDocumentUpload {
  type: string;
  documentCategoryId: string;
  docId: string;
  name: string;
  scopeApplicability: string;
  purpose: string;
  keywords: string;
  file: File | null;
  version: string;
}

interface UserOption {
  label: string;
  value: string;
}

const DocumentPage = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('myaction');

  // Search states for each tab
  const [myActionsSearch, setMyActionsSearch] = useState('');
  const [documentsSearch, setDocumentsSearch] = useState('');
  const [assignmentsSearch, setAssignmentsSearch] = useState('');

  // Data states
  const [myActions, setMyActions] = useState<MyAction[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [myDocuments, setMyDocuments] = useState<Document[]>([]);
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [categorizedDocuments, setCategorizedDocuments] = useState<{[key: string]: Document[]}>({});

  // Loading states
  const [loadingActions, setLoadingActions] = useState(false);
  const [loadingDocuments, setLoadingDocuments] = useState(false);
  const [loadingAssignments, setLoadingAssignments] = useState(false);

  // Counts
  const [actionCount, setActionCount] = useState(0);
  const [documentCount, setDocumentCount] = useState(0);
  const [assignmentCount, setAssignmentCount] = useState(0);

  // Document Initiation Modal state
  const [isInitiateModalOpen, setIsInitiateModalOpen] = useState(false);
  const [documentForm, setDocumentForm] = useState<DocumentInitiation>({
    type: 'New',
    documentCategoryId: '',
    docId: '',
    name: '',
    scopeApplicability: '',
    purpose: '',
    creatorId: '',
    creatorTargetDate: undefined,
    reviewerId: '',
    reviewerTargetDate: undefined,
    approverId: '',
    approverTargetDate: undefined,
    keywords: '',
    version: '1.0'
  });

  // Existing Document Upload Modal state
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);

  // Document Details Modal state
  const [isDocumentDetailsOpen, setIsDocumentDetailsOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);

  // Document Review Modal state (for perform_task actions)
  const [isDocumentReviewOpen, setIsDocumentReviewOpen] = useState(false);
  const [selectedActionForReview, setSelectedActionForReview] = useState<MyAction | null>(null);
  const [reviewComments, setReviewComments] = useState('');
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | null>(null);
  const [reviewDocumentDetails, setReviewDocumentDetails] = useState<Document | null>(null);
  const [loadingReviewDocument, setLoadingReviewDocument] = useState(false);

  // Assignment Modal state
  const [isAssignmentModalOpen, setIsAssignmentModalOpen] = useState(false);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [allUsers, setAllUsers] = useState<UserOption[]>([]);
  const [loadingAllUsers, setLoadingAllUsers] = useState(false);
  const [assignmentDocumentSearch, setAssignmentDocumentSearch] = useState('');
  const [submittingAssignment, setSubmittingAssignment] = useState(false);

  // User assignments states
  const [userAssignments, setUserAssignments] = useState<any[]>([]);
  const [loadingUserAssignments, setLoadingUserAssignments] = useState(false);
  const [userAssignmentsSearch, setUserAssignmentsSearch] = useState('');

  // User documents modal states
  const [isUserDocumentsModalOpen, setIsUserDocumentsModalOpen] = useState(false);
  const [selectedUserDocuments, setSelectedUserDocuments] = useState<any[]>([]);
  const [selectedUserInfo, setSelectedUserInfo] = useState<any>(null);
  const [loadingUserDocuments, setLoadingUserDocuments] = useState(false);

  // Assign document modal states
  const [isAssignDocumentModalOpen, setIsAssignDocumentModalOpen] = useState(false);
  const [allDocuments, setAllDocuments] = useState<any[]>([]);
  const [loadingAllDocuments, setLoadingAllDocuments] = useState(false);
  const [assigningDocument, setAssigningDocument] = useState(false);

  // Note: Action details are now handled by navigation to DocumentCurationPage
  const [uploadForm, setUploadForm] = useState<ExistingDocumentUpload>({
    type: 'Existing',
    documentCategoryId: '',
    docId: '',
    name: '',
    scopeApplicability: '',
    purpose: '',
    keywords: '',
    file: null,
    version: '1.0'
  });

  // User options for dropdowns
  const [creatorOptions, setCreatorOptions] = useState<UserOption[]>([]);
  const [reviewerOptions, setReviewerOptions] = useState<UserOption[]>([]);
  const [approverOptions, setApproverOptions] = useState<UserOption[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);

  // Category options from API
  const [categoryOptions, setCategoryOptions] = useState<UserOption[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);

  // Fetch categories from API
  const fetchCategories = useCallback(async () => {
    try {
      setLoadingCategories(true);
      const maskId = 'doc_category';
      const uriString = {
        where: { maskId },
        include: [{ relation: "dropdownItems" }],
      };
      const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await apiService.get(url);
      const data = response[0]?.dropdownItems.map((item: { name: string; id: string }) => ({
        label: item.name,
        value: item.id,
      })) || [];

      setCategoryOptions(data);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast({
        title: "Error",
        description: "Failed to load categories. Please try again.",
        variant: "destructive"
      });
      // Fallback to default categories
      setCategoryOptions([
        { label: 'Safety', value: 'safety' },
        { label: 'Training', value: 'training' },
        { label: 'Inspection', value: 'inspection' },
        { label: 'Plans', value: 'plans' },
        { label: 'Procedures', value: 'procedures' },
        { label: 'Policies', value: 'policies' },
        { label: 'Guidelines', value: 'guidelines' }
      ]);
    } finally {
      setLoadingCategories(false);
    }
  }, [toast]);

  // Fetch users by role mode
  const fetchUsersByMode = useCallback(async (mode: string) => {
    try {
      const response = await apiService.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: '',
        locationTwoId: '',
        locationThreeId: '',
        locationFourId: '',
        mode: mode,
      });

      return response.map((item: any) => ({
        label: item.firstName,
        value: item.id
      }));
    } catch (error) {
      console.error(`Error fetching users for mode ${mode}:`, error);
      toast({
        title: "Error",
        description: `Failed to fetch users for ${mode}. Please try again.`,
        variant: "destructive"
      });
      return [];
    }
  }, [toast]);

  // Fetch all document-related users
  const fetchDocumentUsers = useCallback(async () => {
    try {
      setLoadingUsers(true);

      const [creators, reviewers, approvers] = await Promise.all([
        fetchUsersByMode('doc_creator'),
        fetchUsersByMode('doc_reviewer'),
        fetchUsersByMode('doc_approver')
      ]);

      setCreatorOptions(creators);
      setReviewerOptions(reviewers);
      setApproverOptions(approvers);
    } catch (error) {
      console.error('Error fetching document users:', error);
    } finally {
      setLoadingUsers(false);
    }
  }, [fetchUsersByMode]);

  // Fetch all users for assignment
  const fetchAllUsers = useCallback(async () => {
    try {
      setLoadingAllUsers(true);

      // Fetch all users from /users API endpoint
      const response = await apiService.get('/users');

      // Transform API response to match UserOption interface
      const transformedUsers = response.map((user: any) => ({
        label: user.firstName || user.name || user.email || 'Unknown User',
        value: user.id
      }));

      setAllUsers(transformedUsers);
    } catch (error) {
      console.error('Error fetching all users:', error);
      toast({
        title: "Error",
        description: "Failed to fetch users for assignment. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingAllUsers(false);
    }
  }, [toast]);

  // Fetch user assignments for the assignment table
  const fetchUserAssignments = useCallback(async () => {
    try {
      setLoadingUserAssignments(true);

      // Fetch all users from /users API endpoint
      const response = await apiService.get('/users-document-allocation-summary');

      // Transform API response to include user details for assignment table
      const transformedUsers = response.map((user: any) => ({
        id: user.id,
        firstName: user.firstName || 'N/A',
        // lastName: user.lastName || 'N/A',
        email: user.email || 'N/A',
        // role: user.role || 'N/A',
        // department: user.department || 'N/A',
        status: user.status || 'Active',
        assignedDocuments: user.documentsAllocated || 0,
        lastLogin: user.lastLogin || 'N/A'
      }));

      setUserAssignments(transformedUsers);
    } catch (error) {
      console.error('Error fetching user assignments:', error);
      toast({
        title: "Error",
        description: "Failed to fetch user assignments. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingUserAssignments(false);
    }
  }, [toast]);

  // Fetch user documents for specific user
  const fetchUserDocuments = useCallback(async (userId: string, userInfo: any) => {
    try {
      setLoadingUserDocuments(true);
      const response = await apiService.get(`/users/${userId}/documents`);

      console.log('User documents response:', response);

      // Set the documents and user info, then open modal
      setSelectedUserDocuments(response || []);
      setSelectedUserInfo(userInfo);
      setIsUserDocumentsModalOpen(true);

      toast({
        title: "User Documents",
        description: `Found ${response.length || 0} documents for ${userInfo.firstName} ${userInfo.lastName}`,
        variant: "default"
      });

      return response;
    } catch (error) {
      console.error('Error fetching user documents:', error);
      toast({
        title: "Error",
        description: "Failed to fetch user documents. Please try again.",
        variant: "destructive"
      });
      return [];
    } finally {
      setLoadingUserDocuments(false);
    }
  }, [toast]);

  // Handle unassign document from user
  const handleUnassignDocument = useCallback(async (documentId: string, documentName: string) => {
    try {
      // Show confirmation dialog
      const confirmed = window.confirm(
        `Are you sure you want to unassign "${documentName}" from ${selectedUserInfo?.firstName} ${selectedUserInfo?.lastName}?`
      );

      if (!confirmed) return;

      // Prepare payload for unallocate API
      const payload = {
        userIds: [selectedUserInfo?.id],
        documentIds: [documentId]
      };

      // Call API to unassign document using the correct endpoint
      await apiService.post('/documents/unallocate-users', payload);

      // Remove document from the current list
      setSelectedUserDocuments(prev => prev.filter(doc => doc.id !== documentId));

      toast({
        title: "Document Unassigned",
        description: `"${documentName}" has been unassigned from ${selectedUserInfo?.firstName} ${selectedUserInfo?.lastName}`,
        variant: "default"
      });

    } catch (error) {
      console.error('Error unassigning document:', error);
      toast({
        title: "Error",
        description: "Failed to unassign document. Please try again.",
        variant: "destructive"
      });
    }
  }, [selectedUserInfo, toast]);

  // Fetch all documents for assignment (approved documents only)
  const fetchAllDocuments = useCallback(async () => {
    try {
      setLoadingAllDocuments(true);
      const response = await apiService.get('/documents');

      console.log('All documents response:', response);

      // Filter to show only approved documents
      const approvedDocuments = (response || []).filter((doc: any) => {
        const status = doc.status || doc.docStatus;
        return status && String(status).toLowerCase() === 'approved';
      });

      console.log('Approved documents for assignment:', approvedDocuments);
      setAllDocuments(approvedDocuments);

    } catch (error) {
      console.error('Error fetching all documents:', error);
      toast({
        title: "Error",
        description: "Failed to fetch documents. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingAllDocuments(false);
    }
  }, [toast]);

  // Fetch ALL documents for the All Documents tab (no filtering)
  const fetchAllDocumentsForTab = useCallback(async () => {
    try {
      setLoadingDocuments(true);
      console.log('🔍 Fetching ALL documents from:', DOCUMENTS_API);
      console.log('🔍 API_BASE_URL:', API_BASE_URL);
      console.log('🔍 Full DOCUMENTS_API URL:', DOCUMENTS_API);

      // Try with include relations first
      const uriString = {
        include: [
          { relation: "creator" },
          { relation: "documentCategory" },
          { relation: "reviewer" },
          { relation: "approver" },
          { relation: "initiator" }
        ]
      };

      const url = `${DOCUMENTS_API}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      console.log('🔍 Full URL with relations:', url);

      const response = await apiService.get(url);

      console.log('📊 All documents for tab response:', response);
      console.log('📊 Response type:', typeof response);
      console.log('📊 Response is array:', Array.isArray(response));
      console.log('📊 Response length:', response?.length || 0);

      // Handle different response formats
      let documentsArray = response;
      if (!Array.isArray(response)) {
        console.log('⚠️ Response is not an array, checking for data property');
        if (response && response.data && Array.isArray(response.data)) {
          documentsArray = response.data;
          console.log('✅ Found data array:', documentsArray.length);
        } else {
          console.error('❌ Response is not in expected format:', response);
          documentsArray = [];
        }
      }

      // Transform API response to match our Document interface
      const transformedDocuments = documentsArray.map((doc: any) => ({
        id: doc.id,
        name: doc.name,
        type: doc.type,
        size: doc.size || 'Unknown',
        uploadedBy: doc.creator?.firstName || doc.uploadedBy || doc.createdBy || 'Not assigned',
        uploadedDate: doc.uploadedDate || doc.created || new Date().toISOString(),
        category: doc.documentCategory?.name || doc.category || 'Uncategorized',
        tags: doc.keywords ? doc.keywords.split(',').map((tag: string) => tag.trim()) : [],
        description: doc.purpose || doc.description || '',
        maskId: doc.maskId,
        scopeApplicability: doc.scopeApplicability,
        files: doc.files,
        purpose: doc.purpose,
        keywords: doc.keywords,
        docId: doc.docId,
        status: doc.status || doc.docStatus || 'Draft',
        created: doc.created || doc.uploadedDate,
        creator: doc.creator,
        reviewer: doc.reviewer,
        approver: doc.approver,
        initiator: doc.initiator,
        documentCategory: doc.documentCategory,
        documentCategoryId: doc.documentCategoryId,
        value: doc.value,
        version: doc.version
      }));

      console.log('✅ Transformed all documents:', transformedDocuments);
      console.log('📊 Setting documents count to:', transformedDocuments.length);
      setDocuments(transformedDocuments);
      setDocumentCount(transformedDocuments.length);

    } catch (error) {
      console.error('❌ Error fetching all documents for tab:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        response: (error as any)?.response?.data || 'No response data'
      });
      toast({
        title: "Error",
        description: "Failed to fetch documents. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingDocuments(false);
    }
  }, [toast]);

  // Handle assign document to user
  const handleAssignDocument = useCallback(async (documentId: string, documentName: string) => {
    try {
      setAssigningDocument(true);

      // Prepare payload for allocate API
      const payload = {
        userIds: [selectedUserInfo?.id],
        documentIds: [documentId]
      };

      // Call API to assign document
      await apiService.post('/documents/allocate-users', payload);

      // Refresh user documents to show the newly assigned document
      await fetchUserDocuments(selectedUserInfo?.id, selectedUserInfo);

      // Close assign modal
      setIsAssignDocumentModalOpen(false);

      toast({
        title: "Document Assigned",
        description: `"${documentName}" has been assigned to ${selectedUserInfo?.firstName} ${selectedUserInfo?.lastName}`,
        variant: "default"
      });

    } catch (error) {
      console.error('Error assigning document:', error);
      toast({
        title: "Error",
        description: "Failed to assign document. Please try again.",
        variant: "destructive"
      });
    } finally {
      setAssigningDocument(false);
    }
  }, [selectedUserInfo, toast, fetchUserDocuments]);

  // Open assign document modal
  const openAssignDocumentModal = useCallback(() => {
    setIsAssignDocumentModalOpen(true);
    fetchAllDocuments();
  }, [fetchAllDocuments]);

  // Fetch documents from API using my-documents endpoint
  const fetchDocuments = useCallback(async () => {
    try {
      setLoadingDocuments(true);

      const uriString = {
        include: [
          { relation: "creator" },
          { relation: "documentCategory" },
          { relation: "reviewer" },
          { relation: "approver" },
          { relation: "initiator" }
        ]
      };

      const url = `${MY_DOCUMENTS_API}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await apiService.get(url);

      console.log('My documents response:', response);

      // Transform API response to match our Document interface
      const transformedDocuments = response.map((doc: any) => ({
        id: doc.id,
        name: doc.name,
        type: doc.type,
        size: doc.size || 'Unknown',
        uploadedBy: doc.creator?.firstName || doc.uploadedBy || doc.createdBy || 'Not assigned',
        uploadedDate: doc.uploadedDate || doc.created || new Date().toISOString(),
        category: doc.documentCategory?.name || doc.category || 'Uncategorized',
        tags: doc.keywords ? doc.keywords.split(',').map((tag: string) => tag.trim()) : [],
        description: doc.purpose || doc.description || '',
        maskId: doc.maskId,
        scopeApplicability: doc.scopeApplicability,
        purpose: doc.purpose,
        keywords: doc.keywords,
        docId: doc.docId,
        created: doc.created,
        updated: doc.updated,
        creatorTargetDate: doc.creatorTargetDate,
        reviewerTargetDate: doc.reviewerTargetDate,
        approverTargetDate: doc.approverTargetDate,
        initiatorId: doc.initiatorId,
        creatorId: doc.creatorId,
        reviewerId: doc.reviewerId,
        approverId: doc.approverId,
        documentCategoryId: doc.documentCategoryId,
        initiator: doc.initiator,
        creator: doc.creator,
        reviewer: doc.reviewer,
        approver: doc.approver,
        documentCategory: doc.documentCategory,
        files: doc.files,
        status: doc.status || 'Draft',
        value: doc.value
      }));

      setMyDocuments(transformedDocuments);
      // setDocumentCount(transformedDocuments.length);

      // Categorize documents by documentCategoryId
      const categorized = transformedDocuments.reduce((acc: {[key: string]: Document[]}, doc: Document) => {
        const categoryId = doc.documentCategoryId || 'uncategorized';
        const categoryName = doc.documentCategory?.name || doc.category || 'Uncategorized';

        if (!acc[categoryId]) {
          acc[categoryId] = [];
        }
        acc[categoryId].push(doc);
        return acc;
      }, {});

      console.log('Categorized documents:', categorized);
      setCategorizedDocuments(categorized);
    } catch (error) {
      console.error('Error fetching documents:', error);
      toast({
        title: "Error",
        description: "Failed to load documents. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingDocuments(false);
    }
  }, [toast]);

  // Fetch MyActions from API
  const fetchMyActions = useCallback(async () => {
    try {
      setLoadingActions(true);

      const uriString = {
        include: [{ relation: "submittedBy" }]
      };
      const url = `${ASSIGNED_ACTION_URL('DOC')}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      const response = await apiService.get(url);

      // Transform API response to match our MyAction interface
      const transformedActions = response.map((action: any) => {
        // Debug log to see what fields are available
        if (action.actionType === 'verify_task') {
          console.log('verify_task action data:', action);
        }

        return {
          id: action.id,
          maskId: action.maskId,
          actionType: action.actionType,
          description: action.description || action.title || 'No description',
          submittedBy: action.submittedBy?.firstName || 'Not assigned',
          dueDate: action.dueDate || action.targetDate || '',
          status: action.status || 'Pending',
          timeline: action.timeline || action.createdAt || '',
          applicationId: action.applicationId,
          comments: action.comments || action.reviewerComments || action.reviewComments || ''
        };
      });

      setMyActions(transformedActions);
      setActionCount(transformedActions.length);
    } catch (error) {
      console.error('Error fetching my actions:', error);
      toast({
        title: "Error",
        description: "Failed to load actions. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingActions(false);
    }
  }, [toast]);

  // Helper function to determine file type from filename
  const getFileTypeFromName = (filename: string): 'pdf' | 'doc' | 'image' | 'video' | 'other' => {
    const extension = filename.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'doc';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return 'image';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return 'video';
      default:
        return 'other';
    }
  };

  // Load data on component mount
  useEffect(() => {
    // Fetch categories, users, documents, and actions on component mount
    fetchCategories();
    fetchDocumentUsers();
    fetchAllDocumentsForTab(); // Fetch ALL documents for "All Documents" tab
    fetchDocuments(); // Fetch user's documents for "My Documents" categorization
    fetchMyActions();
    fetchUserAssignments();

    // Mock Assignments Data (keeping for now)
    const mockAssignments: Assignment[] = [
      {
        id: '1',
        maskId: 'ASG-001',
        title: 'Monthly Safety Audit',
        assignedTo: 'John Smith',
        assignedBy: 'Manager',
        assignedDate: '2024-01-20',
        dueDate: '2024-02-20',
        status: 'Active',
        priority: 'High'
      },
      {
        id: '2',
        maskId: 'ASG-002',
        title: 'Equipment Maintenance Review',
        assignedTo: 'Sarah Johnson',
        assignedBy: 'Supervisor',
        assignedDate: '2024-01-18',
        dueDate: '2024-02-18',
        status: 'Pending',
        priority: 'Medium'
      }
    ];

    setAssignments(mockAssignments);
    setAssignmentCount(mockAssignments.length);
  }, [fetchCategories, fetchDocumentUsers, fetchAllDocumentsForTab, fetchDocuments, fetchMyActions, fetchUserAssignments]);

  // Filter functions
  const filteredMyActions = myActions.filter(action =>
    action.maskId.toLowerCase().includes(myActionsSearch.toLowerCase()) ||
    action.description.toLowerCase().includes(myActionsSearch.toLowerCase()) ||
    action.actionType.toLowerCase().includes(myActionsSearch.toLowerCase())
  );

  const filteredDocuments = documents.filter(doc =>
    doc.name.toLowerCase().includes(documentsSearch.toLowerCase()) ||
    doc.description?.toLowerCase().includes(documentsSearch.toLowerCase()) ||
    doc.maskId?.toLowerCase().includes(documentsSearch.toLowerCase()) ||
    doc.creator?.firstName?.toLowerCase().includes(documentsSearch.toLowerCase()) ||
    doc.reviewer?.firstName?.toLowerCase().includes(documentsSearch.toLowerCase()) ||
    doc.approver?.firstName?.toLowerCase().includes(documentsSearch.toLowerCase()) ||
    doc.documentCategory?.name?.toLowerCase().includes(documentsSearch.toLowerCase()) ||
    doc.tags.some(tag => tag.toLowerCase().includes(documentsSearch.toLowerCase()))
  );

  // Debug logging for filtered documents
  console.log('🔍 Active tab:', activeTab);
  console.log('🔍 Documents state:', documents);
  console.log('🔍 Filtered documents:', filteredDocuments);
  console.log('🔍 Document count:', documentCount);

  const filteredAssignments = assignments.filter(assignment =>
    assignment.maskId.toLowerCase().includes(assignmentsSearch.toLowerCase()) ||
    assignment.title.toLowerCase().includes(assignmentsSearch.toLowerCase()) ||
    assignment.assignedTo.toLowerCase().includes(assignmentsSearch.toLowerCase())
  );

  // Filter documents for assignment based on search and status (only show Approved documents)
  const filteredAssignmentDocuments = documents.filter(doc => {
    // First filter: only show Approved documents
    const isApproved = doc.status?.toLowerCase() === 'approved';

    // Second filter: search functionality
    const matchesSearch = assignmentDocumentSearch === '' ||
      doc.name.toLowerCase().includes(assignmentDocumentSearch.toLowerCase()) ||
      (doc.maskId && doc.maskId.toLowerCase().includes(assignmentDocumentSearch.toLowerCase())) ||
      (doc.docId && doc.docId.toLowerCase().includes(assignmentDocumentSearch.toLowerCase())) ||
      (doc.documentCategory?.name && doc.documentCategory.name.toLowerCase().includes(assignmentDocumentSearch.toLowerCase()));

    return isApproved && matchesSearch;
  });

  // Filter user assignments based on search
  const filteredUserAssignments = userAssignments.filter(user =>
    userAssignmentsSearch === '' ||
    user.firstName.toLowerCase().includes(userAssignmentsSearch.toLowerCase()) ||
    user.lastName.toLowerCase().includes(userAssignmentsSearch.toLowerCase()) ||
    user.email.toLowerCase().includes(userAssignmentsSearch.toLowerCase()) ||
    user.role.toLowerCase().includes(userAssignmentsSearch.toLowerCase()) ||
    user.department.toLowerCase().includes(userAssignmentsSearch.toLowerCase())
  );



  const handleActionClick = async (action: MyAction) => {
    if (!action.id || !action.applicationId) {
      toast({
        title: "Error",
        description: "Action ID and Application ID are required.",
        variant: "destructive"
      });
      return;
    }

    // Check if this is a perform_task, reperform_task, or verify_task action (Review Document)
    if (action.actionType === 'perform_task' || action.actionType === 'reperform_task' || action.actionType === 'verify_task') {
      // Open document review modal
      setSelectedActionForReview(action);
      setIsDocumentReviewOpen(true);
      setReviewComments('');
      setReviewAction(null);

      // Fetch document details for review
      if (action.applicationId) {
        await fetchDocumentDetailsForReview(action.applicationId);
      }
    } else {
      // Navigate to DocumentCurationPage with both applicationId and actionId
      navigate(`/documents/${action.applicationId}/curate/${action.id}`);
    }
  };

  const handleDocumentClick = (document: Document) => {
    console.log('Document clicked:', document);
    setSelectedDocument(document);
    setIsDocumentDetailsOpen(true);
  };

  const handleCategoryClick = (categoryId: string, categoryName: string) => {
    console.log('Category clicked:', categoryName, 'ID:', categoryId);
    // Navigate to the dedicated category page
    navigate(`/apps/doc/category/${categoryId}`);
  };

  const handleAssignmentClick = (assignment: Assignment) => {
    toast({
      title: "Assignment Details",
      description: `Opening details for ${assignment.maskId}`
    });
  };

  // Fetch document details for review modal
  const fetchDocumentDetailsForReview = async (documentId: string) => {
    try {
      setLoadingReviewDocument(true);

      const uriString = {
        include: [
          { relation: "creator" },
          { relation: "documentCategory" },
          { relation: "reviewer" },
          { relation: "approver" },
          { relation: "initiator" }
        ]
      };

      const url = `${DOCUMENTS_API}/${documentId}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await apiService.get(url);
      setReviewDocumentDetails(response);
    } catch (error) {
      console.error('Error fetching document details for review:', error);
      toast({
        title: "Error",
        description: "Failed to load document details for review.",
        variant: "destructive"
      });
    } finally {
      setLoadingReviewDocument(false);
    }
  };

  // Handle document review actions
  const handleReviewAction = async (action: 'approve' | 'reject') => {
    if (!reviewComments.trim()) {
      toast({
        title: "Comments Required",
        description: `Please provide comments for ${action === 'approve' ? 'approval' : 'rejection'}.`,
        variant: "destructive"
      });
      return;
    }

    if (!selectedActionForReview) {
      toast({
        title: "Error",
        description: "No action selected for review.",
        variant: "destructive"
      });
      return;
    }

    try {
      // API call to submit review decision using curator-submit-documents endpoint
      const reviewData = {
        comments: reviewComments.trim(),
        status: action === 'approve' ? 'Completed' : 'Returned'
      };

      // Make API call to curator-submit-documents endpoint
      await apiService.patch(`${API_BASE_URL}/document-task-submit/${selectedActionForReview.id}`, reviewData);

      toast({
        title: `Document ${action === 'approve' ? 'Approved' : 'Rejected'}`,
        description: action === 'approve'
          ? 'Document has been sent to approver for approval.'
          : 'Document has been returned to curator for changes.',
      });

      // Close modal and reset state
      setIsDocumentReviewOpen(false);
      setSelectedActionForReview(null);
      setReviewComments('');
      setReviewAction(null);
      setReviewDocumentDetails(null);
      setLoadingReviewDocument(false);

      // Refresh actions list
      fetchMyActions();

    } catch (error) {
      console.error('Error submitting review:', error);
      toast({
        title: "Review Failed",
        description: `Failed to ${action} document. Please try again.`,
        variant: "destructive"
      });
    }
  };

  // Handle edit document
  const handleEditDocument = (document: Document) => {
    // Check if document can be edited (only NEW type and Approved status)
    if (document.type !== 'New') {
      toast({
        title: "Edit Not Allowed",
        description: "Only documents with type 'NEW' can be edited.",
        variant: "destructive"
      });
      return;
    }

    if (document.status !== 'Approved') {
      toast({
        title: "Edit Not Allowed",
        description: "Only documents with 'Approved' status can be edited.",
        variant: "destructive"
      });
      return;
    }

    // Navigate to document curation page for editing
    navigate(`/documents/${document.id}/curate?mode=edit`);
  };

  // Handle delete document
  const handleDeleteDocument = (document: Document) => {
    // Show confirmation dialog before deleting
    if (window.confirm(`Are you sure you want to delete "${document.name}"? This action cannot be undone.`)) {
      deleteDocument(document.id);
    }
  };

  // Delete document function
  const deleteDocument = async (documentId: string) => {
    try {
      await apiService.delete(`${DOCUMENTS_API}/${documentId}`);

      toast({
        title: "Success",
        description: "Document deleted successfully.",
      });

      // Refresh both document lists
      fetchAllDocumentsForTab(); // For "All Documents" tab
      fetchDocuments(); // For "My Documents" categorization
    } catch (error) {
      console.error('Error deleting document:', error);
      toast({
        title: "Error",
        description: "Failed to delete document. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Handle upload existing document
  const handleUploadExisting = () => {
    setIsUploadModalOpen(true);
  };

  // Handle initiate document
  const handleInitiateDocument = () => {
    setIsInitiateModalOpen(true);
  };

  // Handle form field changes
  const handleFormChange = (field: keyof DocumentInitiation, value: string | Date | undefined) => {
    setDocumentForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle form submission
  const handleFormSubmit = async () => {
    // Basic validation
    if (!documentForm.documentCategoryId || !documentForm.name || !documentForm.purpose || !documentForm.version) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields (Category, Name, Purpose, Version)",
        variant: "destructive"
      });
      return;
    }

    if (!documentForm.creatorId || !documentForm.reviewerId || !documentForm.approverId) {
      toast({
        title: "Validation Error",
        description: "Please select Creator, Reviewer, and Approver",
        variant: "destructive"
      });
      return;
    }

    try {
      // Prepare data for API submission with ISO string dates
      const submissionData = {
        ...documentForm,
        creatorTargetDate: documentForm.creatorTargetDate ? documentForm.creatorTargetDate.toISOString() : '',
        reviewerTargetDate: documentForm.reviewerTargetDate ? documentForm.reviewerTargetDate.toISOString() : '',
        approverTargetDate: documentForm.approverTargetDate ? documentForm.approverTargetDate.toISOString() : ''
      };

      // Send data to API
      await apiService.post(DOCUMENTS_API, submissionData);

      toast({
        title: "Success",
        description: "Document initiated successfully!"
      });

      // Refresh both document lists
      fetchAllDocumentsForTab(); // For "All Documents" tab
      fetchDocuments(); // For "My Documents" categorization

      // Reset form and close modal
      setDocumentForm({
        type: 'New',
        documentCategoryId: '',
        docId: '',
        name: '',
        scopeApplicability: '',
        purpose: '',
        creatorId: '',
        creatorTargetDate: undefined,
        reviewerId: '',
        reviewerTargetDate: undefined,
        approverId: '',
        approverTargetDate: undefined,
        keywords: '',
        version: '1.0'
      });
      setIsInitiateModalOpen(false);
    } catch (error) {
      console.error('Error initiating document:', error);
      toast({
        title: "Error",
        description: "Failed to initiate document. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsInitiateModalOpen(false);
  };

  // Handle upload form field changes
  const handleUploadFormChange = (field: keyof ExistingDocumentUpload, value: string | File | null) => {
    setUploadForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    handleUploadFormChange('file', file);
  };

  // Handle upload form submission
  const handleUploadSubmit = async () => {
    // Basic validation
    if (!uploadForm.documentCategoryId || !uploadForm.name || !uploadForm.purpose || !uploadForm.file || !uploadForm.version) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields, select a file, and specify version",
        variant: "destructive"
      });
      return;
    }

    try {
      // Step 1: Upload file to /files endpoint
      const fileFormData = new FormData();
      fileFormData.append('file', uploadForm.file);

      const fileUploadResponse = await apiService.post(`${API_BASE_URL}/files`, fileFormData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // Get the filename from the response (following the pattern used in other components)
      const uploadedFileName = fileUploadResponse.files?.[0]?.originalname ||
                              fileUploadResponse.data?.files?.[0]?.originalname;

      if (!uploadedFileName) {
        throw new Error('File upload failed - no filename returned');
      }

      // Step 2: Create document record with the uploaded filename
      const documentData = {
        type: uploadForm.type,
        documentCategoryId: uploadForm.documentCategoryId,
        docId: uploadForm.docId,
        name: uploadForm.name,
        scopeApplicability: uploadForm.scopeApplicability,
        purpose: uploadForm.purpose,
        keywords: uploadForm.keywords,
        version: uploadForm.version,
        files: uploadedFileName // Pass the filename as 'files' field
      };

      // Send document data to API
      await apiService.post(DOCUMENTS_API, documentData);

      toast({
        title: "Success",
        description: "Document uploaded successfully!"
      });

      // Refresh both document lists
      fetchAllDocumentsForTab(); // For "All Documents" tab
      fetchDocuments(); // For "My Documents" categorization

      // Reset form and close modal
      setUploadForm({
        type: 'Existing',
        documentCategoryId: '',
        docId: '',
        name: '',
        scopeApplicability: '',
        purpose: '',
        keywords: '',
        file: null,
        version: '1.0'
      });
      setIsUploadModalOpen(false);
    } catch (error) {
      console.error('Error uploading document:', error);
      toast({
        title: "Error",
        description: "Failed to upload document. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Handle upload modal close
  const handleUploadModalClose = () => {
    setIsUploadModalOpen(false);
  };

  // Handle assignment modal functions
  const handleAssignmentSubmit = async () => {
    // Basic validation
    if (selectedDocuments.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please select at least one document",
        variant: "destructive"
      });
      return;
    }

    if (selectedUsers.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please select at least one user to assign",
        variant: "destructive"
      });
      return;
    }

    try {
      setSubmittingAssignment(true);

      // Prepare API payload
      const assignmentPayload = {
        userIds: selectedUsers,
        documentIds: selectedDocuments
      };

      console.log('Assignment Payload:', assignmentPayload);

      // Call the allocate-users API
      const response = await apiService.post('/documents/allocate-users', assignmentPayload);

      console.log('Assignment Response:', response);

      toast({
        title: "Success",
        description: `Assignment created successfully for ${selectedUsers.length} user(s) with ${selectedDocuments.length} document(s).`
      });

      // Reset form and close modal
      handleAssignmentModalClose();
    } catch (error) {
      console.error('Error creating assignment:', error);
      toast({
        title: "Error",
        description: "Failed to create assignment. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSubmittingAssignment(false);
    }
  };

  const handleAssignmentModalClose = () => {
    setIsAssignmentModalOpen(false);
    setSelectedDocuments([]);
    setSelectedUsers([]);
    setAssignmentDocumentSearch('');
  };

  // Wrapper functions for MultiSelect onChange to avoid type conflicts
  const handleUsersChange = (users: string[]) => {
    setSelectedUsers(users);
  };

  // Create tabs with updated counts
  const tabs = [
    {
      value: "myaction",
      label: `My Action (${loadingActions ? '...' : actionCount})`,
      content: (
        <div className="space-y-4">
          <div className="flex items-center justify-between gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search my actions..."
                value={myActionsSearch}
                onChange={(e) => setMyActionsSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="text-sm text-muted-foreground">
              {loadingActions ? (
                <div className="flex items-center">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Loading actions...
                </div>
              ) : (
                `Showing ${filteredMyActions.length} of ${actionCount} items`
              )}
            </div>
          </div>

          {loadingActions ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                <p className="text-muted-foreground">Loading action data...</p>
              </div>
            </div>
          ) : (
            <ExpandableDataTable
              data={filteredMyActions}
              columns={[
                {
                  key: 'timeline',
                  header: 'Timeline',
                  width: 'w-[140px]',
                  filterable: true,
                  filterType: 'select',
                  filterOptions: [
                    { label: 'Upcoming', value: 'Upcoming' },
                    { label: 'Due Now', value: 'Due Now' },
                    { label: 'Overdue', value: 'Overdue' },
                    { label: 'No Due Date', value: 'No Due Date' },
                  ],
                  render: (_, row) => {
                    const timelineStatus = getTimelineStatus(row.dueDate);
                    let badgeClass = '';
                    switch(timelineStatus) {
                      case 'Upcoming': badgeClass = 'bg-teal-600 hover:bg-teal-700 text-white border-teal-700'; break;
                      case 'Due Now': badgeClass = 'bg-amber-500 hover:bg-amber-600 text-black border-amber-600 font-semibold'; break;
                      case 'Overdue': badgeClass = 'bg-red-600 hover:bg-red-700 text-white border-red-700 font-semibold'; break;
                      case 'No Due Date': badgeClass = 'bg-neutral-500 hover:bg-neutral-600 text-white border-neutral-600'; break;
                      case 'Invalid Date': badgeClass = 'bg-rose-500 hover:bg-rose-600 text-white border-rose-600'; break;
                      default: badgeClass = 'bg-gray-400 hover:bg-gray-500 text-white border-gray-500';
                    }
                    return <Badge className={badgeClass}>{timelineStatus}</Badge>;
                  }
                },
                {
                  key: 'maskId',
                  header: 'ID',
                  sortable: true,
                  width: 'w-[120px]',
                  render: (value) => (
                    <span className="text-blue-600 hover:text-blue-800 hover:underline font-medium cursor-pointer">
                      {value}
                    </span>
                  )
                },
                {
                  key: 'actionType',
                  header: 'Required Action',
                  width: 'w-[200px]',
                  filterable: true,
                  filterType: 'text',
                  render: (value) => {
                    switch(value) {
                      case 'doc_initiated':
                        return 'Curate Document';
                      case 'doc_reinitiated':
                        return 'Recurate Document';
                      case 'perform_task':
                        return 'Review Document';
                      case 'reperform_task':
                        return 'Review Document';
                      case 'verify_task':
                        return 'Approve Document';
                      default:
                        return value;
                    }
                  }
                },
                {
                  key: 'dueDate',
                  header: 'Target Date',
                  width: 'w-[140px]',
                  sortable: true,
                  render: (value) => {
                    if (!value) return 'N/A';
                    try {
                      const date = new Date(value);
                      if (isNaN(date.getTime())) return 'N/A';
                      return date.toLocaleDateString();
                    } catch (error) {
                      return 'N/A';
                    }
                  }
                },
                {
                  key: 'status',
                  header: 'Status',
                  width: 'w-[120px]',
                  filterable: true,
                  filterType: 'select',
                  filterOptions: [
                    { label: 'In Progress', value: 'In Progress' },
                    { label: 'Pending', value: 'Pending' },
                    { label: 'Completed', value: 'Completed' },
                    { label: 'Overdue', value: 'Overdue' },
                  ],
                  render: (value) => {
                    let badgeClass = '';
                    let displayValue = value;

                    // Show "Draft" when status is "In Process" or "In Progress"
                    if (value?.toLowerCase() === 'in progress' || value?.toLowerCase() === 'in progress') {
                      displayValue = 'Draft';
                    }

                    switch(value) {
                      case 'In Progress': badgeClass = 'bg-blue-600 hover:bg-blue-700 text-white'; break;
                      case 'Pending': badgeClass = 'bg-yellow-600 hover:bg-yellow-700 text-white'; break;
                      case 'Completed': badgeClass = 'bg-green-600 hover:bg-green-700 text-white'; break;
                      case 'Overdue': badgeClass = 'bg-red-600 hover:bg-red-700 text-white'; break;
                      default: badgeClass = 'bg-gray-500 hover:bg-gray-600 text-white';
                    }
                    return <Badge className={badgeClass}>{displayValue}</Badge>;
                  },
                },
              ]}
              onRowClick={(row) => handleActionClick(row)}
              highlightOnHover={true}
              striped={true}
            />
          )}
        </div>
      ),
    },
    {
      value: "document",
      label: `All Document (${loadingDocuments ? '...' : documentCount})`,
      content: (
        <div className="space-y-4">
        
          <div className="flex items-center justify-between gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search documents..."
                value={documentsSearch}
                onChange={(e) => setDocumentsSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="text-sm text-muted-foreground">
              {loadingDocuments ? (
                <div className="flex items-center">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Loading documents...
                </div>
              ) : (
                `Showing ${filteredDocuments.length} of ${documentCount} items`
              )}
            </div>
          </div>

          {loadingDocuments ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                <p className="text-muted-foreground">Loading document data...</p>
              </div>
            </div>
          ) : filteredDocuments.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No documents found</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Debug: Documents array length: {documents.length}, Filtered: {filteredDocuments.length}
                </p>
              </div>
            </div>
          ) : (
            <ExpandableDataTable
              data={filteredDocuments}
              columns={[
                {
                  key: 'maskId',
                  header: 'Document ID',
                  sortable: true,
                  width: 'w-[140px]',
                  render: (value, row) => (
                    <div>
                      <span className="text-blue-600 hover:text-blue-800 hover:underline font-medium cursor-pointer">
                        {value || 'N/A'}
                      </span>
                    </div>
                  )
                },
                {
                  key: 'docId',
                  header: 'Doc ID',
                  sortable: true,
                  width: 'w-[120px]',
                  render: (value) => (
                    <div>
                      <span className="text-gray-700 font-medium text-sm">
                        {value || 'N/A'}
                      </span>
                    </div>
                  )
                },
                {
                  key: 'name',
                  header: 'Document Name',
                  sortable: true,
                  width: 'w-[250px]',
                  render: (value, row) => (
                    <div>
                      <span className="text-blue-600 hover:text-blue-800 hover:underline font-medium cursor-pointer">
                        {value}
                      </span>
                      <p className="text-xs text-muted-foreground">{row.documentCategory?.name || row.category}</p>
                    </div>
                  )
                },
                {
                  key: 'type',
                  header: 'Type',
                  width: 'w-[100px]',
                  filterable: true,
                  filterType: 'text',
                  render: (value, row) => {
                    const getTypeColor = (type: string) => {
                      switch (type) {
                        case 'Existing':
                          return 'bg-blue-100 text-blue-800 border-blue-200';
                        case 'New':
                          return 'bg-green-100 text-green-800 border-green-200';
                        default:
                          return 'bg-gray-100 text-gray-800 border-gray-200';
                      }
                    };

                    return (
                      <div className="text-center">
                        <div className={`inline-flex items-center px-2 py-1 rounded-full border text-xs font-medium uppercase ${getTypeColor(row.type)}`}>
                          {row.type}
                        </div>
                      </div>
                    );
                  }
                },
                {
                  key: 'status',
                  header: 'Status',
                  width: 'w-[120px]',
                  filterable: true,
                  filterType: 'text',
                  render: (value, row) => {
                    const getStatusColor = (status: string) => {
                      switch (status?.toLowerCase()) {
                        case 'approved':
                          return 'bg-green-100 text-green-800 border-green-200';
                        case 'under review':
                        case 'review':
                          return 'bg-yellow-100 text-yellow-800 border-yellow-200';
                        case 'rejected':
                          return 'bg-red-100 text-red-800 border-red-200';
                        case 'draft':
                          return 'bg-gray-100 text-gray-800 border-gray-200';
                        case 'pending':
                          return 'bg-orange-100 text-orange-800 border-orange-200';
                        default:
                          return 'bg-gray-100 text-gray-800 border-gray-200';
                      }
                    };

                    const displayStatus = row.status || 'Draft';

                    return (
                      <div className="text-center">
                        <div className={`inline-flex items-center px-2 py-1 rounded-full border text-xs font-medium ${getStatusColor(displayStatus)}`}>
                          {displayStatus}
                        </div>
                      </div>
                    );
                  }
                },
                {
                  key: 'initiator',
                  header: 'Initiator',
                  width: 'w-[140px]',
                  filterable: true,
                  filterType: 'text',
                  render: (value, row) => (
                    <div>
                      <p className="font-medium text-sm">{row.initiator?.firstName || 'Not assigned'}</p>
                      {row.initiator?.email && (
                        <p className="text-xs text-muted-foreground">{row.initiator.email}</p>
                      )}
                    </div>
                  )
                },
                {
                  key: 'creator',
                  header: 'Creator',
                  width: 'w-[140px]',
                  filterable: true,
                  filterType: 'text',
                  render: (value, row) => {
                    const creatorName = row.creator?.firstName;
                    const uploadedBy = row.uploadedBy && row.uploadedBy !== 'Not assigned' ? row.uploadedBy : null;
                    const hasCreator = creatorName || uploadedBy;

                    if (!hasCreator) {
                      return (
                        <div>
                          <p className="font-medium text-sm">Not assigned</p>
                        </div>
                      );
                    }
                    return (
                      <div>
                        <p className="font-medium text-sm">{hasCreator}</p>
                        {row.creatorTargetDate && (
                          <div className="text-xs">
                            {(() => {
                              try {
                                const date = new Date(row.creatorTargetDate);
                                const today = new Date();
                                const isOverdue = date < today;
                                return (
                                  <span className={isOverdue ? 'text-red-600 font-medium' : 'text-muted-foreground'}>
                                    Due: {format(date, 'MMM dd, yyyy')}
                                  </span>
                                );
                              } catch (error) {
                                return <span className="text-muted-foreground">Invalid date</span>;
                              }
                            })()}
                          </div>
                        )}
                      </div>
                    );
                  }
                },
                {
                  key: 'reviewer',
                  header: 'Reviewer',
                  width: 'w-[140px]',
                  render: (value, row) => {
                    const hasReviewer = row.reviewer?.firstName;
                    if (!hasReviewer) {
                      return (
                        <div>
                          <p className="font-medium text-sm">Not assigned</p>
                        </div>
                      );
                    }
                    return (
                      <div>
                        <p className="font-medium text-sm">{hasReviewer}</p>
                        {row.reviewerTargetDate && (
                          <p className="text-xs text-amber-600">
                            Due: {format(new Date(row.reviewerTargetDate), 'MMM dd')}
                          </p>
                        )}
                      </div>
                    );
                  }
                },
                {
                  key: 'approver',
                  header: 'Approver',
                  width: 'w-[140px]',
                  render: (value, row) => {
                    const hasApprover = row.approver?.firstName;
                    if (!hasApprover) {
                      return (
                        <div>
                          <p className="font-medium text-sm">Not assigned</p>
                        </div>
                      );
                    }
                    return (
                      <div>
                        <p className="font-medium text-sm">{hasApprover}</p>
                        {row.approverTargetDate && (
                          <p className="text-xs text-amber-600">
                            Due: {format(new Date(row.approverTargetDate), 'MMM dd')}
                          </p>
                        )}
                      </div>
                    );
                  }
                },
                {
                  key: 'created',
                  header: 'Created',
                  width: 'w-[120px]',
                  sortable: true,
                  render: (value) => {
                    if (!value) return 'N/A';
                    try {
                      const date = new Date(value);
                      return (
                        <div>
                          <p className="text-sm font-medium">{format(date, 'MMM dd')}</p>
                          <p className="text-xs text-muted-foreground">{format(date, 'yyyy HH:mm')}</p>
                        </div>
                      );
                    } catch (error) {
                      return 'Invalid';
                    }
                  }
                },
                {
                  key: 'actions',
                  header: 'Actions',
                  width: 'w-[120px]',
                  render: (value, row) => {
                    // Check if document can be edited (only NEW type and Approved status)
                    const canEdit = row.type === 'New' && row.status === 'Approved';

                    return (
                      <div className="flex items-center justify-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditDocument(row);
                          }}
                          disabled={!canEdit}
                          className={`h-8 w-8 p-0 ${canEdit
                            ? 'hover:bg-blue-50 hover:border-blue-300'
                            : 'opacity-50 cursor-not-allowed'
                          }`}
                          title={canEdit
                            ? "Edit Document"
                            : "Edit only allowed for NEW type documents with Approved status"
                          }
                        >
                          <Edit className={`h-4 w-4 ${canEdit ? 'text-blue-600' : 'text-gray-400'}`} />
                        </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteDocument(row);
                        }}
                        className="h-8 w-8 p-0 hover:bg-red-50 hover:border-red-300"
                        title="Delete Document"
                      >
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </Button>
                    </div>
                  );
                }
              }
              ]}
              onRowClick={(row) => handleDocumentClick(row)}
              highlightOnHover={true}
              striped={true}
            />
          )}
        </div>
      )
    },
    {
      value: "mydocuments",
      label: "My Documents",
      content: (
        <div className="space-y-6">
          <div className="flex items-center justify-between gap-4 mb-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Document Categories</h2>
              <p className="text-sm text-gray-600 mt-1">Browse documents by category</p>
            </div>
            <div className="text-sm text-muted-foreground">
              {loadingCategories ? (
                <div className="flex items-center">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Loading categories...
                </div>
              ) : (
                `${categoryOptions.length} categories available`
              )}
            </div>
          </div>

          {loadingCategories ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                <p className="text-muted-foreground">Loading categories...</p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {categoryOptions.length > 0 ? (
                categoryOptions.map((category) => {
                  // Count documents in this category using categorizedDocuments
                  const categoryDocuments = categorizedDocuments[category.value] || [];
                  const categoryDocCount = categoryDocuments.length;

                  return (
                    <div
                      key={category.value}
                      className="group relative bg-white rounded-xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-200 cursor-pointer overflow-hidden"
                      onClick={() => handleCategoryClick(category.value, category.label)}
                    >
                      {/* Category Icon Background */}
                      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-50 to-purple-50 rounded-bl-full opacity-60"></div>

                      {/* Content */}
                      <div className="relative p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="p-3 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
                            <FileText className="h-6 w-6 text-blue-600" />
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-gray-900">{categoryDocCount}</div>
                            <div className="text-xs text-gray-500">documents</div>
                          </div>
                        </div>

                        <div>
                          <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                            {category.label}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {categoryDocCount === 0
                              ? 'No documents yet'
                              : `${categoryDocCount} document${categoryDocCount !== 1 ? 's' : ''} available`
                            }
                          </p>
                        </div>

                        {/* Hover indicator */}
                        <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-200"></div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
                  <div className="p-4 bg-gray-100 rounded-full mb-4">
                    <FileText className="h-8 w-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Categories Available</h3>
                  <p className="text-gray-600 max-w-md">
                    Document categories will appear here once they are configured in the system.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      )
    },
    {
      value: "assignment",
      label: `User Assignments (${loadingUserAssignments ? '...' : userAssignments.length})`,
      content: (
        <div className="space-y-4">
          <div className="flex items-center justify-between gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search users..."
                value={userAssignmentsSearch}
                onChange={(e) => setUserAssignmentsSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex items-center gap-4">
              <Button
                onClick={() => {
                  setIsAssignmentModalOpen(true);
                  fetchAllUsers();
                }}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                <Plus className="h-4 w-4 mr-2" /> Assignment
              </Button>
              <div className="text-sm text-muted-foreground">
                {loadingUserAssignments ? (
                  <div className="flex items-center">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading users...
                  </div>
                ) : (
                  `Showing ${filteredUserAssignments.length} of ${userAssignments.length} users`
                )}
              </div>
            </div>
          </div>

          {loadingUserAssignments ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                <p className="text-muted-foreground">Loading user assignments...</p>
              </div>
            </div>
          ) : (
            <ExpandableDataTable
              data={filteredUserAssignments}
              columns={[
                {
                  key: 'firstName',
                  header: 'Name',
                  sortable: true,
                  width: 'w-[120px]',
                  render: (value) => (
                    <span className="font-medium text-gray-900">
                      {value || 'N/A'}
                    </span>
                  )
                },
                // {
                //   key: 'lastName',
                //   header: 'Last Name',
                //   sortable: true,
                //   width: 'w-[120px]',
                //   render: (value) => (
                //     <span className="font-medium text-gray-900">
                //       {value || 'N/A'}
                //     </span>
                //   )
                // },
                {
                  key: 'email',
                  header: 'Email',
                  sortable: true,
                  width: 'w-[200px]',
                  render: (value) => (
                    <span className="text-blue-600 hover:text-blue-800">
                      {value || 'N/A'}
                    </span>
                  )
                },
                // {
                //   key: 'role',
                //   header: 'Role',
                //   sortable: true,
                //   width: 'w-[120px]',
                //   filterable: true,
                //   filterType: 'text',
                //   render: (value) => (
                //     <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                //       {value || 'N/A'}
                //     </span>
                //   )
                // },
                // {
                //   key: 'department',
                //   header: 'Department',
                //   sortable: true,
                //   width: 'w-[140px]',
                //   filterable: true,
                //   filterType: 'text',
                //   render: (value) => (
                //     <span className="text-gray-700">
                //       {value || 'N/A'}
                //     </span>
                //   )
                // },
                // {
                //   key: 'status',
                //   header: 'Status',
                //   sortable: true,
                //   width: 'w-[100px]',
                //   filterable: true,
                //   filterType: 'text',
                //   render: (value) => {
                //     const getStatusColor = (status: any) => {
                //       const statusStr = status ? String(status).toLowerCase() : '';
                //       switch (statusStr) {
                //         case 'active':
                //           return 'bg-green-100 text-green-800';
                //         case 'inactive':
                //           return 'bg-red-100 text-red-800';
                //         case 'pending':
                //           return 'bg-yellow-100 text-yellow-800';
                //         default:
                //           return 'bg-gray-100 text-gray-800';
                //       }
                //     };

                //     return (
                //       <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(value)}`}>
                //         {value || 'N/A'}
                //       </span>
                //     );
                //   }
                // },
                {
                  key: 'assignedDocuments',
                  header: 'Assigned Documents',
                  sortable: true,
                  width: 'w-[140px]',
                  render: (value) => (
                    <div className="text-center">
                      <span className="inline-flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">
                        {value || 0}
                      </span>
                    </div>
                  )
                },
                {
                  key: 'actions',
                  header: 'Actions',
                  width: 'w-[120px]',
                  render: (value, row) => (
                    <Button
                      size="sm"
                      variant="outline"
                      className="border-purple-600 text-purple-600 hover:bg-purple-50"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Call API to fetch user documents
                        fetchUserDocuments(row.id, row);
                      }}
                    >
                      <Users className="h-3 w-3 mr-1" />
                      View Assignment
                    </Button>
                  )
                }
              ]}
              highlightOnHover={true}
              striped={true}
            />
          )}
        </div>
      ),
    }
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <PageHeader
          title="Documents"
          description="Manage and organize your documents, files, and digital assets"
        />
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleUploadExisting}
            className="bg-blue-50 hover:bg-blue-100 border-blue-300 text-blue-700"
          >
            <Upload className="h-4 w-4 mr-2" /> Upload Existing Document
          </Button>
          <Button
            onClick={handleInitiateDocument}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <Plus className="h-4 w-4 mr-2" /> Initiate Document
          </Button>
        </div>
      </div>

      <TabsContainer
        key={`tabs-${actionCount}-${documentCount}-${assignmentCount}`}
        tabs={tabs}
        defaultValue="myaction"
        onValueChange={(value) => setActiveTab(value)}
      />

      {/* Document Initiation Modal */}
      <Dialog open={isInitiateModalOpen} onOpenChange={setIsInitiateModalOpen}>
        <DialogContent className="max-w-5xl max-h-[95vh] overflow-y-auto">
          <DialogHeader className="pb-6 border-b">
            <DialogTitle className="text-2xl font-semibold text-gray-900 flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <FileText className="h-6 w-6 text-green-600" />
              </div>
              Initiate New Document
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-2">
              Create a new document workflow with assigned roles and target dates
            </p>
          </DialogHeader>

          <div className="py-6 space-y-8">
            {/* Document Information Section */}
            <div className="bg-blue-50 rounded-lg p-6 space-y-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-6 bg-blue-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Document Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Category */}
                <div className="space-y-2">
                  <Label htmlFor="category" className="text-sm font-medium text-gray-700">
                    Document Category <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={documentForm.documentCategoryId}
                    onValueChange={(value) => handleFormChange('documentCategoryId', value)}
                    disabled={loadingCategories}
                  >
                    <SelectTrigger className="h-11">
                      <SelectValue placeholder={loadingCategories ? "Loading categories..." : "Select category"} />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingCategories ? (
                        <SelectItem value="loading" disabled>
                          <div className="flex items-center">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Loading categories...
                          </div>
                        </SelectItem>
                      ) : categoryOptions.length > 0 ? (
                        categoryOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-categories" disabled>
                          No categories available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                {/* Document ID */}
                <div className="space-y-2">
                  <Label htmlFor="docId" className="text-sm font-medium text-gray-700">
                    Document ID
                  </Label>
                  <Input
                    id="docId"
                    value={documentForm.docId}
                    onChange={(e) => handleFormChange('docId', e.target.value)}
                    placeholder="Enter document ID"
                    className="h-11"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 gap-6">
                {/* Name */}
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                    Document Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    value={documentForm.name}
                    onChange={(e) => handleFormChange('name', e.target.value)}
                    placeholder="Enter document name"
                    className="h-11"
                  />
                </div>
              </div>

              {/* Scope & Applicability */}
              <div className="space-y-2">
                <Label htmlFor="scope" className="text-sm font-medium text-gray-700">
                  Scope & Applicability
                </Label>
                <Textarea
                  id="scope"
                  value={documentForm.scopeApplicability}
                  onChange={(e) => handleFormChange('scopeApplicability', e.target.value)}
                  placeholder="Describe the scope and applicability of this document"
                  rows={3}
                  className="resize-none"
                />
              </div>

              {/* Purpose */}
              <div className="space-y-2">
                <Label htmlFor="purpose" className="text-sm font-medium text-gray-700">
                  Purpose <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="purpose"
                  value={documentForm.purpose}
                  onChange={(e) => handleFormChange('purpose', e.target.value)}
                  placeholder="Explain the purpose and objectives of this document"
                  rows={3}
                  className="resize-none"
                />
              </div>
            </div>

            {/* Workflow Assignment Section */}
            <div className="bg-gray-50 rounded-lg p-6 space-y-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-6 bg-green-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Workflow Assignment</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Creator */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 font-semibold text-xs">1</span>
                    </div>
                    <Label className="text-sm font-medium text-gray-700">
                      Creator <span className="text-red-500">*</span>
                    </Label>
                  </div>
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Assigned To</Label>
                      <Select
                        value={documentForm.creatorId}
                        onValueChange={(value) => handleFormChange('creatorId', value)}
                        disabled={loadingUsers}
                      >
                        <SelectTrigger className="h-10">
                          <SelectValue placeholder={loadingUsers ? "Loading..." : "Select creator"} />
                        </SelectTrigger>
                        <SelectContent>
                          {loadingUsers ? (
                            <SelectItem value="loading" disabled>
                              <div className="flex items-center">
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Loading...
                              </div>
                            </SelectItem>
                          ) : creatorOptions.length > 0 ? (
                            creatorOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="no-creators" disabled>
                              No creators available
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Target Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={`w-full h-10 justify-start text-left font-normal ${!documentForm.creatorTargetDate && "text-muted-foreground"}`}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {documentForm.creatorTargetDate ? format(documentForm.creatorTargetDate, "MMM dd") : "Select date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={documentForm.creatorTargetDate}
                            onSelect={(date) => handleFormChange('creatorTargetDate', date)}
                            disabled={(date) => {
                              const today = new Date();
                              today.setHours(0, 0, 0, 0);
                              return date < today; // Disable past dates
                            }}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <p className="text-xs text-gray-500 mt-1">
                        Select a future date for document creation
                      </p>
                    </div>
                  </div>
                </div>

                {/* Reviewer */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-semibold text-xs">2</span>
                    </div>
                    <Label className="text-sm font-medium text-gray-700">
                      Reviewer <span className="text-red-500">*</span>
                    </Label>
                  </div>
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Assigned To</Label>
                      <Select
                        value={documentForm.reviewerId}
                        onValueChange={(value) => handleFormChange('reviewerId', value)}
                        disabled={loadingUsers}
                      >
                        <SelectTrigger className="h-10">
                          <SelectValue placeholder={loadingUsers ? "Loading..." : "Select reviewer"} />
                        </SelectTrigger>
                        <SelectContent>
                          {loadingUsers ? (
                            <SelectItem value="loading" disabled>
                              <div className="flex items-center">
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Loading...
                              </div>
                            </SelectItem>
                          ) : reviewerOptions.length > 0 ? (
                            reviewerOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="no-reviewers" disabled>
                              No reviewers available
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Target Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={`w-full h-10 justify-start text-left font-normal ${!documentForm.reviewerTargetDate && "text-muted-foreground"}`}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {documentForm.reviewerTargetDate ? format(documentForm.reviewerTargetDate, "MMM dd") : "Select date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={documentForm.reviewerTargetDate}
                            onSelect={(date) => handleFormChange('reviewerTargetDate', date)}
                            disabled={(date) => {
                              // Minimum date is the curator's target date, or today if curator date not set
                              const minDate = documentForm.creatorTargetDate || new Date();
                              minDate.setHours(0, 0, 0, 0);
                              return date < minDate;
                            }}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <p className="text-xs text-gray-500 mt-1">
                        {documentForm.creatorTargetDate
                          ? `Must be on or after ${format(documentForm.creatorTargetDate, 'MMM dd, yyyy')}`
                          : 'Must be on or after curator date'
                        }
                      </p>
                    </div>
                  </div>
                </div>

                {/* Approver */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                      <span className="text-purple-600 font-semibold text-xs">3</span>
                    </div>
                    <Label className="text-sm font-medium text-gray-700">
                      Approver <span className="text-red-500">*</span>
                    </Label>
                  </div>
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Assigned To</Label>
                      <Select
                        value={documentForm.approverId}
                        onValueChange={(value) => handleFormChange('approverId', value)}
                        disabled={loadingUsers}
                      >
                        <SelectTrigger className="h-10">
                          <SelectValue placeholder={loadingUsers ? "Loading..." : "Select approver"} />
                        </SelectTrigger>
                        <SelectContent>
                          {loadingUsers ? (
                            <SelectItem value="loading" disabled>
                              <div className="flex items-center">
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Loading...
                              </div>
                            </SelectItem>
                          ) : approverOptions.length > 0 ? (
                            approverOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="no-approvers" disabled>
                              No approvers available
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Target Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={`w-full h-10 justify-start text-left font-normal ${!documentForm.approverTargetDate && "text-muted-foreground"}`}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {documentForm.approverTargetDate ? format(documentForm.approverTargetDate, "MMM dd") : "Select date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={documentForm.approverTargetDate}
                            onSelect={(date) => handleFormChange('approverTargetDate', date)}
                            disabled={(date) => {
                              // Minimum date is the reviewer's target date, or curator date, or today
                              const minDate = documentForm.reviewerTargetDate || documentForm.creatorTargetDate || new Date();
                              minDate.setHours(0, 0, 0, 0);
                              return date < minDate;
                            }}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <p className="text-xs text-gray-500 mt-1">
                        {documentForm.reviewerTargetDate
                          ? `Must be on or after ${format(documentForm.reviewerTargetDate, 'MMM dd, yyyy')}`
                          : documentForm.creatorTargetDate
                            ? `Must be on or after ${format(documentForm.creatorTargetDate, 'MMM dd, yyyy')}`
                            : 'Must be on or after reviewer date'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Keywords Section */}
            <div className="bg-amber-50 rounded-lg p-6 space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-6 bg-amber-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Additional Information</h3>
              </div>

              <div className="space-y-2">
                <Label htmlFor="keywords" className="text-sm font-medium text-gray-700">
                  Preliminary Keywords/Tags
                  <span className="text-xs text-gray-500 font-normal ml-2">(Optional)</span>
                </Label>
                <Textarea
                  id="keywords"
                  value={documentForm.keywords}
                  onChange={(e) => handleFormChange('keywords', e.target.value)}
                  placeholder="Add basic keywords separated by commas for easy search and retrieval. These can be expanded during review and approval phases."
                  rows={3}
                  className="resize-none"
                />
                <p className="text-xs text-gray-500">
                  Example: safety, procedures, training, compliance, guidelines
                </p>
              </div>

              {/* Version Field */}
              <div className="space-y-2">
                <Label htmlFor="version" className="text-sm font-medium text-gray-700">
                  Version <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="version"
                  value={documentForm.version}
                  onChange={(e) => handleFormChange('version', e.target.value)}
                  placeholder="Enter document version (e.g., 1.0, 2.1, etc.)"
                  className="w-full"
                />
                <p className="text-xs text-gray-500">
                  Specify the version number for this document
                </p>
              </div>
            </div>
          </div>

          {/* Modal Footer */}
          <div className="flex justify-between items-center pt-6 border-t bg-gray-50 -mx-6 -mb-6 px-6 py-4 rounded-b-lg">
            <div className="text-sm text-gray-600">
              <span className="font-medium">Required fields</span> are marked with <span className="text-red-500">*</span>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={handleModalClose}
                className="px-6"
              >
                Cancel
              </Button>
              <Button
                onClick={handleFormSubmit}
                className="bg-green-600 hover:bg-green-700 text-white px-6"
              >
                <FileText className="h-4 w-4 mr-2" />
                Initiate Document
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Existing Document Upload Modal */}
      <Dialog open={isUploadModalOpen} onOpenChange={setIsUploadModalOpen}>
        <DialogContent className="max-w-4xl max-h-[95vh] overflow-y-auto">
          <DialogHeader className="pb-6 border-b">
            <DialogTitle className="text-2xl font-semibold text-gray-900 flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Upload className="h-6 w-6 text-blue-600" />
              </div>
              Upload Existing Document
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-2">
              Upload and categorize an existing document with metadata
            </p>
          </DialogHeader>

          <div className="py-6 space-y-8">
            {/* Document Upload Section */}
            <div className="bg-blue-50 rounded-lg p-6 space-y-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-6 bg-blue-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Document Upload</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Category */}
                <div className="space-y-2">
                  <Label htmlFor="upload-category" className="text-sm font-medium text-gray-700">
                    Document Category <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={uploadForm.documentCategoryId}
                    onValueChange={(value) => handleUploadFormChange('documentCategoryId', value)}
                    disabled={loadingCategories}
                  >
                    <SelectTrigger className="h-11">
                      <SelectValue placeholder={loadingCategories ? "Loading categories..." : "Select category"} />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingCategories ? (
                        <SelectItem value="loading" disabled>
                          <div className="flex items-center">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Loading categories...
                          </div>
                        </SelectItem>
                      ) : categoryOptions.length > 0 ? (
                        categoryOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-categories" disabled>
                          No categories available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                {/* Document ID */}
                <div className="space-y-2">
                  <Label htmlFor="upload-docId" className="text-sm font-medium text-gray-700">
                    Document ID
                  </Label>
                  <Input
                    id="upload-docId"
                    value={uploadForm.docId}
                    onChange={(e) => handleUploadFormChange('docId', e.target.value)}
                    placeholder="Enter document ID"
                    className="h-11"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 gap-6">
                {/* Document Name */}
                <div className="space-y-2">
                  <Label htmlFor="upload-name" className="text-sm font-medium text-gray-700">
                    Document Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="upload-name"
                    value={uploadForm.name}
                    onChange={(e) => handleUploadFormChange('name', e.target.value)}
                    placeholder="Enter document name"
                    className="h-11"
                  />
                </div>
              </div>

              {/* File Upload */}
              <div className="space-y-2">
                <Label htmlFor="file-upload" className="text-sm font-medium text-gray-700">
                  Document File <span className="text-red-500">*</span>
                </Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                  <Input
                    id="file-upload"
                    type="file"
                    onChange={handleFileSelect}
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif"
                    className="hidden"
                  />
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <div className="flex flex-col items-center gap-2">
                      <div className="p-3 bg-blue-100 rounded-full">
                        <Upload className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">
                          Click to upload or drag and drop
                        </p>
                        <p className="text-xs text-gray-500">
                          PDF, DOC, XLS, PPT, TXT, Images (Max 50MB)
                        </p>
                      </div>
                    </div>
                  </label>
                </div>
                {uploadForm.file && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3 mt-3">
                    <div className="flex items-center gap-2">
                      <div className="p-1 bg-green-100 rounded">
                        <FileText className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-green-800">
                          {uploadForm.file.name}
                        </p>
                        <p className="text-xs text-green-600">
                          {(uploadForm.file.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Document Information Section */}
            <div className="bg-gray-50 rounded-lg p-6 space-y-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-6 bg-green-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Document Information</h3>
              </div>

              {/* Scope & Applicability */}
              <div className="space-y-2">
                <Label htmlFor="upload-scope" className="text-sm font-medium text-gray-700">
                  Scope & Applicability
                </Label>
                <Textarea
                  id="upload-scope"
                  value={uploadForm.scopeApplicability}
                  onChange={(e) => handleUploadFormChange('scopeApplicability', e.target.value)}
                  placeholder="Describe the scope and applicability of this document"
                  rows={3}
                  className="resize-none"
                />
              </div>

              {/* Purpose */}
              <div className="space-y-2">
                <Label htmlFor="upload-purpose" className="text-sm font-medium text-gray-700">
                  Purpose <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="upload-purpose"
                  value={uploadForm.purpose}
                  onChange={(e) => handleUploadFormChange('purpose', e.target.value)}
                  placeholder="Explain the purpose and objectives of this document"
                  rows={3}
                  className="resize-none"
                />
              </div>
            </div>

            {/* Keywords Section */}
            <div className="bg-amber-50 rounded-lg p-6 space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-6 bg-amber-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Search & Discovery</h3>
              </div>

              <div className="space-y-2">
                <Label htmlFor="upload-keywords" className="text-sm font-medium text-gray-700">
                  Keywords/Tags
                  <span className="text-xs text-gray-500 font-normal ml-2">(Optional)</span>
                </Label>
                <Textarea
                  id="upload-keywords"
                  value={uploadForm.keywords}
                  onChange={(e) => handleUploadFormChange('keywords', e.target.value)}
                  placeholder="Add keywords separated by commas for easy search and retrieval"
                  rows={2}
                  className="resize-none"
                />
                <p className="text-xs text-gray-500">
                  Example: safety, procedures, training, compliance, guidelines
                </p>
              </div>

              {/* Version Field */}
              <div className="space-y-2">
                <Label htmlFor="upload-version" className="text-sm font-medium text-gray-700">
                  Version <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="upload-version"
                  value={uploadForm.version}
                  onChange={(e) => handleUploadFormChange('version', e.target.value)}
                  placeholder="Enter document version (e.g., 1.0, 2.1, etc.)"
                  className="w-full"
                />
                <p className="text-xs text-gray-500">
                  Specify the version number for this document
                </p>
              </div>
            </div>
          </div>

          {/* Modal Footer */}
          <div className="flex justify-between items-center pt-6 border-t bg-gray-50 -mx-6 -mb-6 px-6 py-4 rounded-b-lg">
            <div className="text-sm text-gray-600">
              <span className="font-medium">Required fields</span> are marked with <span className="text-red-500">*</span>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={handleUploadModalClose}
                className="px-6"
              >
                Cancel
              </Button>
              <Button
                onClick={handleUploadSubmit}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6"
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Document
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Document Details Modal */}
      <Dialog open={isDocumentDetailsOpen} onOpenChange={setIsDocumentDetailsOpen}>
        <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
          <DialogHeader className="pb-6 border-b">
            <DialogTitle className="text-2xl font-semibold text-gray-900 flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              Document Details
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-2">
              Complete information about the selected document
            </p>
          </DialogHeader>

          {selectedDocument ? (
            <DocumentDetailsView document={selectedDocument} />
          ) : (
            <div className="p-8 text-center text-gray-500">
              No document selected
            </div>
          )}

          {/* Modal Footer */}
          <div className="flex justify-end items-center pt-6 border-t bg-gray-50 -mx-6 -mb-6 px-6 py-4 rounded-b-lg">
            <Button
              variant="outline"
              onClick={() => setIsDocumentDetailsOpen(false)}
              className="px-6"
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Assignment Modal */}
      <Dialog open={isAssignmentModalOpen} onOpenChange={setIsAssignmentModalOpen}>
        <DialogContent className="max-w-4xl max-h-[95vh] overflow-y-auto">
          <DialogHeader className="pb-6 border-b">
            <DialogTitle className="text-2xl font-semibold text-gray-900 flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              Create Assignment
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-2">
              Assign documents to multiple users
            </p>
          </DialogHeader>

          <div className="py-6">
            {/* Document Selection Section - Moved to top */}
            <div className="bg-green-50 rounded-lg p-6 mb-8">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-6 bg-green-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Select Documents</h3>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left side - Document list with checkboxes */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    Documents to Assign <span className="text-red-500">*</span>
                    <span className="text-gray-500 font-normal ml-2">
                      ({filteredAssignmentDocuments.length} approved {assignmentDocumentSearch ? 'found' : 'available'})
                    </span>
                  </Label>
                  <p className="text-xs text-gray-500 mt-1">
                    Only approved documents are available for assignment
                  </p>

                  {/* Search input for documents */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search documents..."
                      value={assignmentDocumentSearch}
                      onChange={(e) => setAssignmentDocumentSearch(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <div className="border rounded-lg bg-white max-h-80 overflow-y-auto">
                    {filteredAssignmentDocuments.length > 0 ? (
                      <div className="p-4 space-y-3">
                        {filteredAssignmentDocuments.map(doc => (
                          <div key={doc.id} className="flex items-start gap-3 p-3 hover:bg-gray-50 rounded-lg border border-transparent hover:border-gray-200 transition-colors">
                            <input
                              type="checkbox"
                              id={`doc-${doc.id}`}
                              checked={selectedDocuments.includes(doc.id)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedDocuments([...selectedDocuments, doc.id]);
                                } else {
                                  setSelectedDocuments(selectedDocuments.filter(id => id !== doc.id));
                                }
                              }}
                              className="mt-1 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                            />
                            <label htmlFor={`doc-${doc.id}`} className="flex-1 cursor-pointer">
                              <div className="flex items-center gap-2 mb-1">
                                <FileCheck className="h-4 w-4 text-green-600" />
                                <p className="text-sm font-medium text-gray-900">{doc.name}</p>
                              </div>
                              <p className="text-xs text-gray-500">
                                {doc.maskId || doc.docId || 'No ID'} • {doc.category}
                              </p>
                            </label>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-8 text-center text-gray-500">
                        <FileCheck className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                        <p>
                          {assignmentDocumentSearch
                            ? `No approved documents found matching "${assignmentDocumentSearch}"`
                            : 'No approved documents available for assignment'
                          }
                        </p>
                        {assignmentDocumentSearch && (
                          <button
                            onClick={() => setAssignmentDocumentSearch('')}
                            className="text-blue-600 hover:text-blue-800 text-sm mt-2 underline"
                          >
                            Clear search
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-gray-500">
                    {selectedDocuments.length} document(s) selected
                  </p>
                </div>

                {/* Right side - Selected documents */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    Selected Documents ({selectedDocuments.length})
                  </Label>
                  <div className="border rounded-lg bg-white max-h-80 overflow-y-auto">
                    {selectedDocuments.length > 0 ? (
                      <div className="p-4 space-y-2">
                        {selectedDocuments.map(docId => {
                          const doc = documents.find(d => d.id === docId);
                          return doc ? (
                            <div key={docId} className="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                              <FileCheck className="h-4 w-4 text-green-600" />
                              <div className="flex-1">
                                <p className="text-sm font-medium text-gray-900">{doc.name}</p>
                                <p className="text-xs text-gray-500">
                                  {doc.maskId || doc.docId || 'No ID'} • {doc.category}
                                </p>
                              </div>
                              <button
                                onClick={() => setSelectedDocuments(selectedDocuments.filter(id => id !== docId))}
                                className="text-red-500 hover:text-red-700 p-1"
                                title="Remove document"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            </div>
                          ) : null;
                        })}
                      </div>
                    ) : (
                      <div className="p-8 text-center text-gray-400">
                        <FileCheck className="h-8 w-8 mx-auto mb-2" />
                        <p className="text-sm">No documents selected</p>
                        <p className="text-xs">Select documents from the left to see them here</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* User Selection Section */}
            <div className="bg-blue-50 rounded-lg p-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-6 bg-blue-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Select Users</h3>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  Assign to Users <span className="text-red-500">*</span>
                </Label>
                {loadingAllUsers ? (
                  <div className="flex items-center justify-center h-12 border rounded-lg">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading users...
                  </div>
                ) : (
                  <div>
                    <MultiSelect
                      options={allUsers}
                      selected={selectedUsers}
                      onChange={handleUsersChange as unknown as (selected: string[]) => void}
                      placeholder="Select users to assign documents to"
                    />
                  </div>
                )}
                <p className="text-xs text-gray-500">
                  Selected {selectedUsers.length} user(s)
                </p>
              </div>
            </div>
          </div>

          {/* Modal Footer */}
          <div className="flex justify-end items-center gap-3 pt-6 border-t bg-gray-50 -mx-6 -mb-6 px-6 py-4 rounded-b-lg">
            <Button
              variant="outline"
              onClick={handleAssignmentModalClose}
              className="px-6"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAssignmentSubmit}
              disabled={submittingAssignment}
              className="px-6 bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50"
            >
              {submittingAssignment ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Creating Assignment...
                </>
              ) : (
                'Create Assignment'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Document Review Modal */}
      <Dialog open={isDocumentReviewOpen} onOpenChange={(open) => {
        setIsDocumentReviewOpen(open);
        if (!open) {
          setSelectedActionForReview(null);
          setReviewComments('');
          setReviewAction(null);
          setReviewDocumentDetails(null);
        }
      }}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-gray-900">
              {selectedActionForReview?.actionType === 'verify_task'
                ? 'Approve Document'
                : selectedActionForReview?.actionType === 'perform_task'
                  ? 'Document Review - Review Document'
                  : 'Document Review - Document Action'
              }
            </DialogTitle>
            <DialogDescription>
              Review the document details and provide your decision with comments.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Action Information */}
            {selectedActionForReview && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-medium text-blue-900 mb-2">Action Details</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-blue-800">Action ID:</span>
                    <span className="ml-2 text-blue-700">{selectedActionForReview.maskId}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Description:</span>
                    <span className="ml-2 text-blue-700">
                      {selectedActionForReview.actionType === 'verify_task' && selectedActionForReview.comments
                        ? selectedActionForReview.comments
                        : selectedActionForReview.description
                      }
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Submitted By:</span>
                    <span className="ml-2 text-blue-700">{selectedActionForReview.submittedBy}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Due Date:</span>
                    <span className="ml-2 text-blue-700">
                      {selectedActionForReview.dueDate
                        ? format(new Date(selectedActionForReview.dueDate), 'MMM dd, yyyy')
                        : 'N/A'}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Reviewer Comments Section - Only show for verify_task */}
            {selectedActionForReview?.actionType === 'verify_task' && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 space-y-3">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  <h3 className="font-semibold text-blue-900">Reviewer Comments</h3>
                </div>
                <div className="bg-white border border-blue-100 rounded-md p-3">
                  {selectedActionForReview?.comments ? (
                    <p className="text-gray-700 whitespace-pre-wrap">{selectedActionForReview.comments}</p>
                  ) : (
                    <div>
                      <p className="text-gray-500 italic">No reviewer comments available</p>
                      <p className="text-xs text-gray-400 mt-2">Debug info: {JSON.stringify({
                        actionType: selectedActionForReview?.actionType,
                        comments: selectedActionForReview?.comments,
                        hasComments: !!selectedActionForReview?.comments
                      })}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Document Details Section */}
            <DocumentDetailsView
              document={reviewDocumentDetails}
              loading={loadingReviewDocument}
            />

            {/* Comments Section */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-gray-700">
                Comments <span className="text-red-500">*</span>
              </label>
              <textarea
                value={reviewComments}
                onChange={(e) => setReviewComments(e.target.value)}
                placeholder="Please provide your comments for this review decision..."
                className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                required
              />
              <p className="text-xs text-gray-500">
                Comments are required for both approval and rejection decisions.
              </p>
            </div>
          </div>

          {/* Modal Footer with Action Buttons */}
          <div className="flex justify-end items-center gap-3 pt-6 border-t bg-gray-50 -mx-6 -mb-6 px-6 py-4 rounded-b-lg">
            <Button
              variant="outline"
              onClick={() => {
                setIsDocumentReviewOpen(false);
                setSelectedActionForReview(null);
                setReviewComments('');
                setReviewAction(null);
                setReviewDocumentDetails(null);
              }}
              className="px-6"
            >
              Cancel
            </Button>
            <Button
              onClick={() => handleReviewAction('reject')}
              className="px-6 bg-red-600 hover:bg-red-700 text-white"
              disabled={!reviewComments.trim()}
            >
              Reject
            </Button>
            <Button
              onClick={() => handleReviewAction('approve')}
              className="px-6 bg-green-600 hover:bg-green-700 text-white"
              disabled={!reviewComments.trim()}
            >
              Approve
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* User Documents Modal */}
      <Dialog open={isUserDocumentsModalOpen} onOpenChange={setIsUserDocumentsModalOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle className="text-xl font-semibold text-gray-900">
                  📋 {selectedUserInfo?.firstName} {selectedUserInfo?.lastName}
                </DialogTitle>
                <p className="text-sm text-gray-600 mt-1">
                  Email: {selectedUserInfo?.email} 
                </p>
              </div>
              <Button
                className="bg-purple-600 hover:bg-purple-700 text-white"
                onClick={openAssignDocumentModal}
              >
                <Plus className="h-4 w-4 mr-2" />
                Assign Document
              </Button>
            </div>
          </DialogHeader>

          <div className="space-y-4">
            {loadingUserDocuments ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                  <p className="text-muted-foreground">Loading user documents...</p>
                </div>
              </div>
            ) : selectedUserDocuments.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Documents Assigned</h3>
                <p className="text-gray-500">This user has no documents assigned to them.</p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">
                    📄 Documents ({selectedUserDocuments.length})
                  </h3>
                </div>

                <ExpandableDataTable
                  data={selectedUserDocuments}
                  columns={[
                    {
                      key: 'maskId',
                      header: 'Document ID',
                      sortable: true,
                      width: 'w-[140px]',
                      render: (value, row) => (
                        <span
                          className="text-blue-600 hover:text-blue-800 font-medium cursor-pointer underline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDocumentClick(row);
                          }}
                          title="Click to view document details"
                        >
                          {value}
                        </span>
                      )
                    },
                    {
                      key: 'name',
                      header: 'Document Name',
                      sortable: true,
                      width: 'w-[200px]',
                      filterable: true,
                      filterType: 'text',
                      render: (value) => (
                        <span className="font-medium text-gray-900">
                          {value}
                        </span>
                      )
                    },
                    {
                      key: 'type',
                      header: 'Type',
                      sortable: true,
                      width: 'w-[100px]',
                      filterable: true,
                      filterType: 'text',
                      render: (value) => (
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                          {value}
                        </span>
                      )
                    },
                    {
                      key: 'status',
                      header: 'Status',
                      sortable: true,
                      width: 'w-[120px]',
                      filterable: true,
                      filterType: 'text',
                      render: (value, row) => {
                        const status = value || row.docStatus;
                        const getStatusColor = (status: any) => {
                          const statusStr = status ? String(status).toLowerCase() : '';
                          switch (statusStr) {
                            case 'approved':
                              return 'bg-green-100 text-green-800';
                            case 'in draft':
                              return 'bg-yellow-100 text-yellow-800';
                            case 'published':
                              return 'bg-blue-100 text-blue-800';
                            case 'pending':
                              return 'bg-orange-100 text-orange-800';
                            default:
                              return 'bg-gray-100 text-gray-800';
                          }
                        };

                        return (
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
                            {status}
                          </span>
                        );
                      }
                    },
                    {
                      key: 'created',
                      header: 'Created Date',
                      sortable: true,
                      width: 'w-[120px]',
                      render: (value) => {
                        try {
                          return new Date(value).toLocaleDateString();
                        } catch (error) {
                          return 'N/A';
                        }
                      }
                    },
                    // {
                    //   key: 'purpose',
                    //   header: 'Purpose',
                    //   width: 'w-[150px]',
                    //   render: (value) => (
                    //     <span className="text-gray-700 text-sm" title={value}>
                    //       {value ? (value.length > 30 ? `${value.substring(0, 30)}...` : value) : 'N/A'}
                    //     </span>
                    //   )
                    // },
                    // {
                    //   key: 'comments',
                    //   header: 'Comments',
                    //   width: 'w-[150px]',
                    //   render: (value) => (
                    //     value ? (
                    //       <span className="text-yellow-700 text-sm bg-yellow-50 px-2 py-1 rounded" title={value}>
                    //         {value.length > 20 ? `${value.substring(0, 20)}...` : value}
                    //       </span>
                    //     ) : (
                    //       <span className="text-gray-400 text-sm">No comments</span>
                    //     )
                    //   )
                    // },
                    {
                      key: 'actions',
                      header: 'Actions',
                      width: 'w-[100px]',
                      render: (value, row) => (
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-red-600 text-red-600 hover:bg-red-50"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleUnassignDocument(row.id, row.name);
                          }}
                        >
                          <X className="h-3 w-3 mr-1" />
                          Unassign
                        </Button>
                      )
                    }
                  ]}
                  highlightOnHover={true}
                  striped={true}
                />
              </div>
            )}
          </div>

          <div className="flex justify-end pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => {
                setIsUserDocumentsModalOpen(false);
                setSelectedUserDocuments([]);
                setSelectedUserInfo(null);
              }}
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Assign Document Modal */}
      {isAssignDocumentModalOpen && (
        <Dialog open={isAssignDocumentModalOpen} onOpenChange={setIsAssignDocumentModalOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-xl font-semibold text-gray-900">
                📋 Assign Document to {selectedUserInfo?.firstName} 
              </DialogTitle>
              <p className="text-sm text-gray-600 mt-1">
                Select approved documents to assign to this user. Already assigned documents are disabled.
              </p>
            </DialogHeader>

            {loadingAllDocuments ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-purple-600" />
                <span className="ml-2 text-gray-600">Loading documents...</span>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">
                    📄 Approved Documents ({allDocuments.length})
                  </h3>
                </div>

                <ExpandableDataTable
                  data={allDocuments}
                  columns={[
                    {
                      key: 'maskId',
                      header: 'Document ID',
                      sortable: true,
                      width: 'w-[140px]',
                      render: (value) => (
                        <span className="text-blue-600 hover:text-blue-800 font-medium">
                          {value}
                        </span>
                      )
                    },
                    {
                      key: 'name',
                      header: 'Document Name',
                      sortable: true,
                      width: 'w-[200px]',
                      filterable: true,
                      filterType: 'text',
                      render: (value) => (
                        <span className="font-medium text-gray-900">
                          {value}
                        </span>
                      )
                    },
                    {
                      key: 'type',
                      header: 'Type',
                      sortable: true,
                      width: 'w-[100px]',
                      filterable: true,
                      filterType: 'text',
                      render: (value) => (
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                          {value}
                        </span>
                      )
                    },
                    // {
                    //   key: 'status',
                    //   header: 'Status',
                    //   sortable: true,
                    //   width: 'w-[120px]',
                    //   filterable: true,
                    //   filterType: 'text',
                    //   render: (value, row) => {
                    //     const status = value || row.docStatus;
                    //     const getStatusColor = (status: any) => {
                    //       const statusStr = status ? String(status).toLowerCase() : '';
                    //       switch (statusStr) {
                    //         case 'approved':
                    //           return 'bg-green-100 text-green-800';
                    //         case 'in draft':
                    //           return 'bg-yellow-100 text-yellow-800';
                    //         case 'published':
                    //           return 'bg-blue-100 text-blue-800';
                    //         case 'pending':
                    //           return 'bg-orange-100 text-orange-800';
                    //         default:
                    //           return 'bg-gray-100 text-gray-800';
                    //       }
                    //     };

                    //     return (
                    //       <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
                    //         {status}
                    //       </span>
                    //     );
                    //   }
                    // },
                    {
                      key: 'created',
                      header: 'Created Date',
                      sortable: true,
                      width: 'w-[120px]',
                      render: (value) => {
                        try {
                          return new Date(value).toLocaleDateString();
                        } catch (error) {
                          return 'N/A';
                        }
                      }
                    },
                    // {
                    //   key: 'purpose',
                    //   header: 'Purpose',
                    //   width: 'w-[150px]',
                    //   render: (value) => (
                    //     <span className="text-gray-700 text-sm" title={value}>
                    //       {value ? (value.length > 30 ? `${value.substring(0, 30)}...` : value) : 'N/A'}
                    //     </span>
                    //   )
                    // },
                    {
                      key: 'actions',
                      header: 'Actions',
                      width: 'w-[120px]',
                      render: (value, row) => {
                        // Check if document is already assigned to this user
                        const isAlreadyAssigned = selectedUserDocuments.some(doc => doc.id === row.id);

                        return (
                          <Button
                            size="sm"
                            variant="outline"
                            className={
                              isAlreadyAssigned
                                ? "border-gray-300 text-gray-400 cursor-not-allowed"
                                : "border-green-600 text-green-600 hover:bg-green-50"
                            }
                            disabled={isAlreadyAssigned || assigningDocument}
                            onClick={(e) => {
                              e.stopPropagation();
                              if (!isAlreadyAssigned) {
                                handleAssignDocument(row.id, row.name);
                              }
                            }}
                          >
                            {assigningDocument ? (
                              <Loader2 className="h-3 w-3 animate-spin mr-1" />
                            ) : (
                              <Plus className="h-3 w-3 mr-1" />
                            )}
                            {isAlreadyAssigned ? 'Assigned' : 'Assign'}
                          </Button>
                        );
                      }
                    }
                  ]}
                  highlightOnHover={true}
                  striped={true}
                />
              </div>
            )}
          </DialogContent>
        </Dialog>
      )}


    </div>
  );
};

export default DocumentPage;