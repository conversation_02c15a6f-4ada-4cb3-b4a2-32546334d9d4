// Asset Tracking & Maintenance Types

export interface Asset {
  id: string;
  name: string;
  description: string;
  assetTag: string;
  category: AssetCategory;
  location: string;
  department: string;
  manufacturer: string;
  model: string;
  serialNumber: string;
  purchaseDate: string;
  warrantyExpiry?: string;
  status: AssetStatus;
  condition: AssetCondition;
  criticality: AssetCriticality;
  specifications: Record<string, any>;
  images: string[];
  qrCode?: string;
  created: string;
  updated: string;
  createdBy: string;
  updatedBy: string;
}

export interface AssetCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
}

export enum AssetStatus {
  ACTIVE = 'Active',
  INACTIVE = 'Inactive',
  MAINTENANCE = 'Under Maintenance',
  RETIRED = 'Retired',
  DISPOSED = 'Disposed'
}

export enum AssetCondition {
  EXCELLENT = 'Excellent',
  GOOD = 'Good',
  FAIR = 'Fair',
  POOR = 'Poor',
  CRITICAL = 'Critical'
}

export enum AssetCriticality {
  LOW = 'Low',
  MEDIUM = 'Medium',
  HIGH = 'High',
  CRITICAL = 'Critical'
}

// Maintenance Types
export interface MaintenanceRecord {
  id: string;
  assetId: string;
  asset?: Asset;
  type: MaintenanceType;
  title: string;
  description: string;
  scheduledDate: string;
  completedDate?: string;
  dueDate: string;
  status: MaintenanceStatus;
  priority: MaintenancePriority;
  assignedTo: string[];
  assignedUsers?: User[];
  estimatedDuration: number; // in hours
  actualDuration?: number; // in hours
  cost?: number;
  parts: MaintenancePart[];
  procedures: string[];
  notes: string;
  attachments: string[];
  created: string;
  updated: string;
  createdBy: string;
  updatedBy: string;
}

export interface MaintenancePart {
  id: string;
  name: string;
  partNumber: string;
  quantity: number;
  unitCost: number;
  supplier: string;
}

export enum MaintenanceType {
  PREVENTIVE = 'Preventive',
  CORRECTIVE = 'Corrective',
  PREDICTIVE = 'Predictive',
  EMERGENCY = 'Emergency',
  ROUTINE = 'Routine'
}

export enum MaintenanceStatus {
  SCHEDULED = 'Scheduled',
  IN_PROGRESS = 'In Progress',
  COMPLETED = 'Completed',
  CANCELLED = 'Cancelled',
  OVERDUE = 'Overdue'
}

export enum MaintenancePriority {
  LOW = 'Low',
  MEDIUM = 'Medium',
  HIGH = 'High',
  URGENT = 'Urgent'
}

// IoT and Monitoring Types
export interface IoTSensor {
  id: string;
  assetId: string;
  asset?: Asset;
  name: string;
  type: SensorType;
  location: string;
  status: SensorStatus;
  lastReading?: SensorReading;
  thresholds: SensorThreshold[];
  calibrationDate?: string;
  nextCalibrationDate?: string;
  created: string;
  updated: string;
}

export interface SensorReading {
  id: string;
  sensorId: string;
  value: number;
  unit: string;
  timestamp: string;
  quality: ReadingQuality;
  alerts?: SensorAlert[];
}

export interface SensorThreshold {
  id: string;
  name: string;
  minValue?: number;
  maxValue?: number;
  alertLevel: AlertLevel;
  enabled: boolean;
}

export interface SensorAlert {
  id: string;
  sensorId: string;
  thresholdId: string;
  message: string;
  level: AlertLevel;
  timestamp: string;
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: string;
}

export enum SensorType {
  PRESSURE = 'Pressure',
  TEMPERATURE = 'Temperature',
  VIBRATION = 'Vibration',
  FLOW = 'Flow',
  LEVEL = 'Level',
  HUMIDITY = 'Humidity',
  VOLTAGE = 'Voltage',
  CURRENT = 'Current'
}

export enum SensorStatus {
  ONLINE = 'Online',
  OFFLINE = 'Offline',
  ERROR = 'Error',
  MAINTENANCE = 'Maintenance'
}

export enum ReadingQuality {
  GOOD = 'Good',
  UNCERTAIN = 'Uncertain',
  BAD = 'Bad'
}

export enum AlertLevel {
  INFO = 'Info',
  WARNING = 'Warning',
  CRITICAL = 'Critical',
  EMERGENCY = 'Emergency'
}

// Calibration Types
export interface CalibrationRecord {
  id: string;
  assetId: string;
  asset?: Asset;
  sensorId?: string;
  sensor?: IoTSensor;
  title: string;
  description: string;
  calibrationDate: string;
  nextCalibrationDate: string;
  frequency: CalibrationFrequency;
  status: CalibrationStatus;
  performedBy: string;
  performedByUser?: User;
  certificateNumber?: string;
  standardUsed: string;
  results: CalibrationResult[];
  notes: string;
  attachments: string[];
  cost?: number;
  created: string;
  updated: string;
}

export interface CalibrationResult {
  parameter: string;
  expectedValue: number;
  actualValue: number;
  tolerance: number;
  unit: string;
  passed: boolean;
}

export enum CalibrationFrequency {
  MONTHLY = 'Monthly',
  QUARTERLY = 'Quarterly',
  SEMI_ANNUAL = 'Semi-Annual',
  ANNUAL = 'Annual',
  BIENNIAL = 'Biennial'
}

export enum CalibrationStatus {
  DUE = 'Due',
  OVERDUE = 'Overdue',
  COMPLETED = 'Completed',
  IN_PROGRESS = 'In Progress',
  CANCELLED = 'Cancelled'
}

// Downtime and Analytics Types
export interface DowntimeRecord {
  id: string;
  assetId: string;
  asset?: Asset;
  startTime: string;
  endTime?: string;
  duration?: number; // in minutes
  reason: DowntimeReason;
  category: DowntimeCategory;
  description: string;
  impact: DowntimeImpact;
  rootCause?: string;
  correctiveActions: string[];
  reportedBy: string;
  reportedByUser?: User;
  created: string;
  updated: string;
}

export enum DowntimeReason {
  PLANNED_MAINTENANCE = 'Planned Maintenance',
  UNPLANNED_MAINTENANCE = 'Unplanned Maintenance',
  EQUIPMENT_FAILURE = 'Equipment Failure',
  POWER_OUTAGE = 'Power Outage',
  MATERIAL_SHORTAGE = 'Material Shortage',
  OPERATOR_ERROR = 'Operator Error',
  QUALITY_ISSUE = 'Quality Issue',
  OTHER = 'Other'
}

export enum DowntimeCategory {
  MECHANICAL = 'Mechanical',
  ELECTRICAL = 'Electrical',
  HYDRAULIC = 'Hydraulic',
  PNEUMATIC = 'Pneumatic',
  SOFTWARE = 'Software',
  PROCESS = 'Process',
  EXTERNAL = 'External'
}

export enum DowntimeImpact {
  LOW = 'Low',
  MEDIUM = 'Medium',
  HIGH = 'High',
  CRITICAL = 'Critical'
}

// Dashboard and Analytics Types
export interface ATMDashboard {
  totalAssets: number;
  activeAssets: number;
  assetsUnderMaintenance: number;
  criticalAssets: number;
  overdueMaintenances: number;
  upcomingMaintenances: number;
  overdueCalibrations: number;
  upcomingCalibrations: number;
  activeSensors: number;
  sensorAlerts: number;
  totalDowntime: number; // in hours
  mtbf: number; // Mean Time Between Failures
  mttr: number; // Mean Time To Repair
  assetsByCategory: AssetCategoryStats[];
  maintenanceByType: MaintenanceTypeStats[];
  downtimeByReason: DowntimeReasonStats[];
  recentAlerts: SensorAlert[];
  upcomingTasks: MaintenanceRecord[];
}

export interface AssetCategoryStats {
  category: string;
  count: number;
  percentage: number;
}

export interface MaintenanceTypeStats {
  type: MaintenanceType;
  count: number;
  percentage: number;
}

export interface DowntimeReasonStats {
  reason: DowntimeReason;
  duration: number;
  percentage: number;
}

// User interface (assuming it exists in the system)
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  department?: string;
  role?: string;
}

// API Response Types
export interface ATMApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
  total?: number;
  page?: number;
  limit?: number;
}

// Filter and Search Types
export interface AssetFilter {
  category?: string;
  status?: AssetStatus;
  condition?: AssetCondition;
  location?: string;
  department?: string;
  search?: string;
}

export interface MaintenanceFilter {
  assetId?: string;
  type?: MaintenanceType;
  status?: MaintenanceStatus;
  priority?: MaintenancePriority;
  assignedTo?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

export interface CalibrationFilter {
  assetId?: string;
  status?: CalibrationStatus;
  frequency?: CalibrationFrequency;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}
