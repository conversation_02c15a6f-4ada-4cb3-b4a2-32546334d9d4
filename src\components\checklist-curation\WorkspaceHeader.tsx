import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Save, Smartphone } from "lucide-react";
import { DroppedItem } from "@/types/draggable";

interface WorkspaceHeaderProps {
  items: DroppedItem[];
  onSave: () => void;
  onPreview: () => void;
}

const WorkspaceHeader: React.FC<WorkspaceHeaderProps> = ({
  items,
  onSave,
  onPreview,
}) => {
  return (
    <div className="bg-white dark:bg-slate-800 border-b p-3 flex justify-between items-center sticky top-0 z-10 shadow-sm">
      <div className="text-sm text-muted-foreground">
        <span className="font-medium">{items.length}</span> components in workspace
      </div>
      <div className="flex gap-2">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={onSave}
          className="flex items-center gap-1 border-slate-200 hover:border-slate-300 hover:bg-slate-100"
        >
          <Save className="h-4 w-4" />
          Save
        </Button>
        <Button 
          variant="default" 
          size="sm" 
          onClick={onPreview}
          className="flex items-center gap-1"
          disabled={items.length === 0}
        >
          <Smartphone className="h-4 w-4" />
          Mobile Preview
        </Button>
      </div>
    </div>
  );
};

export default WorkspaceHeader;
