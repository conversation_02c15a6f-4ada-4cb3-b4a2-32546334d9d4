import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import apiService from "@/services/apiService";
import { useToast } from "@/components/ui/use-toast";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { Loader2 } from "lucide-react";


interface LocationItem {
  id: string;
  name: string;
}

interface LocationTitles {
  tier1: string;
  tier2: string;
  tier3: string;
  tier4: string;
  tier5: string;
  tier6: string;
}

interface DynamicTitle {
  title: string;
  altTitle: string;
}

interface AllFilterLocationProps {
  handleFilter: (
    locationOneId: string,
    locationTwoId?: string,
    locationThreeId?: string,
    locationFourId?: string,
    locationFiveId?: string,
    locationSixId?: string
  ) => void;
  getLocation: {
    locationOneId: string;
    locationTwoId: string;
    locationThreeId: string;
    locationFourId: string;
    locationFiveId: string;
    locationSixId: string;
  };
}

const AllFilterLocation: React.FC<AllFilterLocationProps> = (props) => {
    const { toast } = useToast();
    // Get access token from Redux store
    const { accessToken } = useSelector((state: RootState) => state.auth);

    const [locationOne, setLocationOne] = useState<LocationItem[]>([]);
    const [locationTwo, setLocationTwo] = useState<LocationItem[]>([]);
    const [locationThree, setLocationThree] = useState<LocationItem[]>([]);
    const [locationFour, setLocationFour] = useState<LocationItem[]>([]);
    const [locationFive, setLocationFive] = useState<LocationItem[]>([]);
    const [locationSix, setLocationSix] = useState<LocationItem[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    // State for dynamic location titles
    const [titles, setTitles] = useState<LocationTitles>({
        tier1: 'Tier I',
        tier2: 'Tier II',
        tier3: 'Tier III',
        tier4: 'Tier IV',
        tier5: 'Tier V',
        tier6: 'Tier VI',
    });

    const [selectedLocationOne, setSelectedLocationOne] = useState('');
    const [selectedLocationTwo, setSelectedLocationTwo] = useState('');
    const [selectedLocationThree, setSelectedLocationThree] = useState('');
    const [selectedLocationFour, setSelectedLocationFour] = useState('');
    const [selectedLocationFive, setSelectedLocationFive] = useState('');
    const [selectedLocationSix, setSelectedLocationSix] = useState('');

    const prevSelectedLocationOne = useRef('');
    const prevSelectedLocationTwo = useRef('');
    const prevSelectedLocationThree = useRef('');
    const prevSelectedLocationFour = useRef('');
    const prevSelectedLocationFive = useRef('');
    const prevSelectedLocationSix = useRef('');

    // Helper function to handle API errors - wrapped in useCallback to prevent dependency changes
    const handleApiError = useCallback((error: unknown) => {
        if (error && typeof error === 'object' && 'response' in error) {
            const errorWithResponse = error as {
                response?: {
                    status: number;
                    data?: { message?: string }
                };
                request?: unknown;
                message?: string;
            };

            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            if (errorWithResponse.response) {
                if (errorWithResponse.response.status === 401) {
                    toast({
                        title: "Authentication Error",
                        description: "Your session has expired. Please log in again.",
                        variant: "destructive"
                    });
                } else {
                    toast({
                        title: "API Error",
                        description: `Error ${errorWithResponse.response.status}: ${errorWithResponse.response.data?.message || 'Unknown error'}`,
                        variant: "destructive"
                    });
                }
            } else if (errorWithResponse.request) {
                // The request was made but no response was received
                toast({
                    title: "Network Error",
                    description: "Could not connect to the server. Please check your internet connection.",
                    variant: "destructive"
                });
            } else if (errorWithResponse.message) {
                // Something happened in setting up the request that triggered an Error
                toast({
                    title: "Error",
                    description: errorWithResponse.message || "An unknown error occurred",
                    variant: "destructive"
                });
            }
        } else {
            // Fallback for unexpected error types
            toast({
                title: "Error",
                description: "An unknown error occurred",
                variant: "destructive"
            });
        }
    }, [toast]);

    // Check if token exists
    useEffect(() => {
        if (!accessToken) {
            console.warn('No access token found in Redux store');
            toast({
                title: "Authentication Warning",
                description: "No access token found. You may need to log in again.",
                variant: "destructive"
            });
        }
    }, [accessToken, toast]);

    // Fetch dynamic location titles
    useEffect(() => {
        const getLocationConfigs = async () => {
            if (!accessToken) {
                console.warn('Cannot fetch location titles: No access token available');
                return;
            }

            try {
                const data = await apiService.get('/dynamic-titles');

                if (data && data.length > 0) {
                    const titles = data as DynamicTitle[];
                    const locationsObject = titles.reduce<Record<string, string>>((obj, item) => {
                        obj[item.title] = item.altTitle;
                        return obj;
                    }, {});
                    setTitles({
                        tier1: locationsObject.LocationOne || 'Tier I',
                        tier2: locationsObject.LocationTwo || 'Tier II',
                        tier3: locationsObject.LocationThree || 'Tier III',
                        tier4: locationsObject.LocationFour || 'Tier IV',
                        tier5: locationsObject.LocationFive || 'Tier V',
                        tier6: locationsObject.LocationSix || 'Tier VI',
                    });
                } else {
                    setTitles({
                        tier1: 'Tier I',
                        tier2: 'Tier II',
                        tier3: 'Tier III',
                        tier4: 'Tier IV',
                        tier5: 'Tier V',
                        tier6: 'Tier VI',
                    });
                }
            } catch (error) {
                console.error("Error fetching location configs:", error);
                handleApiError(error);
                setTitles({
                    tier1: 'Tier I',
                    tier2: 'Tier II',
                    tier3: 'Tier III',
                    tier4: 'Tier IV',
                    tier5: 'Tier V',
                    tier6: 'Tier VI',
                });
            }
        };

        getLocationConfigs();
    }, [accessToken, handleApiError]);

    useEffect(() => {
        const populateLocations = async () => {
            if (!props.getLocation || Object.keys(props.getLocation).length === 0) return;
            if (!accessToken) {
                console.warn('Cannot fetch locations: No access token available');
                return;
            }

            setIsLoading(true);
            const { locationOneId, locationTwoId, locationThreeId, locationFourId, locationFiveId, locationSixId } = props.getLocation;

            if (locationOneId) {
                setSelectedLocationOne(locationOneId);
                try {
                    const twoRes = await apiService.get(`/location-ones/${locationOneId}/location-twos`);
                    setLocationTwo(twoRes);
                } catch (error) {
                    console.error('Error fetching location two:', error);
                    handleApiError(error);
                }
            }

            if (locationTwoId) {
                setSelectedLocationTwo(locationTwoId);
                try {
                    const threeRes = await apiService.get(`/location-twos/${locationTwoId}/location-threes`);
                    setLocationThree(threeRes);
                } catch (error) {
                    console.error('Error fetching location three:', error);
                    handleApiError(error);
                }
            }

            if (locationThreeId) {
                setSelectedLocationThree(locationThreeId);
                try {
                    const fourRes = await apiService.get(`/location-threes/${locationThreeId}/location-fours`);
                    setLocationFour(fourRes);
                } catch (error) {
                    console.error('Error fetching location four:', error);
                    handleApiError(error);
                }
            }

            if (locationFourId) {
                setSelectedLocationFour(locationFourId);
                try {
                    const fiveRes = await apiService.get(`/location-fours/${locationFourId}/location-fives`);
                    setLocationFive(fiveRes);
                } catch (error) {
                    console.error('Error fetching location five:', error);
                    handleApiError(error);
                }
            }

            if (locationFiveId) {
                setSelectedLocationFive(locationFiveId);
                try {
                    const sixRes = await apiService.get(`/location-fives/${locationFiveId}/location-sixes`);
                    setLocationSix(sixRes);
                } catch (error) {
                    console.error('Error fetching location six:', error);
                    handleApiError(error);
                }
            }

            if (locationSixId) {
                setSelectedLocationSix(locationSixId);
            }

            setIsLoading(false);
        };

        populateLocations();
    }, [props.getLocation, handleApiError, accessToken]);

    useEffect(() => {
        const fetchLocationOne = async () => {
            if (!accessToken) {
                console.warn('Cannot fetch location one: No access token available');
                return;
            }

            setIsLoading(true);
            try {
                const data = await apiService.get('/location-ones');
                setLocationOne(data);

                // Auto-select if only one option is available
                if (data.length === 1 && !selectedLocationOne) {
                    setSelectedLocationOne(data[0].id);
                }
            } catch (error) {
                console.error('Error fetching location one:', error);
                handleApiError(error);
            } finally {
                setIsLoading(false);
            }
        };
        fetchLocationOne();
    }, [handleApiError, accessToken, selectedLocationOne]);

    useEffect(() => {
        if (!selectedLocationOne || selectedLocationOne === prevSelectedLocationOne.current) return;
        prevSelectedLocationOne.current = selectedLocationOne;

        const fetchLocationTwo = async () => {
            if (!accessToken) {
                console.warn('Cannot fetch location two: No access token available');
                return;
            }

            setIsLoading(true);
            try {
                const data = await apiService.get(`/location-ones/${selectedLocationOne}/location-twos`);
                setLocationTwo(data);
                // Reset lower-level selections
                setSelectedLocationTwo('');
                setSelectedLocationThree('');
                setSelectedLocationFour('');
                setSelectedLocationFive('');
                setSelectedLocationSix('');
                setLocationThree([]);
                setLocationFour([]);
                setLocationFive([]);
                setLocationSix([]);

                // Auto-select if only one option is available
                if (data.length === 1) {
                    setSelectedLocationTwo(data[0].id);
                }
            } catch (error) {
                console.error('Error fetching location two:', error);
                handleApiError(error);
            } finally {
                setIsLoading(false);
            }
        };
        fetchLocationTwo();
        // Call handleFilter with updated selections
        props.handleFilter(selectedLocationOne, '', '', '', '', '');
    }, [selectedLocationOne, props, handleApiError, accessToken]);

    useEffect(() => {
        if (!selectedLocationTwo || selectedLocationTwo === prevSelectedLocationTwo.current) return;
        prevSelectedLocationTwo.current = selectedLocationTwo;

        const fetchLocationThree = async () => {
            if (!accessToken) {
                console.warn('Cannot fetch location three: No access token available');
                return;
            }

            setIsLoading(true);
            try {
                const data = await apiService.get(`/location-twos/${selectedLocationTwo}/location-threes`);
                setLocationThree(data);
                // Reset lower-level selections
                setSelectedLocationThree('');
                setSelectedLocationFour('');
                setSelectedLocationFive('');
                setSelectedLocationSix('');
                setLocationFour([]);
                setLocationFive([]);
                setLocationSix([]);

                // Auto-select if only one option is available
                if (data.length === 1) {
                    setSelectedLocationThree(data[0].id);
                }
            } catch (error) {
                console.error('Error fetching location three:', error);
                handleApiError(error);
            } finally {
                setIsLoading(false);
            }
        };
        fetchLocationThree();
        // Call handleFilter with updated selections
        props.handleFilter(selectedLocationOne, selectedLocationTwo, '', '', '', '');
    }, [selectedLocationTwo, selectedLocationOne, props, handleApiError, accessToken]);

    useEffect(() => {
        if (!selectedLocationThree || selectedLocationThree === prevSelectedLocationThree.current) return;
        prevSelectedLocationThree.current = selectedLocationThree;

        const fetchLocationFour = async () => {
            if (!accessToken) {
                console.warn('Cannot fetch location four: No access token available');
                return;
            }

            setIsLoading(true);
            try {
                const data = await apiService.get(`/location-threes/${selectedLocationThree}/location-fours`);
                setLocationFour(data);
                // Reset lower-level selections
                setSelectedLocationFour('');
                setSelectedLocationFive('');
                setSelectedLocationSix('');
                setLocationFive([]);
                setLocationSix([]);

                // Auto-select if only one option is available
                if (data.length === 1) {
                    setSelectedLocationFour(data[0].id);
                }
            } catch (error) {
                console.error('Error fetching location four:', error);
                handleApiError(error);
            } finally {
                setIsLoading(false);
            }
        };
        fetchLocationFour();
        // Call handleFilter with updated selections
        props.handleFilter(selectedLocationOne, selectedLocationTwo, selectedLocationThree, '', '', '');
    }, [selectedLocationThree, selectedLocationOne, selectedLocationTwo, props, handleApiError, accessToken]);

    useEffect(() => {
        if (!selectedLocationFour || selectedLocationFour === prevSelectedLocationFour.current) return;
        prevSelectedLocationFour.current = selectedLocationFour;

        const fetchLocationFive = async () => {
            if (!accessToken) {
                console.warn('Cannot fetch location five: No access token available');
                return;
            }

            setIsLoading(true);
            try {
                const data = await apiService.get(`/location-fours/${selectedLocationFour}/location-fives`);
                setLocationFive(data);
                // Reset lower-level selections
                setSelectedLocationFive('');
                setSelectedLocationSix('');
                setLocationSix([]);

                // Auto-select if only one option is available
                if (data.length === 1) {
                    setSelectedLocationFive(data[0].id);
                }
            } catch (error) {
                console.error('Error fetching location five:', error);
                handleApiError(error);
            } finally {
                setIsLoading(false);
            }
        };
        fetchLocationFive();
        // Call handleFilter with updated selections
        props.handleFilter(selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, '', '');
    }, [selectedLocationFour, selectedLocationOne, selectedLocationTwo, selectedLocationThree, props, handleApiError, accessToken]);

    useEffect(() => {
        if (!selectedLocationFive || selectedLocationFive === prevSelectedLocationFive.current) return;
        prevSelectedLocationFive.current = selectedLocationFive;

        const fetchLocationSix = async () => {
            if (!accessToken) {
                console.warn('Cannot fetch location six: No access token available');
                return;
            }

            setIsLoading(true);
            try {
                const data = await apiService.get(`/location-fives/${selectedLocationFive}/location-sixes`);
                setLocationSix(data);
                // Reset lower-level selections
                setSelectedLocationSix('');

                // Auto-select if only one option is available
                if (data.length === 1) {
                    setSelectedLocationSix(data[0].id);
                }
            } catch (error) {
                console.error('Error fetching location six:', error);
                handleApiError(error);
            } finally {
                setIsLoading(false);
            }
        };
        fetchLocationSix();
        // Call handleFilter with updated selections
        props.handleFilter(selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, selectedLocationFive, '');
    }, [selectedLocationFive, selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, props, handleApiError, accessToken]);

    useEffect(() => {
        if (!selectedLocationSix || selectedLocationSix === prevSelectedLocationSix.current) return;
        prevSelectedLocationSix.current = selectedLocationSix;

        // Call handleFilter with updated selections
        props.handleFilter(
            selectedLocationOne,
            selectedLocationTwo,
            selectedLocationThree,
            selectedLocationFour,
            selectedLocationFive,
            selectedLocationSix
        );
    }, [selectedLocationSix, selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, selectedLocationFive, props]);

    // Function to handle location selection change
    const handleLocationOneChange = (value: string) => {
        setSelectedLocationOne(value);
    };

    const handleLocationTwoChange = (value: string) => {
        setSelectedLocationTwo(value);
    };

    const handleLocationThreeChange = (value: string) => {
        setSelectedLocationThree(value);
    };

    const handleLocationFourChange = (value: string) => {
        setSelectedLocationFour(value);
    };

    const handleLocationFiveChange = (value: string) => {
        setSelectedLocationFive(value);
    };

    const handleLocationSixChange = (value: string) => {
        setSelectedLocationSix(value);
    };

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 relative">
            {isLoading && (
                <div className="absolute inset-0 bg-white/50 flex items-center justify-center z-10">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
            )}
            <div className="space-y-2">
                <label className="text-sm font-medium">{titles.tier1}</label>
                <Select
                    value={selectedLocationOne}
                    onValueChange={handleLocationOneChange}
                    disabled={isLoading}
                >
                    <SelectTrigger className="w-full">
                        <SelectValue placeholder={`Select ${titles.tier1}`} />
                    </SelectTrigger>
                    <SelectContent>
                        {locationOne.map((location) => (
                            <SelectItem key={location.id} value={location.id}>
                                {location.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>

            {selectedLocationOne && locationTwo.length > 0 && (
                <div className="space-y-2">
                    <label className="text-sm font-medium">{titles.tier2}</label>
                    <Select
                        value={selectedLocationTwo}
                        onValueChange={handleLocationTwoChange}
                        disabled={isLoading}
                    >
                        <SelectTrigger className="w-full">
                            <SelectValue placeholder={`Select ${titles.tier2}`} />
                        </SelectTrigger>
                        <SelectContent>
                            {locationTwo.map((location) => (
                                <SelectItem key={location.id} value={location.id}>
                                    {location.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            )}

            {selectedLocationTwo && locationThree.length > 0 && (
                <div className="space-y-2">
                    <label className="text-sm font-medium">{titles.tier3}</label>
                    <Select
                        value={selectedLocationThree}
                        onValueChange={handleLocationThreeChange}
                        disabled={isLoading}
                    >
                        <SelectTrigger className="w-full">
                            <SelectValue placeholder={`Select ${titles.tier3}`} />
                        </SelectTrigger>
                        <SelectContent>
                            {locationThree.map((location) => (
                                <SelectItem key={location.id} value={location.id}>
                                    {location.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            )}

            {selectedLocationThree && locationFour.length > 0 && (
                <div className="space-y-2">
                    <label className="text-sm font-medium">{titles.tier4}</label>
                    <Select
                        value={selectedLocationFour}
                        onValueChange={handleLocationFourChange}
                        disabled={isLoading}
                    >
                        <SelectTrigger className="w-full">
                            <SelectValue placeholder={`Select ${titles.tier4}`} />
                        </SelectTrigger>
                        <SelectContent>
                            {locationFour.map((location) => (
                                <SelectItem key={location.id} value={location.id}>
                                    {location.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            )}

            {selectedLocationFour && locationFive.length > 0 && (
                <div className="space-y-2">
                    <label className="text-sm font-medium">{titles.tier5}</label>
                    <Select
                        value={selectedLocationFive}
                        onValueChange={handleLocationFiveChange}
                        disabled={isLoading}
                    >
                        <SelectTrigger className="w-full">
                            <SelectValue placeholder={`Select ${titles.tier5}`} />
                        </SelectTrigger>
                        <SelectContent>
                            {locationFive.map((location) => (
                                <SelectItem key={location.id} value={location.id}>
                                    {location.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            )}

            {selectedLocationFive && locationSix.length > 0 && (
                <div className="space-y-2">
                    <label className="text-sm font-medium">{titles.tier6}</label>
                    <Select
                        value={selectedLocationSix}
                        onValueChange={handleLocationSixChange}
                        disabled={isLoading}
                    >
                        <SelectTrigger className="w-full">
                            <SelectValue placeholder={`Select ${titles.tier6}`} />
                        </SelectTrigger>
                        <SelectContent>
                            {locationSix.map((location) => (
                                <SelectItem key={location.id} value={location.id}>
                                    {location.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            )}
        </div>
    );
};

export default AllFilterLocation;
