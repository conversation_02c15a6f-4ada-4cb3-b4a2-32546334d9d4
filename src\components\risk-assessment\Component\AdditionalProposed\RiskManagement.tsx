import React, { useState } from 'react';
import ProposedAdditionalControls from '@/components/risk-assessment/Component/AdditionalProposed/ProposedAdditionalControls';
import ResidualRiskAssessment from '@/components/risk-assessment/Component/AdditionalProposed/ResidualRiskAssessment';

interface ControlOption {
  value: string;
  label: string;
}

interface ResponsibilityOption {
  id: string;
  name: string;
  firstName?: string;
}

interface SeverityOption {
  value: string;
  label: string;
}

interface LikelihoodOption {
  value: string;
  label: string;
}

interface SeverityData {
  id: string;
  severity: string;
  personnel: string;
  property: string;
  environment: string;
  serviceLoss: string;
}

interface LevelData {
  level: string;
  descriptor: string;
  detailedDescription: string;
}

interface TableData {
  id: string;
  severity: string;
  rare: string;
  unlikely: string;
  possible: string;
  likely: string;
  almostCertain: string;
}

interface TaskItem {
  [key: number]: any;
}

interface ProposedRiskManagementProps {
  item: TaskItem[];
  control: ControlOption[];
  responsibility: ResponsibilityOption[];
  severity: SeverityOption[];
  severityData: SeverityData[];
  likelyhood: LikelihoodOption[];
  levelData: LevelData[];
  tableData: TableData[];
  required: boolean;
  onControlAddion: (value: string, index: number) => void;
  onControlAddionText: (value: string, index: number) => void;
  onDeleteConseq: (index: number, type: string) => void;
  onResponsePerson: (person: ResponsibilityOption, index: number) => void;
  onResponseDate: (date: Date | null, index: number) => void;
  addAdditionalControl: () => void;
  onChangeSeverity: (option: { value: string; label: string }, type: string) => void;
  onChangeLikelyhood: (option: { value: string; label: string }, type: string) => void;
  cellClassName: (value: number) => string;
  cellStyle: (data: any, field: string) => string;
  rowClassName: (data: any) => string;
}

const ProposedRiskManagement: React.FC<ProposedRiskManagementProps> = ({
  item,
  control,
  responsibility,
  severity,
  severityData,
  likelyhood,
  levelData,
  tableData,
  required,
  onControlAddion,
  onControlAddionText,
  onDeleteConseq,
  onResponsePerson,
  onResponseDate,
  addAdditionalControl,
  onChangeSeverity,
  onChangeLikelyhood,
  cellClassName,
  cellStyle,
  rowClassName
}) => {
  const [severityTable, setSeverityTable] = useState<boolean>(false);
  const [likelyhoodTable, setLikelyhoodTable] = useState<boolean>(false);
  const [riskTable, setRiskTable] = useState<boolean>(false);

  return (
    <div className="space-y-8">
      <ProposedAdditionalControls
        item={item}
        control={control}
        responsibility={responsibility}
        onControlAddion={onControlAddion}
        onControlAddionText={onControlAddionText}
        onDeleteConseq={onDeleteConseq}
        onResponsePerson={onResponsePerson}
        onResponseDate={onResponseDate}
        addAdditionalControl={addAdditionalControl}
        required={required}
      />

      <ResidualRiskAssessment
        item={item}
        severity={severity}
        severityData={severityData}
        likelyhood={likelyhood}
        levelData={levelData}
        tableData={tableData}
        severityTable={severityTable}
        likelyhoodTable={likelyhoodTable}
        riskTable={riskTable}
        setSeverityTable={setSeverityTable}
        setLikelyhoodTable={setLikelyhoodTable}
        setRiskTable={setRiskTable}
        onChangeSeverity={onChangeSeverity}
        onChangeLikelyhood={onChangeLikelyhood}
        cellClassName={cellClassName}
        cellStyle={cellStyle}
        rowClassName={rowClassName}
        required={required}
      />
    </div>
  );
};

export default ProposedRiskManagement;
