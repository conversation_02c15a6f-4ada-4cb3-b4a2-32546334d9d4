import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, Target, Zap, Droplets, Users, MoreHorizontal, Download, Share2, Maximize2, RefreshCw, X } from "lucide-react";
import { useState, useMemo, useRef } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import * as XLSX from 'xlsx';

interface ChartGridProps {
  activeView: string;
  selectedMetric: string;
}

const ChartGrid = ({ activeView, selectedMetric }: ChartGridProps) => {
  // Sample data for different chart types
  const monthlyData = [
    { month: 'Jan', emissions: 230, energy: 1200, water: 1600, satisfaction: 8.2 },
    { month: 'Feb', emissions: 215, energy: 1150, water: 1580, satisfaction: 8.3 },
    { month: 'Mar', emissions: 235, energy: 1250, water: 1560, satisfaction: 8.0 },
    { month: 'Apr', emissions: 205, energy: 1100, water: 1550, satisfaction: 8.1 },
    { month: 'May', emissions: 222, energy: 1180, water: 1540, satisfaction: 8.4 },
    { month: 'Jun', emissions: 195, energy: 1050, water: 1530, satisfaction: 8.2 }
  ];

  const performanceData = [
    { subject: 'Emissions', current: 85, target: 100, industry: 70 },
    { subject: 'Energy', current: 92, target: 100, industry: 75 },
    { subject: 'Water', current: 78, target: 100, industry: 65 },
    { subject: 'Waste', current: 88, target: 100, industry: 72 },
    { subject: 'Social', current: 95, target: 100, industry: 80 },
    { subject: 'Governance', current: 97, target: 100, industry: 85 }
  ];

  const targetData = [
    { category: 'Q1', achieved: 85, target: 90 },
    { category: 'Q2', achieved: 92, target: 90 },
    { category: 'Q3', achieved: 88, target: 90 },
    { category: 'Q4', achieved: 95, target: 90 }
  ];

  const today = new Date();
  const formatDate = (date: Date) => {
    const day = date.getDate();
    const month = date.toLocaleString('default', { month: 'short' });
    const year = date.getFullYear().toString().slice(-2);
    let suffix = 'th';
    if (day % 10 === 1 && day !== 11) suffix = 'st';
    else if (day % 10 === 2 && day !== 12) suffix = 'nd';
    else if (day % 10 === 3 && day !== 13) suffix = 'rd';
    return `${day}${suffix} ${month} ${year}`;
  };
  const asOfDate = `(as of ${formatDate(today)})`;

  // Period selector state
  const [selectedPeriod, setSelectedPeriod] = useState<'3M' | '6M' | '1Y' | 'Max'>('1Y');
  const periodOptions = ['3M', '6M', '1Y', 'Max'];

  // Full screen modal state
  const [fullScreenChart, setFullScreenChart] = useState<{
    isOpen: boolean;
    title: string;
    chart: React.ReactNode;
    data: any[];
  }>({
    isOpen: false,
    title: '',
    chart: null,
    data: []
  });

  // Chart refs for export functionality
  const chartRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Test function to see if clicks work
  const testFunction = (chartIndex: number, action: string) => {
    console.log(`Test function called: ${action} for chart ${chartIndex}`);
    alert(`${action} clicked for chart ${chartIndex + 1}!`);
  };

  // Refresh data function
  const refreshChartData = (chartIndex: number) => {
    // Simulate data refresh
    console.log(`Refreshing data for chart ${chartIndex}`);
    // In a real application, this would trigger a data fetch
    // For now, we'll just show a success message
    alert(`Chart ${chartIndex + 1} data refreshed successfully!`);
  };

  // Export chart as PNG
  const exportChartAsPNG = async (chartIndex: number, title: string) => {
    const chartElement = chartRefs.current[chartIndex];
    if (!chartElement) return;

    try {
      const canvas = await html2canvas(chartElement, {
        scale: 2,
        backgroundColor: '#ffffff',
        logging: false,
        useCORS: true,
      });

      const link = document.createElement('a');
      link.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_chart.png`;
      link.href = canvas.toDataURL();
      link.click();
    } catch (error) {
      console.error('Error exporting PNG:', error);
      alert('Failed to export chart as PNG');
    }
  };

  // Export chart as PDF
  const exportChartAsPDF = async (chartIndex: number, title: string) => {
    const chartElement = chartRefs.current[chartIndex];
    if (!chartElement) return;

    try {
      const canvas = await html2canvas(chartElement, {
        scale: 2,
        backgroundColor: '#ffffff',
        logging: false,
        useCORS: true,
      });

      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
      });

      const imgData = canvas.toDataURL('image/png');
      const imgWidth = 297; // A4 landscape width in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
      pdf.save(`${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_chart.pdf`);
    } catch (error) {
      console.error('Error exporting PDF:', error);
      alert('Failed to export chart as PDF');
    }
  };

  // Export chart data as CSV
  const exportChartDataAsCSV = (chartIndex: number, title: string, data: any[]) => {
    try {
      const headers = Object.keys(data[0] || {});
      const csvContent = [
        [`Chart Data - ${title}`],
        [`Generated: ${new Date().toLocaleDateString()}`],
        [],
        headers,
        ...data.map(row => headers.map(header => row[header]))
      ].map(row => row.join(',')).join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_data.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error exporting CSV:', error);
      alert('Failed to export chart data as CSV');
    }
  };

  // Share chart function
  const shareChart = async (chartIndex: number, title: string) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Chart: ${title}`,
          text: `Check out this chart: ${title}`,
          url: window.location.href
        });
      } catch (error) {
        console.error('Error sharing:', error);
        copyChartLink(title);
      }
    } else {
      copyChartLink(title);
    }
  };

  // Copy chart link to clipboard
  const copyChartLink = (title: string) => {
    navigator.clipboard.writeText(window.location.href).then(() => {
      alert(`Link to ${title} copied to clipboard!`);
    }).catch(() => {
      alert('Failed to copy link to clipboard');
    });
  };

  // Open chart in full screen
  const openFullScreen = (chartIndex: number, title: string, chart: React.ReactNode, data: any[]) => {
    setFullScreenChart({
      isOpen: true,
      title,
      chart,
      data
    });
  };

  // Helper to get months for the period
  const getMonthsForPeriod = (data, period) => {
    if (period === 'Max') return data;
    const count = period === '3M' ? 3 : period === '6M' ? 6 : 12;
    return data.slice(-count);
  };

  // Filtered data for charts
  const filteredMonthlyData = useMemo(() => getMonthsForPeriod(monthlyData, selectedPeriod), [monthlyData, selectedPeriod]);
  const filteredTargetData = useMemo(() => {
    if (selectedPeriod === 'Max' || selectedPeriod === '1Y') return targetData;
    if (selectedPeriod === '6M') return targetData.slice(-2); // Show last 2 quarters
    if (selectedPeriod === '3M') return targetData.slice(-1); // Show last quarter
    return targetData;
  }, [targetData, selectedPeriod]);

  const chartCards = [
    {
      title: `Monthly Trends ${asOfDate}`,
      subtitle: 'Monthly performance over time (Real-time)',
      icon: TrendingUp,
      color: "bg-blue-500",
      data: filteredMonthlyData,
      chart: (
        <ResponsiveContainer width="100%" height={200}>
          <AreaChart data={filteredMonthlyData}>
            <defs>
              <linearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
            <XAxis dataKey="month" stroke="#64748b" fontSize={10} />
            <YAxis stroke="#64748b" fontSize={10} label={{ value: 'Count', position: 'insideTopLeft', offset: -5 }} />
            <Tooltip
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                fontSize: '12px'
              }}
            />
            <Area
              type="monotone"
              dataKey={selectedMetric === 'emissions' ? 'emissions' : 'energy'}
              stroke="#3b82f6"
              fill="url(#areaGradient)"
            />
          </AreaChart>
        </ResponsiveContainer>
      )
    },
    {
      title: `Monthly Performance Radar ${asOfDate}`,
      subtitle: 'Monthly multi-dimensional analysis (Real-time)',
      icon: Target,
      color: "bg-green-500",
      data: performanceData,
      chart: (
        <ResponsiveContainer width="100%" height={200}>
          <RadarChart data={performanceData}>
            <PolarGrid stroke="#e5e7eb" />
            <PolarAngleAxis dataKey="subject" tick={{ fontSize: 10 }} />
            <PolarRadiusAxis angle={90} domain={[0, 100]} tick={{ fontSize: 8 }} />
            <Radar
              name="Current"
              dataKey="current"
              stroke="#10b981"
              fill="#10b981"
              fillOpacity={0.3}
              strokeWidth={2}
            />
            <Radar
              name="Target"
              dataKey="target"
              stroke="#6b7280"
              fill="transparent"
              strokeDasharray="5 5"
            />
            <Tooltip />
          </RadarChart>
        </ResponsiveContainer>
      )
    },
    {
      title: `Monthly Target Achievement ${asOfDate}`,
      subtitle: 'Monthly quarterly performance (Real-time)',
      icon: Zap,
      color: "bg-purple-500",
      data: filteredTargetData,
      chart: (
        <ResponsiveContainer width="100%" height={200}>
          <BarChart data={filteredTargetData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
            <XAxis dataKey="category" stroke="#64748b" fontSize={10} />
            <YAxis stroke="#64748b" fontSize={10} label={{ value: 'Count', position: 'insideTopLeft', offset: -5 }} />
            <Tooltip
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                fontSize: '12px'
              }}
            />
            <Bar dataKey="achieved" fill="#8b5cf6" radius={[4, 4, 0, 0]} />
            <Bar dataKey="target" fill="#e5e7eb" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      )
    },
    {
      title: `Monthly Efficiency Metrics ${asOfDate}`,
      subtitle: 'Monthly resource optimization (Real-time)',
      icon: Droplets,
      color: "bg-cyan-500",
      data: filteredMonthlyData,
      chart: (
        <ResponsiveContainer width="100%" height={200}>
          <LineChart data={filteredMonthlyData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
            <XAxis dataKey="month" stroke="#64748b" fontSize={10} />
            <YAxis stroke="#64748b" fontSize={10} label={{ value: 'Count', position: 'insideTopLeft', offset: -5 }} />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'white', 
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                fontSize: '12px'
              }}
            />
            <Line 
              type="monotone" 
              dataKey="water" 
              stroke="#06b6d4" 
              strokeWidth={3}
              dot={{ fill: '#06b6d4', strokeWidth: 2, r: 4 }}
            />
            <Line 
              type="monotone" 
              dataKey="energy" 
              stroke="#f59e0b" 
              strokeWidth={2}
              strokeDasharray="5 5"
              dot={{ fill: '#f59e0b', strokeWidth: 2, r: 3 }}
            />
          </LineChart>
        </ResponsiveContainer>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold text-gray-900">
            Interactive Analytics
          </h3>
          <p className="text-gray-600">
            Comprehensive view of {activeView} metrics with real-time insights
          </p>
        </div>
        <Badge variant="outline" className="bg-white/50 text-gray-700">
          {chartCards.length} Active Charts
        </Badge>
      </div>

      {/* Period Selector */}
      <div className="flex gap-2 mb-2">
        {periodOptions.map((period) => (
          <button
            key={period}
            onClick={() => setSelectedPeriod(period as any)}
            className={`px-4 py-1.5 rounded-md text-sm font-medium border transition-all duration-200 ${selectedPeriod === period ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-blue-50'}`}
          >
            {period}
          </button>
        ))}
      </div>

      {/* Chart Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {chartCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <Card
              key={index}
              className="p-6 bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              {/* Card Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${card.color} text-white`}>
                    <Icon className="w-5 h-5" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{card.title}</h4>
                    <p className="text-sm text-gray-500">{card.subtitle}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-1 text-green-600">
                    <TrendingUp className="w-4 h-4" />
                    <span className="text-sm font-medium">+12%</span>
                  </div>

                  {/* Chart Context Menu */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <button
                        className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-gray-500 hover:text-gray-700"
                        onClick={() => console.log('Dropdown trigger clicked for chart', index)}
                      >
                        <MoreHorizontal className="w-4 h-4" />
                      </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem
                        className="flex items-center space-x-2 cursor-pointer"
                        onSelect={() => testFunction(index, 'Refresh Data')}
                      >
                        <RefreshCw className="w-4 h-4" />
                        <span>Refresh Data</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="flex items-center space-x-2 cursor-pointer"
                        onSelect={() => testFunction(index, 'View Full Screen')}
                      >
                        <Maximize2 className="w-4 h-4" />
                        <span>View Full Screen</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="flex items-center space-x-2 cursor-pointer"
                        onSelect={() => {
                          console.log('Export PNG clicked for chart', index, card.title);
                          exportChartAsPNG(index, card.title);
                        }}
                      >
                        <Download className="w-4 h-4" />
                        <span>Export as PNG</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="flex items-center space-x-2 cursor-pointer"
                        onSelect={() => exportChartAsPDF(index, card.title)}
                      >
                        <Download className="w-4 h-4" />
                        <span>Export as PDF</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="flex items-center space-x-2 cursor-pointer"
                        onSelect={() => exportChartDataAsCSV(index, card.title, card.data)}
                      >
                        <Download className="w-4 h-4" />
                        <span>Export Data (CSV)</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="flex items-center space-x-2 cursor-pointer"
                        onSelect={() => shareChart(index, card.title)}
                      >
                        <Share2 className="w-4 h-4" />
                        <span>Share Chart</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>

              {/* Chart Container */}
              <div
                ref={(el) => (chartRefs.current[index] = el)}
                className="bg-gradient-to-br from-gray-50 to-white rounded-lg p-3 border border-gray-100"
              >
                {card.chart}
              </div>

              {/* Quick Stats */}
              <div className="mt-4 flex justify-between text-sm">
                <span className="text-gray-500">Last updated: 2 min ago</span>
                <button className="text-blue-600 hover:text-blue-800 font-medium">
                  View Details →
                </button>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Summary Stats */}
      <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-0 shadow-lg">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600">94%</div>
            <div className="text-sm text-gray-600">Overall Performance</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600">12</div>
            <div className="text-sm text-gray-600">Targets Achieved</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600">8.4</div>
            <div className="text-sm text-gray-600">Satisfaction Score</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600">-15%</div>
            <div className="text-sm text-gray-600">Emissions Reduction</div>
          </div>
        </div>
      </Card>

      {/* Full Screen Chart Modal */}
      <Dialog open={fullScreenChart.isOpen} onOpenChange={(open) => setFullScreenChart(prev => ({ ...prev, isOpen: open }))}>
        <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-0">
          <DialogHeader className="p-6 pb-0">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-xl font-semibold">
                {fullScreenChart.title}
              </DialogTitle>
              <div className="flex items-center space-x-2">
                {/* Full Screen Chart Actions */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const chartIndex = chartCards.findIndex(card => card.title === fullScreenChart.title);
                    if (chartIndex !== -1) refreshChartData(chartIndex);
                  }}
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Download className="w-4 h-4 mr-2" />
                      Export
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem
                      className="flex items-center space-x-2 cursor-pointer"
                      onSelect={() => {
                        const chartIndex = chartCards.findIndex(card => card.title === fullScreenChart.title);
                        if (chartIndex !== -1) exportChartAsPNG(chartIndex, fullScreenChart.title);
                      }}
                    >
                      <Download className="w-4 h-4" />
                      <span>Export as PNG</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="flex items-center space-x-2 cursor-pointer"
                      onSelect={() => {
                        const chartIndex = chartCards.findIndex(card => card.title === fullScreenChart.title);
                        if (chartIndex !== -1) exportChartAsPDF(chartIndex, fullScreenChart.title);
                      }}
                    >
                      <Download className="w-4 h-4" />
                      <span>Export as PDF</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="flex items-center space-x-2 cursor-pointer"
                      onSelect={() => exportChartDataAsCSV(-1, fullScreenChart.title, fullScreenChart.data)}
                    >
                      <Download className="w-4 h-4" />
                      <span>Export Data (CSV)</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="flex items-center space-x-2 cursor-pointer"
                      onSelect={() => {
                        const chartIndex = chartCards.findIndex(card => card.title === fullScreenChart.title);
                        if (chartIndex !== -1) shareChart(chartIndex, fullScreenChart.title);
                      }}
                    >
                      <Share2 className="w-4 h-4" />
                      <span>Share Chart</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </DialogHeader>
          <div className="flex-1 p-6 pt-4">
            <div className="w-full h-[70vh] bg-gradient-to-br from-gray-50 to-white rounded-lg p-6 border border-gray-100">
              {fullScreenChart.chart}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ChartGrid;
