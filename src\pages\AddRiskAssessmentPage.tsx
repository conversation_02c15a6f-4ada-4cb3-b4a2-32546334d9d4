import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import RiskAssessmentForm from '@/components/risk-assessment/RiskAssessmentForm';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';

const AddRiskAssessmentPage: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleSubmit = (data: any, isDraft: boolean) => {
    // In a real application, this would send the data to an API
    console.log('Form submitted:', data);
    
    toast({
      title: isDraft ? "Draft Saved" : "Risk Assessment Submitted",
      description: isDraft 
        ? "Your risk assessment has been saved as a draft." 
        : "Your risk assessment has been submitted successfully.",
      variant: "default",
    });

    // Navigate back to the risk assessment page
    navigate('/risk-assessment');
  };

  const handleCancel = () => {
    navigate('/risk-assessment');
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="sticky top-0 z-10 bg-background border-b border-border/50 p-4 flex items-center">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={handleCancel}
          className="mr-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Risk Assessment
        </Button>
        <h1 className="text-xl font-bold">Add New Risk Assessment</h1>
      </div>

      <RiskAssessmentForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default AddRiskAssessmentPage;
