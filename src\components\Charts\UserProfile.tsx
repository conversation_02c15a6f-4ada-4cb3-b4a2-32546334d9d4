import { User, Mail, Briefcase } from "lucide-react";

const UserProfile = () => {
  // Mock user data - in a real app, this would come from authentication context or API
  const user = {
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Sustainability Manager",
    avatar: null // Could be a URL to profile picture
  };

  return (
    <div className="flex items-center space-x-3 mb-4">
      {/* Avatar */}
      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
        <User className="h-5 w-5 text-white" />
      </div>

      {/* User Information */}
      <div>
        <div className="flex items-center space-x-3">
          <h3 className="text-sm font-medium text-gray-900">{user.name}</h3>
          <span className="text-xs text-gray-500">•</span>
          <span className="text-xs text-gray-600">{user.role}</span>
        </div>
        <div className="flex items-center space-x-1 mt-0.5">
          <Mail className="h-3 w-3 text-gray-400" />
          <span className="text-xs text-gray-500">{user.email}</span>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
