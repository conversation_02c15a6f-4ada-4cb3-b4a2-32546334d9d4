import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { X, Plus } from 'lucide-react';
import { CreateImpactAssessmentData } from '@/types/changeManagement';

const impactAssessmentSchema = z.object({
  changeRequestId: z.string().min(1, 'Change request is required'),
  riskLevel: z.enum(['Low', 'Medium', 'High', 'Critical']),
  businessImpact: z.enum(['Minimal', 'Low', 'Medium', 'High', 'Critical']),
  technicalImpact: z.enum(['Minimal', 'Low', 'Medium', 'High', 'Critical']),
  operationalImpact: z.enum(['Minimal', 'Low', 'Medium', 'High', 'Critical']),
  safetyImpact: z.enum(['None', 'Low', 'Medium', 'High', 'Critical']),
  environmentalImpact: z.enum(['None', 'Low', 'Medium', 'High', 'Critical']),
  complianceImpact: z.enum(['None', 'Low', 'Medium', 'High', 'Critical']),
  riskMitigationMeasures: z.string().min(1, 'Risk mitigation measures are required'),
  recommendations: z.string().min(1, 'Recommendations are required'),
  additionalRequirements: z.string().min(1, 'Additional requirements are required'),
  estimatedDowntime: z.number().optional(),
  resourceRequirements: z.string().min(1, 'Resource requirements are required'),
  testingRequirements: z.string().min(1, 'Testing requirements are required'),
  communicationPlan: z.string().min(1, 'Communication plan is required'),
});

interface CreateImpactAssessmentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreateImpactAssessmentData) => void;
  changeRequestId?: string;
  changeRequests: Array<{ id: string; maskId: string; title: string }>;
}

const CreateImpactAssessmentModal: React.FC<CreateImpactAssessmentModalProps> = ({
  open,
  onOpenChange,
  onSubmit,
  changeRequestId,
  changeRequests,
}) => {
  const [stakeholdersToNotify, setStakeholdersToNotify] = useState<string[]>([]);
  const [newStakeholder, setNewStakeholder] = useState('');

  const form = useForm<z.infer<typeof impactAssessmentSchema>>({
    resolver: zodResolver(impactAssessmentSchema),
    defaultValues: {
      changeRequestId: changeRequestId || '',
      riskLevel: 'Medium',
      businessImpact: 'Medium',
      technicalImpact: 'Medium',
      operationalImpact: 'Medium',
      safetyImpact: 'None',
      environmentalImpact: 'None',
      complianceImpact: 'None',
      riskMitigationMeasures: '',
      recommendations: '',
      additionalRequirements: '',
      resourceRequirements: '',
      testingRequirements: '',
      communicationPlan: '',
    },
  });

  const handleSubmit = (values: z.infer<typeof impactAssessmentSchema>) => {
    const assessmentData: CreateImpactAssessmentData = {
      ...values,
      stakeholdersToNotify,
    };
    onSubmit(assessmentData);
    form.reset();
    setStakeholdersToNotify([]);
    onOpenChange(false);
  };

  const addStakeholder = () => {
    if (newStakeholder.trim() && !stakeholdersToNotify.includes(newStakeholder.trim())) {
      setStakeholdersToNotify([...stakeholdersToNotify, newStakeholder.trim()]);
      setNewStakeholder('');
    }
  };

  const removeStakeholder = (stakeholder: string) => {
    setStakeholdersToNotify(stakeholdersToNotify.filter(s => s !== stakeholder));
  };

  const impactLevels = [
    { value: 'Minimal', label: 'Minimal' },
    { value: 'Low', label: 'Low' },
    { value: 'Medium', label: 'Medium' },
    { value: 'High', label: 'High' },
    { value: 'Critical', label: 'Critical' },
  ];

  const riskLevels = [
    { value: 'Low', label: 'Low' },
    { value: 'Medium', label: 'Medium' },
    { value: 'High', label: 'High' },
    { value: 'Critical', label: 'Critical' },
  ];

  const safetyEnvironmentalLevels = [
    { value: 'None', label: 'None' },
    { value: 'Low', label: 'Low' },
    { value: 'Medium', label: 'Medium' },
    { value: 'High', label: 'High' },
    { value: 'Critical', label: 'Critical' },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Impact Assessment</DialogTitle>
          <DialogDescription>
            Evaluate the risks and impacts of the proposed change.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="changeRequestId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Change Request</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select change request" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {changeRequests.map((cr) => (
                        <SelectItem key={cr.id} value={cr.id}>
                          {cr.maskId} - {cr.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Impact Assessment Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="riskLevel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Overall Risk Level</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {riskLevels.map((level) => (
                          <SelectItem key={level.value} value={level.value}>
                            {level.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="businessImpact"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Business Impact</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {impactLevels.map((level) => (
                          <SelectItem key={level.value} value={level.value}>
                            {level.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="technicalImpact"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Technical Impact</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {impactLevels.map((level) => (
                          <SelectItem key={level.value} value={level.value}>
                            {level.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="operationalImpact"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Operational Impact</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {impactLevels.map((level) => (
                          <SelectItem key={level.value} value={level.value}>
                            {level.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="safetyImpact"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Safety Impact</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {safetyEnvironmentalLevels.map((level) => (
                          <SelectItem key={level.value} value={level.value}>
                            {level.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="environmentalImpact"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Environmental Impact</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {safetyEnvironmentalLevels.map((level) => (
                          <SelectItem key={level.value} value={level.value}>
                            {level.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="complianceImpact"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Compliance Impact</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {safetyEnvironmentalLevels.map((level) => (
                          <SelectItem key={level.value} value={level.value}>
                            {level.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="estimatedDowntime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estimated Downtime (Hours)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="Enter estimated downtime"
                        {...field}
                        onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="riskMitigationMeasures"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Risk Mitigation Measures</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe measures to mitigate identified risks"
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="recommendations"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Recommendations</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Provide recommendations for the change"
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="additionalRequirements"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Additional Requirements</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="List any additional requirements"
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="resourceRequirements"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Resource Requirements</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe required resources"
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="testingRequirements"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Testing Requirements</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe testing requirements"
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="communicationPlan"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Communication Plan</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe the communication plan"
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Stakeholders to Notify */}
            <div className="space-y-3">
              <FormLabel>Stakeholders to Notify</FormLabel>
              <div className="flex gap-2">
                <Input
                  placeholder="Add stakeholder"
                  value={newStakeholder}
                  onChange={(e) => setNewStakeholder(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addStakeholder())}
                />
                <Button type="button" onClick={addStakeholder} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {stakeholdersToNotify.map((stakeholder) => (
                  <Badge key={stakeholder} variant="secondary" className="flex items-center gap-1">
                    {stakeholder}
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => removeStakeholder(stakeholder)}
                    />
                  </Badge>
                ))}
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit">
                Create Impact Assessment
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateImpactAssessmentModal;
