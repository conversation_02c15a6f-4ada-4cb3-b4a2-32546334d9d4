import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  ChevronLeft,
  ChevronRight,
  Search,
  SortAsc,
  SortDesc,
  Filter
} from 'lucide-react';
import { Observation } from '@/types/observation';
import { format } from 'date-fns';

interface ObservationStatusTableProps {
  observations: Observation[];
}

const ObservationStatusTable: React.FC<ObservationStatusTableProps> = ({
  observations
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState<{ key: keyof Observation; direction: 'asc' | 'desc' } | null>(null);

  const itemsPerPage = 10;

  // Filter observations based on search term
  const filteredObservations = observations.filter(obs =>
    obs.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    obs.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (obs.description && obs.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Sort observations
  const sortedObservations = React.useMemo(() => {
    let sortableObservations = [...filteredObservations];
    if (sortConfig !== null) {
      sortableObservations.sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];

        if (aValue === null || aValue === undefined) return sortConfig.direction === 'asc' ? -1 : 1;
        if (bValue === null || bValue === undefined) return sortConfig.direction === 'asc' ? 1 : -1;

        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableObservations;
  }, [filteredObservations, sortConfig]);

  // Paginate observations
  const paginatedObservations = sortedObservations.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const totalPages = Math.ceil(sortedObservations.length / itemsPerPage);

  const handleSort = (key: keyof Observation) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const getStatusBadgeClass = (status: string) => {
    switch(status) {
      case 'New': return 'bg-safety-400 hover:bg-safety-500';
      case 'Open': return 'bg-warning-400 hover:bg-warning-500 text-black';
      case 'In Progress': return 'bg-primary hover:bg-primary/90';
      case 'Pending Review': return 'bg-muted-foreground hover:bg-gray-600';
      case 'Closed': return 'bg-success-500 hover:bg-success-600';
      case 'Action Completed & Closed': return 'bg-indigo-600 hover:bg-indigo-700 text-white';
      default: return 'bg-muted hover:bg-muted/90';
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search observations..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">
                <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort('id')}>
                  Incident ID
                  {sortConfig?.key === 'id' && (
                    sortConfig.direction === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
                  )}
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort('description')}>
                  Title
                  {sortConfig?.key === 'description' && (
                    sortConfig.direction === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
                  )}
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort('location')}>
                  Location
                  {sortConfig?.key === 'location' && (
                    sortConfig.direction === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
                  )}
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort('reportedDate')}>
                  Date
                  {sortConfig?.key === 'reportedDate' && (
                    sortConfig.direction === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
                  )}
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort('category')}>
                  Impact Classification
                  {sortConfig?.key === 'category' && (
                    sortConfig.direction === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
                  )}
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center gap-1 cursor-pointer" onClick={() => handleSort('status')}>
                  Status
                  {sortConfig?.key === 'status' && (
                    sortConfig.direction === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
                  )}
                </div>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedObservations.length > 0 ? (
              paginatedObservations.map((observation) => (
                <TableRow key={observation.id} className="hover:bg-muted/50 cursor-pointer">
                  <TableCell className="font-medium text-blue-600">{observation.id}</TableCell>
                  <TableCell>{observation.description || 'No description'}</TableCell>
                  <TableCell>{observation.location}</TableCell>
                  <TableCell>{format(new Date(observation.reportedDate), 'dd MMM yyyy')}</TableCell>
                  <TableCell>{observation.category}</TableCell>
                  <TableCell>
                    <Badge className={getStatusBadgeClass(observation.status)}>
                      {observation.status}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  No observations found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, sortedObservations.length)} of {sortedObservations.length} entries
          </p>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">Previous page</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Next page</span>
            </Button>
          </div>
        </div>
      )}

      {totalPages > 1 && (
        <div className="flex justify-center">
          <Button variant="outline" size="sm" className="text-xs">
            Load more
          </Button>
        </div>
      )}
    </div>
  );
};

export default ObservationStatusTable;
