export interface MyInspectionAction {
  id: string;
  maskId: string;
  actionType: string;
  actionToBeTaken: string;
  location: string;
  description: string;
  submittedBy: string;
  dueDate: string;
  timelineStatus: string;
  status: string;
  // Additional fields from API response
  application?: string;
  applicationDetails?: {
    extraInfo?: string;
    [key: string]: any;
  };
  applicationId?: string;
  trackId?: string;
  sequence?: string;
  prefix?: string;
  counter?: number;
  objectId?: string;
  assignedToId?: string[];
  submitURL?: string;
  serviceId?: string;
  submittedById?: string;
  created?: string;
  updated?: string;
}

export interface InspectionAttachment {
  name: string;
  type: string;
  size: number;
  url: string;
}

export interface FullLocation {
  country: string;
  region: string;
  site: string;
  level: string;
}

export interface InspectionAction {
  id: string;
  actionType: string;
  status: string;
  actionToBeTaken?: string;
  actionTaken?: string;
  dueDate?: string;
  createdDate?: string;
  assignedToId?: string[];
  uploads?: string[];
}

export interface Inspection {
  id: string;
  maskId?: string;
  title: string;
  location: string;
  fullLocation: FullLocation;
  status: string;
  scheduledDate: Date;
  completedDate?: Date | null;
  description?: string;
  checklist?: {
    id: string;
    name: string;
    description: string;
  };
  inspector?: {
    id: string;
    firstName: string;
    lastName?: string;
  };
  assignedBy?: string;
  attachments?: string[] | InspectionAttachment[];
  uploads?: string[];
  inspectionActions?: InspectionAction[];
  // Additional fields for reports
  inspectionCategory?: string;
  checklistVersion?: string;
  originalDueDate?: Date;
  actualCompletionDate?: Date;
  totalActions?: number;
  completedActions?: number;
  actionStatus?: string;
  totalActionData?: InspectionAction[];
  // Location hierarchy
  locationOne?: { id: string; name: string };
  locationTwo?: { id: string; name: string };
  locationThree?: { id: string; name: string };
  locationFour?: { id: string; name: string };
  locationFive?: { id: string; name: string };
  locationSix?: { id: string; name: string };
}

export interface ChecklistItem {
  id: string;
  name: string;
  description: string;
  status: string;
  curator?: string;
  created: Date;
  updated: Date;
}
