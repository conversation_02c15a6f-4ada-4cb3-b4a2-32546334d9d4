import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import Sidebar from "@/components/checklist-curation/Sidebar";
import DroppableArea from "@/components/checklist-curation/DroppableArea";
import { DroppedItem, ContentComponent } from "@/types/draggable";

interface CurateChecklistModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  accessToken: string | null;
  checklist: any;
}

const CurateChecklistModal = ({ isOpen, onClose, onSuccess, accessToken, checklist }: CurateChecklistModalProps) => {
  const { toast } = useToast();
  const [items, setItems] = useState<DroppedItem[]>([]);
  const [isDragging, setIsDragging] = useState(false);

  const handleDragStart = (type: string) => {
    setIsDragging(true);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleAddItem = (item: DroppedItem) => {
    setItems((prev) => [...prev, item]);
    toast({
      title: "Component Added",
      description: `${item.type} component has been added.`,
    });
  };

  const handleRemoveItem = (id: string) => {
    setItems((prev) => prev.filter((item) => item.id !== id));
    toast({
      title: "Component Removed",
      description: "Component has been removed.",
    });
  };

  const handleUpdateItem = (id: string, data: ContentComponent) => {
    setItems((prev) =>
      prev.map((item) => (item.id === id ? { ...item, data } : item))
    );
    toast({
      title: "Component Updated",
      description: "Changes have been saved.",
    });
  };

  const handleReorderItems = (sourceIndex: number, targetIndex: number) => {
    setItems((prev) => {
      if (sourceIndex === -1 || targetIndex === -1 || sourceIndex === targetIndex) return prev;

      const newItems = [...prev];
      const [movedItem] = newItems.splice(sourceIndex, 1);
      newItems.splice(targetIndex, 0, movedItem);

      // Update position property for all items
      return newItems.map((item, index) => ({
        ...item,
        data: { ...item.data, position: index }
      }));
    });

    toast({
      title: "Components Reordered",
      description: "The order has been updated.",
    });
  };

  const handleSaveChecklist = async () => {
    try {
      // Here you would save the checklist structure to your backend
      // For now, we'll just show a success message
      toast({
        title: "Checklist Saved",
        description: "Your checklist has been saved successfully.",
      });
      onSuccess();
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save checklist. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[98vw] w-full p-0 max-h-none h-auto">
        <div className="bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
          {/* Fixed Header */}
          <div className="sticky top-0 z-50 bg-gradient-to-r from-white via-slate-50 to-white dark:from-slate-800 dark:via-slate-750 dark:to-slate-800 shadow-xl py-6 px-8 border-b border-slate-200 dark:border-slate-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-xl">📋</span>
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
                    Curate Checklist
                  </h1>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-lg font-medium text-slate-700 dark:text-slate-300">
                      {checklist?.name || 'Untitled Checklist'}
                    </span>
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  </div>
                  <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                    ✨ Build and customize your checklist using the modular content canvas
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="px-6 py-3 h-auto font-medium hover:bg-slate-100 dark:hover:bg-slate-700 transition-all duration-200"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveChecklist}
                  className="px-8 py-3 h-auto font-semibold bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border-0"
                >
                  💾 Save Checklist
                </Button>
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div
            className="flex"
            onDragEnd={handleDragEnd}
          >
            <Sidebar
              onDragStart={handleDragStart}
            />

            <DroppableArea
              items={items}
              onAddItem={handleAddItem}
              onRemoveItem={handleRemoveItem}
              onUpdateItem={handleUpdateItem}
              onReorderItems={handleReorderItems}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CurateChecklistModal;
