import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  AlertTriangle, 
  Shield, 
  CheckCircle,
  Clock,
  X,
  ChevronDown,
  ChevronUp,
  FileText,
  Users,
  Calendar
} from 'lucide-react';
import { formatDate } from '@/utils/dateUtils';
import ImageComponent from '@/components/common/ImageComponent';

interface TaskItem {
  type: string;
  name?: string;
  images?: string[];
  selected?: Array<{
    id: string;
    name: string;
    image: string;
  }>;
  option?: Array<{
    current_type: string;
    value: string;
    method?: string;
    files?: string[];
    person?: {
      name: string;
    };
    date?: string;
  }>;
  severity?: string | number;
  likelyhood?: string | number;
  level?: any;
  accept?: boolean;
  step?: number;
  value?: any;
}

interface SubActivityDetailProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task: TaskItem[] | null;
  taskIndex: number;
}

const SubActivityDetailDialog: React.FC<SubActivityDetailProps> = ({
  open,
  onOpenChange,
  task,
  taskIndex,
}) => {
  const [severityTable, setSeverityTable] = useState(false);
  const [likelyhoodTable, setLikelyhoodTable] = useState(false);
  const [riskTable, setRiskTable] = useState(false);

  if (!task) {
    return null;
  }

  // Helper function to get risk level color
  const getRiskLevelColor = (severity: number, likelihood: number) => {
    const riskValue = severity * likelihood;
    if (riskValue >= 15) return 'bg-red-100 text-red-800 border-red-200';
    if (riskValue >= 8) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-green-100 text-green-800 border-green-200';
  };

  // Helper function to get risk matrix value
  const getRiskMatrixValue = (severity: number, likelihood: number) => {
    const riskValue = severity * likelihood;
    if (riskValue >= 20) return 'Very High';
    if (riskValue >= 15) return 'High';
    if (riskValue >= 8) return 'Medium';
    if (riskValue >= 4) return 'Low';
    return 'Very Low';
  };

  const severity = Number(task[4]?.severity) || 0;
  const likelihood = Number(task[4]?.likelyhood) || 0;
  const residualSeverity = Number(task[7]?.severity) || 0;
  const residualLikelihood = Number(task[7]?.likelyhood) || 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-hidden bg-white">
        <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200 p-6 -m-6 mb-0">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                <FileText className="w-6 h-6 text-white" />
              </div>
              <div className="space-y-2">
                <DialogTitle className="text-2xl font-bold text-gray-800">
                  Sub-Activity Risk Assessment
                </DialogTitle>
                <p className="text-gray-600 text-lg font-medium">
                  {task[0]?.name || `Sub-Activity ${taskIndex + 1}`}
                </p>
              </div>
            </div>
          </div>
        </DialogHeader>

        <div className="overflow-y-auto max-h-[calc(95vh-120px)] p-6 space-y-6">
          {/* Hazards Identified */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-orange-600" />
                Hazards Identified
              </CardTitle>
            </CardHeader>
            <CardContent>
              {task[1]?.selected && task[1].selected.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {task[1].selected.map((hazard, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50">
                      <img
                        src={`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${hazard.image}`}
                        alt={hazard.name}
                        className="w-10 h-10 object-contain"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                      <p className="font-medium text-gray-800">{hazard.name}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No hazards identified</p>
              )}
            </CardContent>
          </Card>

          {/* Consequences */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-red-600" />
                Consequences
              </CardTitle>
              <p className="text-sm text-gray-600">
                Identify the potential consequence on Safety, Environment, Financial, Security and Community/Brand Exposure due to presence of hazards
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {task[2]?.option && task[2].option.length > 0 ? (
                task[2].option.map((consequence, index) => (
                  <div key={index} className="border rounded-lg p-4 bg-gray-50">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                      <div>
                        <h6 className="font-semibold text-gray-700 mb-1">Impact on</h6>
                        <p className="text-gray-600">{consequence.current_type}</p>
                      </div>
                      <div className="md:col-span-3">
                        <h6 className="font-semibold text-gray-700 mb-1">Description</h6>
                        <p className="text-gray-600">{consequence.value}</p>
                      </div>
                    </div>
                    {consequence.files && consequence.files.length > 0 && (
                      <div>
                        <h6 className="font-semibold text-gray-700 mb-2">Uploaded Files</h6>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                          {consequence.files.map((file, fileIndex) => (
                            <div key={fileIndex} className="border rounded-lg p-2 bg-white">
                              <ImageComponent fileName={file} size="100" name={true} />
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No consequences identified</p>
              )}
            </CardContent>
          </Card>

          {/* Current Controls */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-blue-600" />
                Current Controls
              </CardTitle>
              <p className="text-sm text-gray-600">
                Existing measures in place to mitigate or manage the above identified hazards and potential consequences.
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {task[3]?.option && task[3].option.length > 0 ? (
                task[3].option.map((control, index) => (
                  <div key={index} className="border rounded-lg p-4 bg-gray-50">
                    <div className="grid grid-cols-1 md:grid-cols-6 gap-4 mb-4">
                      <div>
                        <h6 className="font-semibold text-gray-700 mb-1">Type</h6>
                        <p className="text-gray-600">{control.current_type}</p>
                      </div>
                      <div>
                        <h6 className="font-semibold text-gray-700 mb-1">Method</h6>
                        <p className="text-gray-600">{control.method}</p>
                      </div>
                      <div className="md:col-span-4">
                        <h6 className="font-semibold text-gray-700 mb-1">Description</h6>
                        <p className="text-gray-600">{control.value}</p>
                      </div>
                    </div>
                    {control.files && control.files.length > 0 && (
                      <div>
                        <h6 className="font-semibold text-gray-700 mb-2">Uploaded Files</h6>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                          {control.files.map((file, fileIndex) => (
                            <div key={fileIndex} className="border rounded-lg p-2 bg-white">
                              <ImageComponent fileName={file} size="100" name={true} />
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No current controls identified</p>
              )}
            </CardContent>
          </Card>

          {/* Risk Assessment */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-yellow-600" />
                Risk Assessment
              </CardTitle>
              <p className="text-sm text-gray-600">
                Risk level based on the presence of the identified controls
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Severity */}
              <div className="border-b border-gray-200 pb-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                  <div className="md:col-span-3">
                    <h6 className="font-semibold text-gray-700 mb-1">Severity</h6>
                    <p className="text-sm text-gray-600">
                      Degree of harm or impact that could result from a hazardous event or situation
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-lg font-semibold">
                      {severity}
                    </div>
                  </div>
                </div>
              </div>

              {/* Likelihood */}
              <div className="border-b border-gray-200 pb-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                  <div className="md:col-span-3">
                    <h6 className="font-semibold text-gray-700 mb-1">Likelihood</h6>
                    <p className="text-sm text-gray-600">
                      Frequency with which a hazardous event or situation could happen
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-lg font-semibold">
                      {likelihood}
                    </div>
                  </div>
                </div>
              </div>

              {/* Risk Level */}
              <div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                  <div className="md:col-span-3">
                    <h6 className="font-semibold text-gray-700 mb-1">Risk Level</h6>
                  </div>
                  <div className="text-center">
                    <div className={`inline-block px-4 py-2 rounded-lg font-semibold border ${getRiskLevelColor(severity, likelihood)}`}>
                      {getRiskMatrixValue(severity, likelihood)} ({severity * likelihood})
                    </div>
                  </div>
                </div>
              </div>

              {/* Risk Acceptability */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h6 className="font-semibold text-gray-700 mb-3">Is this Risk Level Acceptable?</h6>
                <div className="flex items-center gap-4">
                  <Badge 
                    className={task[5]?.accept === true ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}
                  >
                    {task[5]?.accept === true ? (
                      <>
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Yes - Acceptable
                      </>
                    ) : (
                      <>
                        <X className="w-3 h-3 mr-1" />
                        No - Additional Controls Required
                      </>
                    )}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Controls (if risk not acceptable) */}
          {task[5]?.accept === false && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="w-5 h-5 text-purple-600" />
                    Proposed Additional Controls
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {task[6]?.option && task[6].option.length > 0 ? (
                    task[6].option.map((control, index) => (
                      <div key={index} className="border rounded-lg p-4 bg-purple-50">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <h6 className="font-semibold text-gray-700 mb-1">Type</h6>
                            <p className="text-gray-600">{control.current_type}</p>
                          </div>
                          <div>
                            <h6 className="font-semibold text-gray-700 mb-1">Description</h6>
                            <p className="text-gray-600">{control.value}</p>
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h6 className="font-semibold text-gray-700 mb-1">Responsibility</h6>
                            <p className="text-gray-600">{control.person?.name}</p>
                          </div>
                          <div>
                            <h6 className="font-semibold text-gray-700 mb-1">Target Date</h6>
                            <p className="text-gray-600">{control.date ? formatDate(control.date) : 'Not specified'}</p>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-center py-4">No additional controls proposed</p>
                  )}
                </CardContent>
              </Card>

              {/* Residual Risk Assessment */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5 text-green-600" />
                    Residual Risk Assessment
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    Expected risk based on the implementation of the identified additional controls
                  </p>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Residual Severity */}
                  <div className="border-b border-gray-200 pb-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                      <div className="md:col-span-3">
                        <h6 className="font-semibold text-gray-700 mb-1">Residual Severity</h6>
                      </div>
                      <div className="text-center">
                        <div className="inline-block px-4 py-2 bg-green-100 text-green-800 rounded-lg font-semibold">
                          {residualSeverity}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Residual Likelihood */}
                  <div className="border-b border-gray-200 pb-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                      <div className="md:col-span-3">
                        <h6 className="font-semibold text-gray-700 mb-1">Residual Likelihood</h6>
                      </div>
                      <div className="text-center">
                        <div className="inline-block px-4 py-2 bg-green-100 text-green-800 rounded-lg font-semibold">
                          {residualLikelihood}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Residual Risk Level */}
                  <div>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                      <div className="md:col-span-3">
                        <h6 className="font-semibold text-gray-700 mb-1">Residual Risk Level</h6>
                      </div>
                      <div className="text-center">
                        <div className={`inline-block px-4 py-2 rounded-lg font-semibold border ${getRiskLevelColor(residualSeverity, residualLikelihood)}`}>
                          {getRiskMatrixValue(residualSeverity, residualLikelihood)} ({residualSeverity * residualLikelihood})
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SubActivityDetailDialog;
