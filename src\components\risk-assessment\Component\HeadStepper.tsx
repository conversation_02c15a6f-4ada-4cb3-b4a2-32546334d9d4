import React from 'react';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock, Circle } from 'lucide-react';

interface StageStatus {
  hazardsIdentification: string;
  consequences: string;
  currentControls: string;
  riskEstimation: string;
  additionalControls: string;
}

interface HeadStepperProps {
  stages: string[];
  stageStatus: StageStatus;
  activeStage: number;
  handleStageClick: (index: number) => void;
  getStatusClass: (status: string) => string;
}

const HeadStepper: React.FC<HeadStepperProps> = ({ 
  stages, 
  stageStatus, 
  activeStage, 
  handleStageClick, 
  getStatusClass 
}) => {
  const getStatusKeyByIndex = (index: number): string => {
    switch (index) {
      case 0:
        return stageStatus.hazardsIdentification;
      case 1:
        return stageStatus.consequences;
      case 2:
        return stageStatus.currentControls;
      case 3:
        return stageStatus.riskEstimation;
      case 4:
        return stageStatus.additionalControls;
      default:
        return '';
    }
  };

  const getStepIcon = (status: string) => {
    if (status === 'completed') return <CheckCircle className="h-4 w-4" />;
    if (status === 'inprogress') return <Clock className="h-4 w-4" />;
    return <Circle className="h-4 w-4" />;
  };

  const getStepVariant = (status: string, isActive: boolean) => {
    if (status === 'completed') return 'default';
    if (status === 'inprogress') return 'secondary';
    if (isActive) return 'outline';
    return 'outline';
  };

  return (
    <div className="space-y-4">
      {/* Stepper */}
      <div className="flex items-center justify-between w-full">
        {stages.map((stage, index) => {
          const stageKey = getStatusKeyByIndex(index);
          const isActive = activeStage === index;
          const isCompleted = stageKey === 'completed';
          const isInProgress = stageKey === 'inprogress';

          return (
            <React.Fragment key={index}>
              <div 
                className="flex flex-col items-center cursor-pointer group"
                onClick={() => handleStageClick(index)}
              >
                {/* Step Circle */}
                <div className={`
                  flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
                  ${isCompleted ? 'bg-green-500 border-green-500 text-white' : 
                    isInProgress ? 'bg-orange-500 border-orange-500 text-white' :
                    isActive ? 'border-primary text-primary' : 'border-gray-300 text-gray-400'}
                  group-hover:border-primary group-hover:text-primary
                `}>
                  {isCompleted ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : isInProgress ? (
                    <Clock className="h-5 w-5" />
                  ) : (
                    <span className="text-sm font-semibold">{index + 1}</span>
                  )}
                </div>
                
                {/* Step Title */}
                <div className={`
                  mt-2 text-xs text-center max-w-20 leading-tight
                  ${isActive ? 'text-primary font-medium' : 'text-gray-600'}
                  group-hover:text-primary
                `}>
                  {stage}
                </div>
              </div>

              {/* Connector Line */}
              {index < stages.length - 1 && (
                <div className={`
                  flex-1 h-0.5 mx-2 transition-colors
                  ${isCompleted ? 'bg-green-500' : 'bg-gray-300'}
                `} />
              )}
            </React.Fragment>
          );
        })}
      </div>

      {/* Legend */}
      <div className="flex justify-end gap-4 text-xs">
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
          <span>Finalized</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-orange-500"></div>
          <span>Drafted</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-gray-400"></div>
          <span>No information entered</span>
        </div>
      </div>
    </div>
  );
};

export default HeadStepper;
