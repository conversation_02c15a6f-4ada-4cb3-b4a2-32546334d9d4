import React from 'react';
import { format } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON>cil, Trash2, RotateCcw } from 'lucide-react';
import { RoleAssignment, MODULES } from '@/types/roleAssignment';

interface RoleAssignmentTableProps {
  assignments: RoleAssignment[];
  onEdit: (assignment: RoleAssignment) => void;
  onDelete: (assignmentId: string) => void;
  onReset: (assignmentId: string) => void;
}

const RoleAssignmentTable: React.FC<RoleAssignmentTableProps> = ({
  assignments,
  onEdit,
  onDelete,
  onReset
}) => {
  // Get module name from module ID
  const getModuleName = (moduleId: string): string => {
    return MODULES.find(m => m.id === moduleId)?.name || moduleId;
  };

  // Group assignments by user
  const groupedAssignments = assignments.reduce((acc, assignment) => {
    if (!acc[assignment.userId]) {
      acc[assignment.userId] = [];
    }
    acc[assignment.userId].push(assignment);
    return acc;
  }, {} as Record<string, RoleAssignment[]>);

  return (
    <div className="table-container animate-fade-in">
      <Table>
        <TableHeader className="table-header">
          <TableRow>
            <TableHead>User</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Module</TableHead>
            <TableHead>Assigned Roles</TableHead>
            <TableHead>Assigned Date</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {assignments.length === 0 ? (
            <TableRow className="table-row">
              <TableCell colSpan={6} className="table-cell h-24 text-center text-gray-500">
                No role assignments found
              </TableCell>
            </TableRow>
          ) : (
            assignments.map((assignment) => (
              <TableRow key={assignment.id} className="table-row">
                <TableCell className="table-cell font-medium">{assignment.userName}</TableCell>
                <TableCell className="table-cell">
                  <div className="text-sm">
                    <div><span className="font-medium">Country:</span> {assignment.location.country}</div>
                    <div><span className="font-medium">Region:</span> {assignment.location.region}</div>
                    <div><span className="font-medium">Site:</span> {assignment.location.site}</div>
                    <div><span className="font-medium">Level:</span> {assignment.location.level}</div>
                  </div>
                </TableCell>
                <TableCell className="table-cell">{getModuleName(assignment.module)}</TableCell>
                <TableCell className="table-cell">
                  <div className="flex flex-wrap gap-1">
                    {assignment.roles.length > 0 ? (
                      assignment.roles.map(role => (
                        <Badge key={role} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                          {role}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-muted-foreground text-sm italic">No roles assigned</span>
                    )}
                  </div>
                </TableCell>
                <TableCell className="table-cell">{format(assignment.assignedAt, 'PPP')}</TableCell>
                <TableCell className="table-cell text-right">
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 text-[#0A5A8F] hover:text-[#0A5A8F]/80 hover:bg-[#0A5A8F]/10 transition-all duration-300"
                      onClick={() => onEdit(assignment)}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 text-amber-600 hover:text-amber-700 hover:bg-amber-50 transition-all duration-300"
                      onClick={() => onReset(assignment.id)}
                    >
                      Reset
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 text-[#D12027] hover:text-[#D12027]/80 hover:bg-[#D12027]/10 transition-all duration-300"
                      onClick={() => onDelete(assignment.id)}
                    >
                      Delete
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default RoleAssignmentTable;
