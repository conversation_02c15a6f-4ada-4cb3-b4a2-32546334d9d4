import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/components/ui/use-toast";
import PageHeader from "@/components/common/PageHeader";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Upload, UserPlus, HelpCircle, Download } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import CreateUserModal from "@/components/users/CreateUserModal";
import EditUserModal from "@/components/users/EditUserModal";
import UserTable from "@/components/users/UserTable";
import { User } from "@/types/user";
import { downloadFile } from "@/lib/fileUtils";
import apiService from "@/services/apiService";

// API endpoints
const USERS_ENDPOINT = '/users/external';

// Interface for API response
interface ApiUser {
  id: string;
  firstName: string;
  email: string;
  company: string;
  type: string;
  status: boolean;
  roles: string[];
  created: string;
  updated: string;
}

const UsersPage = () => {
  const { toast } = useToast();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [externalUsers, setExternalUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchExternalUsers = useCallback(async () => {
    try {
      setIsLoading(true);
      const data = await apiService.get(USERS_ENDPOINT);

      // Filter to show only External type users and map to our User interface
      const filteredUsers = (data || [])
        .filter((user: ApiUser) => user.type === "External")
        .map((user: ApiUser) => ({
          id: user.id,
          name: user.firstName,
          email: user.email,
          company: user.company,
          createdAt: new Date(user.created),
        }));

      setExternalUsers(filteredUsers);
    } catch (error) {
      console.error('Error fetching external users:', error);
      toast({
        title: "Error",
        description: "Failed to load external users. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Fetch external users on component mount
  useEffect(() => {
    fetchExternalUsers();
  }, [fetchExternalUsers]);

  // Handle user creation
  const handleCreateUser = async (user: User) => {
    try {
      // Transform user data to match API format
      const apiUserData = {
        firstName: user.name,
        email: user.email,
        company: user.company,
        type: "External",
        status: true,
        roles: ["user"]
      };

      // Create user via API
      await apiService.post(USERS_ENDPOINT, apiUserData);

      // Refresh the users list to get the latest data
      await fetchExternalUsers();

      // Show success message
      toast({
        title: "External User Created",
        description: `${user.name} has been successfully added.`,
        variant: "default"
      });
    } catch (error) {
      console.error('Error creating user:', error);
      toast({
        title: "Error",
        description: "Failed to create user. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Handle bulk upload (placeholder function)
  const handleBulkUpload = () => {
    toast({
      title: "Upload External Users",
      description: "This feature is not implemented yet.",
      variant: "default"
    });
  };

  // Handle template download
  const handleTemplateDownload = () => {
    const templateUrl = '/templates/external_users_template.csv';
    const filename = 'external_users_template.csv';

    downloadFile(templateUrl, filename);

    toast({
      title: "Template Downloaded",
      description: "External users template has been downloaded.",
      variant: "default"
    });
  };

  // Handle edit user
  const handleEditClick = (user: User) => {
    setSelectedUser(user);
    setIsEditModalOpen(true);
  };

  // Handle edit user submission
  const handleEditSubmit = async (userId: string, updatedData: { name: string; email: string; company: string }) => {
    try {
      // Transform data to match API format
      const apiUpdateData = {
        firstName: updatedData.name,
        email: updatedData.email,
        company: updatedData.company
      };

      // Update user via API
      await apiService.patch(`${USERS_ENDPOINT}/${userId}`, apiUpdateData);

      // Refresh the users list to get the latest data
      await fetchExternalUsers();

      // Show success message
      toast({
        title: "User Updated",
        description: `${updatedData.name}'s information has been updated successfully.`,
        variant: "default"
      });
    } catch (error) {
      console.error('Error updating user:', error);
      toast({
        title: "Error",
        description: "Failed to update user. Please try again.",
        variant: "destructive"
      });
    }
  };





  return (
    <>
      <div className="mb-6">
        <PageHeader
          title="External Users Management"
          description="Manage contractor and visitor accounts with limited system access"
        />
      </div>

      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div>
              <div className="text-xl font-medium mb-2">External Users</div>
              <p className="text-muted-foreground">
                Manage contractor and visitor accounts in this section. External users have
                limited access to the system based on their assigned roles and permissions.
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center">
                <Button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="flex items-center gap-2"
                >
                  <UserPlus className="h-4 w-4" /> Create External User
                </Button>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" className="ml-1">
                        <HelpCircle className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Create a single external user account with name, email, and organization details</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={handleBulkUpload}
                  className="flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" /> Upload External Users
                </Button>
                <Button
                  variant="ghost"
                  onClick={handleTemplateDownload}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                </Button>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" className="ml-1">
                        <HelpCircle className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Upload a CSV file with multiple external users or download a template</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>

          <UserTable
            users={externalUsers}
            onEdit={handleEditClick}
            loading={isLoading}
            itemsPerPage={15}
          />
        </CardContent>
      </Card>

      {/* Create User Modal */}
      <CreateUserModal
        open={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
        onSubmit={handleCreateUser}
      />

      {/* Edit User Modal */}
      <EditUserModal
        open={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        user={selectedUser}
        onSubmit={handleEditSubmit}
      />


    </>
  );
};

export default UsersPage;
