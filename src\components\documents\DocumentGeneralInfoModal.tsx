import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  FileText, 
  User, 
  Calendar, 
  Tag, 
  Building, 
  Clock,
  Target,
  Loader2
} from 'lucide-react';
import { format } from 'date-fns';

interface MyAction {
  id: string;
  maskId: string;
  actionType: string;
  description: string;
  submittedBy: string;
  dueDate: string;
  status: string;
  timeline?: string;
  applicationId?: string;
}

interface Document {
  id: string;
  name: string;
  type: 'pdf' | 'doc' | 'image' | 'video' | 'other';
  size: string;
  uploadedBy: string;
  uploadedDate: string;
  category: string;
  tags: string[];
  description?: string;
  maskId?: string;
  scopeApplicability?: string;
  purpose?: string;
  keywords?: string;
  docId?: string;
  created?: string;
  updated?: string;
  creatorTargetDate?: string;
  reviewerTargetDate?: string;
  approverTargetDate?: string;
  initiatorId?: string;
  creatorId?: string;
  reviewerId?: string;
  approverId?: string;
  documentCategoryId?: string;
  initiator?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  creator?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  reviewer?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  approver?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  documentCategory?: {
    id: string;
    name: string;
    level?: number;
  };
}

interface DocumentGeneralInfoModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  action: MyAction | null;
  document: Document | null;
  loading: boolean;
}

const DocumentGeneralInfoModal: React.FC<DocumentGeneralInfoModalProps> = ({
  open,
  onOpenChange,
  action,
  document,
  loading
}) => {
  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return <FileText className="h-6 w-6 text-red-500" />;
      case 'doc':
        return <FileText className="h-6 w-6 text-blue-500" />;
      case 'image':
        return <FileText className="h-6 w-6 text-green-500" />;
      case 'video':
        return <FileText className="h-6 w-6 text-purple-500" />;
      default:
        return <FileText className="h-6 w-6 text-gray-500" />;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
    } catch (error) {
      return 'Invalid date';
    }
  };

  const getStatusBadge = (status: string) => {
    let badgeClass = '';
    switch(status?.toLowerCase()) {
      case 'completed': badgeClass = 'bg-green-600 hover:bg-green-700 text-white'; break;
      case 'pending': badgeClass = 'bg-yellow-600 hover:bg-yellow-700 text-white'; break;
      case 'in progress': badgeClass = 'bg-blue-600 hover:bg-blue-700 text-white'; break;
      case 'overdue': badgeClass = 'bg-red-600 hover:bg-red-700 text-white'; break;
      default: badgeClass = 'bg-gray-500 hover:bg-gray-600 text-white';
    }
    return <Badge className={badgeClass}>{status}</Badge>;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-6 border-b">
          <DialogTitle className="text-2xl font-semibold text-gray-900 flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
            Document General Information
          </DialogTitle>
          {action && (
            <p className="text-sm text-gray-600 mt-2">
              Action: {action.maskId} - {action.actionType}
            </p>
          )}
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
              <p className="text-muted-foreground">Loading document details...</p>
            </div>
          </div>
        ) : document ? (
          <div className="py-6 space-y-8">
            {/* Action Information */}
            {action && (
              <div className="bg-blue-50 rounded-lg p-6">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-2 h-6 bg-blue-500 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-gray-900">Action Information</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Action ID</p>
                    <p className="text-sm font-semibold text-blue-600">{action.maskId}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Action Type</p>
                    <p className="text-sm text-gray-900">{action.actionType}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Status</p>
                    {getStatusBadge(action.status)}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Due Date</p>
                    <p className="text-sm text-gray-900">{formatDate(action.dueDate)}</p>
                  </div>
                </div>
                {action.description && (
                  <div className="mt-4">
                    <p className="text-sm font-medium text-gray-500">Description</p>
                    <p className="text-sm text-gray-900">{action.description}</p>
                  </div>
                )}
              </div>
            )}

            {/* Document Basic Information */}
            <div className="bg-gray-50 rounded-lg p-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-6 bg-green-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Document Information</h3>
              </div>
              
              <div className="flex items-start gap-4 mb-6">
                {getFileIcon(document.type)}
                <div className="flex-1">
                  <h4 className="text-xl font-semibold text-gray-900">{document.name}</h4>
                  <p className="text-sm text-gray-600">{document.maskId || 'No Document ID'}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Building className="h-4 w-4 text-gray-500" />
                    <p className="text-sm font-medium text-gray-500">Category</p>
                  </div>
                  <p className="text-sm text-gray-900">{document.documentCategory?.name || document.category}</p>
                </div>
                
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <FileText className="h-4 w-4 text-gray-500" />
                    <p className="text-sm font-medium text-gray-500">Document ID</p>
                  </div>
                  <p className="text-sm text-gray-900">{document.docId || 'Not assigned'}</p>
                </div>

                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <p className="text-sm font-medium text-gray-500">File Size</p>
                  </div>
                  <p className="text-sm text-gray-900">{document.size}</p>
                </div>
              </div>
            </div>

            {/* Document Content */}
            <div className="bg-yellow-50 rounded-lg p-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-6 bg-yellow-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Document Content</h3>
              </div>
              
              <div className="space-y-4">
                {document.purpose && (
                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-2">Purpose</p>
                    <p className="text-sm text-gray-900 bg-white p-3 rounded border">{document.purpose}</p>
                  </div>
                )}
                
                {document.scopeApplicability && (
                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-2">Scope & Applicability</p>
                    <p className="text-sm text-gray-900 bg-white p-3 rounded border">{document.scopeApplicability}</p>
                  </div>
                )}

                {document.keywords && (
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Tag className="h-4 w-4 text-gray-500" />
                      <p className="text-sm font-medium text-gray-500">Keywords</p>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {document.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Workflow Information */}
            <div className="bg-purple-50 rounded-lg p-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-6 bg-purple-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Workflow & Timeline</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Creator */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 font-semibold text-xs">1</span>
                    </div>
                    <p className="text-sm font-medium text-gray-700">Creator</p>
                  </div>
                  <div className="bg-white p-3 rounded border">
                    <div className="flex items-center gap-2 mb-1">
                      <User className="h-4 w-4 text-gray-500" />
                      <p className="text-sm font-medium">{document.creator?.firstName || 'Not assigned'}</p>
                    </div>
                    {document.creator?.email && (
                      <p className="text-xs text-gray-500">{document.creator.email}</p>
                    )}
                    {document.creatorTargetDate && (
                      <div className="flex items-center gap-1 mt-2">
                        <Target className="h-3 w-3 text-amber-500" />
                        <p className="text-xs text-amber-600">Due: {formatDate(document.creatorTargetDate)}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Reviewer */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-semibold text-xs">2</span>
                    </div>
                    <p className="text-sm font-medium text-gray-700">Reviewer</p>
                  </div>
                  <div className="bg-white p-3 rounded border">
                    <div className="flex items-center gap-2 mb-1">
                      <User className="h-4 w-4 text-gray-500" />
                      <p className="text-sm font-medium">{document.reviewer?.firstName || 'Not assigned'}</p>
                    </div>
                    {document.reviewer?.email && (
                      <p className="text-xs text-gray-500">{document.reviewer.email}</p>
                    )}
                    {document.reviewerTargetDate && (
                      <div className="flex items-center gap-1 mt-2">
                        <Target className="h-3 w-3 text-amber-500" />
                        <p className="text-xs text-amber-600">Due: {formatDate(document.reviewerTargetDate)}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Approver */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                      <span className="text-purple-600 font-semibold text-xs">3</span>
                    </div>
                    <p className="text-sm font-medium text-gray-700">Approver</p>
                  </div>
                  <div className="bg-white p-3 rounded border">
                    <div className="flex items-center gap-2 mb-1">
                      <User className="h-4 w-4 text-gray-500" />
                      <p className="text-sm font-medium">{document.approver?.firstName || 'Not assigned'}</p>
                    </div>
                    {document.approver?.email && (
                      <p className="text-xs text-gray-500">{document.approver.email}</p>
                    )}
                    {document.approverTargetDate && (
                      <div className="flex items-center gap-1 mt-2">
                        <Target className="h-3 w-3 text-amber-500" />
                        <p className="text-xs text-amber-600">Due: {formatDate(document.approverTargetDate)}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Timestamps */}
            <div className="bg-gray-50 rounded-lg p-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-6 bg-gray-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Timestamps</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <p className="text-sm font-medium text-gray-500">Created</p>
                  </div>
                  <p className="text-sm text-gray-900">{formatDate(document.created)}</p>
                </div>
                
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <p className="text-sm font-medium text-gray-500">Last Updated</p>
                  </div>
                  <p className="text-sm text-gray-900">{formatDate(document.updated)}</p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No document details available</p>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default DocumentGeneralInfoModal;
