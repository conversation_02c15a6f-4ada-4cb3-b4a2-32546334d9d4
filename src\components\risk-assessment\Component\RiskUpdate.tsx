import React, { useState, useRef, useEffect } from 'react';
import { useSelector } from 'react-redux';
import SignatureCanvas from 'react-signature-canvas';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Upload, Trash2, X } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import apiService from '@/services/apiService';
import ImageComponent from '@/components/common/ImageComponent';
import { RootState } from '@/store';

interface FormValues {
  reasonForReview: string;
  changes: string;
  reasonForChanges: string;
  initiatedBy: string;
  approvedBy: string;
  reference: string;
  signature: {
    name?: string;
    sign?: string;
  };
  attachment: string[];
}

interface FormErrors {
  [key: string]: string;
}

interface RiskUpdateProps {
  show: boolean;
  onChangeModel: (show: boolean) => void;
  id: string;
  onSubmitUpdate: () => void;
}

const RiskUpdate: React.FC<RiskUpdateProps> = ({ show, onChangeModel, id, onSubmitUpdate }) => {
  const user = useSelector((state: RootState) => state.auth.user);
  const { toast } = useToast();
  const [formValues, setFormValues] = useState<FormValues>({
    reasonForReview: '',
    changes: '',
    reasonForChanges: '',
    initiatedBy: '',
    approvedBy: '',
    reference: '',
    signature: {},
    attachment: []
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [amend, setAmend] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const sign1 = useRef<SignatureCanvas>(null);

  const dataURItoFile = (dataURI: string, filename: string): File => {
    const byteString = atob(dataURI.split(",")[1]);
    const mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
    const ab = new ArrayBuffer(byteString.length);
    const dw = new DataView(ab);
    for (let i = 0; i < byteString.length; i++) {
      dw.setUint8(i, byteString.charCodeAt(i));
    }
    return new File([ab], filename, { type: mimeString });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormValues({
      ...formValues,
      [name]: value,
    });

    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: '',
      });
    }
  };

  const validateForm = (): FormErrors => {
    const errors: FormErrors = {};
    if (!formValues.reasonForReview) errors.reasonForReview = 'Reason for review is required';
    if (!formValues.changes) errors.changes = 'Changes are required';
    if (!formValues.reasonForChanges) errors.reasonForChanges = 'Reason for change is required';
    if (!formValues.initiatedBy) errors.initiatedBy = 'Initiated by is required';
    if (!formValues.approvedBy) errors.approvedBy = 'Approved by is required';
    if (!formValues.reference) errors.reference = 'Reference is required';
    if (!sign1.current || sign1.current.isEmpty()) errors.signature = 'Signature is required';
    if (amend.length === 0) errors.attachment = 'Attachment is required';
    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const errors = validateForm();

    if (Object.keys(errors).length === 0) {
      try {
        setIsLoading(true);
        const filename = new Date().getTime() + "captin_sign.png";
        let signatureResponse;

        if (sign1.current && !sign1.current.isEmpty()) {
          const formData1 = new FormData();
          formData1.append('file', dataURItoFile(sign1.current.getTrimmedCanvas().toDataURL("image/png"), filename));

          signatureResponse = await apiService.post('/files', formData1, {
            headers: {
              'Content-Type': 'multipart/form-data',
            }
          });
        }

        let data: FormValues = formValues;
        if (signatureResponse?.files?.[0]?.originalname) {
          data = {
            ...formValues,
            signature: {
              name: user?.firstName || '',
              sign: signatureResponse.files[0].originalname
            }
          };
        }

        await apiService.post(`/risk-assessments/${id}/update`, data);

        toast({
          title: "Success",
          description: "Risk assessment updated successfully",
        });

        onSubmitUpdate();
        onChangeModel(false);
        resetForm();
      } catch (error) {
        console.error("Update error: ", error);
        toast({
          title: "Error",
          description: "Failed to update risk assessment",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    } else {
      setFormErrors(errors);
    }
  };

  const resetForm = () => {
    setFormValues({
      reasonForReview: '',
      changes: '',
      reasonForChanges: '',
      initiatedBy: '',
      approvedBy: '',
      reference: '',
      signature: {},
      attachment: []
    });
    setFormErrors({});
    setAmend([]);
    if (sign1.current) {
      sign1.current.clear();
    }
  };

  const amendUploads = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const formData1 = new FormData();
    formData1.append('file', file);

    try {
      const response = await apiService.post('/files', formData1, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });

      if (response?.files?.[0]?.originalname) {
        setAmend(prevAmend => [...prevAmend, response.files[0].originalname]);
        toast({
          title: "Success",
          description: "File uploaded successfully",
        });
      }
    } catch (error) {
      console.error("File upload error: ", error);
      toast({
        title: "Error",
        description: "Failed to upload file",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    setFormValues(prevState => ({
      ...prevState,
      attachment: amend
    }));
  }, [amend]);

  const onDeleteAmendFiles = (index: number) => {
    const newAmend = [...amend];
    newAmend.splice(index, 1);
    setAmend(newAmend);
  };

  return (
    <Dialog open={show} onOpenChange={onChangeModel}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Risk Assessment Update</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="reasonForReview">Reason For Review</Label>
              <Textarea
                id="reasonForReview"
                name="reasonForReview"
                value={formValues.reasonForReview}
                onChange={handleInputChange}
                rows={3}
              />
              {formErrors.reasonForReview && (
                <p className="text-sm text-destructive">{formErrors.reasonForReview}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="changes">Changes</Label>
              <Textarea
                id="changes"
                name="changes"
                value={formValues.changes}
                onChange={handleInputChange}
                rows={3}
              />
              {formErrors.changes && (
                <p className="text-sm text-destructive">{formErrors.changes}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="reasonForChanges">Reason for Changes</Label>
              <Textarea
                id="reasonForChanges"
                name="reasonForChanges"
                value={formValues.reasonForChanges}
                onChange={handleInputChange}
                rows={3}
              />
              {formErrors.reasonForChanges && (
                <p className="text-sm text-destructive">{formErrors.reasonForChanges}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="initiatedBy">Initiated By</Label>
              <Textarea
                id="initiatedBy"
                name="initiatedBy"
                value={formValues.initiatedBy}
                onChange={handleInputChange}
                rows={3}
              />
              {formErrors.initiatedBy && (
                <p className="text-sm text-destructive">{formErrors.initiatedBy}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="approvedBy">Approved By</Label>
              <Textarea
                id="approvedBy"
                name="approvedBy"
                value={formValues.approvedBy}
                onChange={handleInputChange}
                rows={3}
              />
              {formErrors.approvedBy && (
                <p className="text-sm text-destructive">{formErrors.approvedBy}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="reference">Reference</Label>
              <Textarea
                id="reference"
                name="reference"
                value={formValues.reference}
                onChange={handleInputChange}
                rows={3}
              />
              {formErrors.reference && (
                <p className="text-sm text-destructive">{formErrors.reference}</p>
              )}
            </div>
          </div>

          {/* File Upload Section */}
          <div className="space-y-4">
            <Label>Attachment (if any)</Label>

            {amend.length > 0 && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 border-2 border-dashed rounded-lg">
                {amend.map((file, index) => (
                  <div key={index} className="relative">
                    <div className="border rounded-lg p-2 shadow-sm">
                      <ImageComponent fileName={file} size={'100'} name={true} />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute -top-2 -right-2 h-6 w-6 p-0"
                        onClick={() => onDeleteAmendFiles(index)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            <div className="flex justify-center">
              <label className="cursor-pointer">
                <div className="flex flex-col items-center justify-center w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
                  <Upload className="h-8 w-8 text-gray-400" />
                  <span className="text-xs text-gray-500 mt-1">Upload</span>
                </div>
                <input
                  type="file"
                  className="hidden"
                  onChange={amendUploads}
                  accept="image/*,.pdf,.doc,.docx"
                />
              </label>
            </div>
            {formErrors.attachment && (
              <p className="text-sm text-destructive">{formErrors.attachment}</p>
            )}
          </div>

          {/* Signature Section */}
          <div className="space-y-4">
            <div className="text-center">
              <p className="text-sm text-muted-foreground mb-4">
                I confirm that the risk assessment has been reviewed as above and
                changes (if any) have been made as shown here. This change has
                also been authorized by the relevant authority.
              </p>
            </div>

            <div className="flex flex-col items-center space-y-4">
              <div className="border-2 border-gray-300 rounded-lg">
                <SignatureCanvas
                  penColor="#1F3BB3"
                  canvasProps={{
                    width: 500,
                    height: 200,
                    className: 'signature-canvas',
                    style: { borderRadius: '6px' },
                  }}
                  ref={sign1}
                />
              </div>
              {formErrors.signature && (
                <p className="text-sm text-destructive">{formErrors.signature}</p>
              )}
              <Button
                type="button"
                variant="outline"
                onClick={() => sign1.current?.clear()}
              >
                <X className="h-4 w-4 mr-2" />
                Clear Signature
              </Button>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onChangeModel(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Updating...' : 'Update'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default RiskUpdate;
