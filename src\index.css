@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Brand colors from AcuiZen logo */
    --acuizen-blue: 202 89% 30%; /* #0A5A8F */
    --acuizen-red: 358 73% 48%; /* #D12027 */
    --acuizen-gray: 0 0% 33%; /* #555555 */

    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: var(--acuizen-blue);
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 202 89% 95%;
    --accent-foreground: var(--acuizen-blue);

    --destructive: var(--acuizen-red);
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: var(--acuizen-blue);

    --radius: 0.5rem;

    --sidebar-background: 210 29% 24%;
    --sidebar-foreground: 0 0% 96%;
    --sidebar-primary: 210 100% 52%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 29% 29%;
    --sidebar-accent-foreground: 0 0% 96%;
    --sidebar-border: 210 29% 22%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 100% 52%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 210 29% 18%;
    --sidebar-foreground: 0 0% 96%;
    --sidebar-primary: 210 100% 52%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 29% 24%;
    --sidebar-accent-foreground: 0 0% 96%;
    --sidebar-border: 210 29% 16%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .badge {
    @apply inline-flex items-center justify-center rounded-full text-xs font-medium transition-colors
    focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring
    focus-visible:ring-offset-2 ring-offset-background px-2 py-0.5;
  }

  .badge-primary {
    @apply bg-primary text-primary-foreground;
  }

  .badge-secondary {
    @apply bg-secondary text-secondary-foreground;
  }

  .badge-danger {
    @apply bg-danger-400 text-white;
  }

  .badge-warning {
    @apply bg-warning-400 text-black;
  }

  .badge-success {
    @apply bg-success-500 text-white;
  }

  .transition-width {
    transition-property: width;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  .nav-icon-btn {
    @apply flex items-center justify-center h-10 w-10 rounded-md hover:bg-sidebar-accent p-2 text-sidebar-foreground;
  }

  .nav-icon-active {
    @apply bg-sidebar-accent;
  }

  .data-grid {
    @apply w-full border border-border rounded-lg overflow-hidden;
  }

  .data-grid-header {
    @apply bg-[#EFF6FF] text-[#4B5563] uppercase text-xs font-medium;
  }

  .data-grid-header th {
    @apply px-4 py-3 text-left;
  }

  .data-grid-body tr {
    @apply border-t border-border hover:bg-[#F9FAFB];
  }

  .data-grid-body td {
    @apply px-4 py-3;
  }

  /* Table styles matching the reference UI */
  .table-container {
    @apply w-full border border-border rounded-md overflow-hidden;
  }

  .table-header {
    @apply bg-[#EFF6FF] text-[#4B5563];
  }

  .table-header th {
    @apply font-medium text-sm py-3 px-4 text-left;
  }

  .table-row {
    @apply border-t border-border hover:bg-[#F9FAFB] transition-colors;
  }

  .table-cell {
    @apply py-3 px-4 text-sm;
  }

  /* Animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }

  .animate-pulse-subtle {
    animation: pulseSlight 2s infinite;
  }

  /* Gradient backgrounds */
  .bg-gradient-blue {
    background: linear-gradient(135deg, hsl(var(--acuizen-blue)) 0%, hsl(202, 89%, 40%) 100%);
  }

  /* Lightbox styles */
  .lightbox-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    pointer-events: auto;
  }

  /* Ensure table cells are visible */
  .table-cell {
    display: table-cell !important;
  }

  .bg-gradient-card {
    background: linear-gradient(to bottom right, white, hsl(210, 40%, 98%));
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .bg-gradient-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.22);
  }

  .bg-gradient-sidebar {
    background: linear-gradient(180deg, hsl(202, 89%, 20%) 0%, hsl(202, 89%, 18%) 30%, hsl(358, 73%, 25%) 70%, hsl(358, 73%, 20%) 100%);
  }

  /* Stepper styles */
  .stepper-container {
    @apply w-full flex items-center justify-between;
  }

  .stepper-item {
    @apply flex items-center flex-1;
  }

  .stepper-step {
    @apply flex flex-col items-center space-y-2 relative z-10;
  }

  .stepper-step-active .stepper-circle {
    @apply border-primary bg-primary/10 text-primary;
  }

  .stepper-step-completed .stepper-circle {
    @apply border-primary bg-primary text-primary-foreground;
  }

  .stepper-circle {
    @apply h-10 w-10 rounded-full border-2 flex items-center justify-center transition-all duration-300;
  }

  .stepper-label {
    @apply text-xs font-medium transition-colors duration-300;
  }

  .stepper-connector {
    @apply h-[2px] flex-1 transition-colors duration-500;
  }

  .stepper-connector-active {
    @apply bg-primary;
  }

  .animate-check {
    animation: checkmark 0.4s ease-in-out forwards;
  }

  /* Keyframes */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes pulseSlight {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }

  @keyframes checkmark {
    0% {
      transform: scale(0);
      opacity: 0;
    }
    50% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Gallery and Lightbox styles */
  .gallery-wrapper {
    @apply w-full;
  }

  .gallery-container {
    @apply w-full overflow-hidden rounded-md;
  }

  /* Image container in ImageComponent */
  .image-container {
    @apply w-full;
  }

  .image-wrapper {
    @apply rounded-md overflow-hidden border border-gray-200 shadow-sm;
  }

  /* File container styles */
  .file-container {
    @apply w-full flex flex-col items-center justify-center;
  }

  .file-icon-wrapper {
    @apply flex items-center justify-center p-2 rounded-md border border-gray-200 shadow-sm bg-gray-50 w-16 h-16;
  }

  /* Make gallery images look clickable */
  .gallery-container img {
    @apply cursor-pointer transition-opacity hover:opacity-90 object-cover;
  }

  /* Lightbox styles for yet-another-react-lightbox */
  .yarl__container {
    z-index: 9999 !important;
  }

  .yarl__slide_image {
    object-fit: contain !important;
  }

  .yarl__toolbar {
    background-color: rgba(0, 0, 0, 0.3) !important;
    padding: 8px !important;
  }

  .yarl__button {
    opacity: 0.8 !important;
    transition: opacity 0.2s ease-in-out !important;
  }

  .yarl__button:hover {
    opacity: 1 !important;
  }

  /* Fix for react-photo-gallery alignment */
  .react-photo-gallery--gallery {
    @apply w-full;
  }

  .react-photo-gallery--gallery > div {
    @apply justify-center;
  }

  /* Observation/Inspection detail styles */
  .obs-title {
    @apply text-sm font-semibold text-gray-700 mb-1;
  }

  .obs-content {
    @apply text-sm text-gray-900 mb-2;
  }

  .obs-subcontent {
    @apply text-xs text-gray-600 mb-1;
  }

  /* Print styles for react-to-print */
  @media print {
    .print-content {
      font-family: 'Arial', sans-serif !important;
      color: #000 !important;
      background: white !important;
      padding: 20px !important;
      margin: 0 !important;
      font-size: 12px !important;
      line-height: 1.4 !important;
    }

    .print-only {
      display: block !important;
    }

    .no-print {
      display: none !important;
    }

    /* Reset grid layouts for print */
    .grid {
      display: block !important;
    }

    .grid > * {
      margin-bottom: 15px !important;
    }

    /* Card styles for print */
    .card, [class*="card"] {
      border: 1px solid #e5e7eb !important;
      margin-bottom: 15px !important;
      page-break-inside: avoid !important;
      background: white !important;
      box-shadow: none !important;
    }

    /* Badge styles for print */
    .badge, [class*="badge"] {
      border: 1px solid #d1d5db !important;
      padding: 2px 6px !important;
      border-radius: 4px !important;
      font-size: 10px !important;
      background: white !important;
      color: #000 !important;
    }

    /* Heading styles for print */
    h1, h2, h3, h4, h5, h6 {
      color: #000 !important;
      page-break-after: avoid !important;
      margin-bottom: 10px !important;
    }

    /* Text color overrides for print */
    .text-muted-foreground {
      color: #6b7280 !important;
    }

    .text-gray-700 {
      color: #374151 !important;
    }

    .text-gray-600 {
      color: #4b5563 !important;
    }

    /* Spacing utilities for print */
    .space-y-6 > * + * {
      margin-top: 1.5rem !important;
    }

    .space-y-4 > * + * {
      margin-top: 1rem !important;
    }

    .space-y-2 > * + * {
      margin-top: 0.5rem !important;
    }

    .mb-4 {
      margin-bottom: 1rem !important;
    }

    .mb-2 {
      margin-bottom: 0.5rem !important;
    }

    .mt-1 {
      margin-top: 0.25rem !important;
    }

    .p-6 {
      padding: 1.5rem !important;
    }

    .p-4 {
      padding: 1rem !important;
    }

    .border {
      border: 1px solid #e5e7eb !important;
    }

    .rounded-md {
      border-radius: 0.375rem !important;
    }

    .bg-gray-50 {
      background-color: #f9fafb !important;
    }

    .text-sm {
      font-size: 0.875rem !important;
    }

    .font-medium {
      font-weight: 500 !important;
    }

    .font-semibold {
      font-weight: 600 !important;
    }

    .font-bold {
      font-weight: 700 !important;
    }

    /* Hide interactive elements in print */
    button, .button {
      display: none !important;
    }

    /* Ensure images are visible but sized appropriately */
    img {
      max-width: 100% !important;
      height: auto !important;
      page-break-inside: avoid !important;
    }

    /* Table styles for print */
    table {
      border-collapse: collapse !important;
      width: 100% !important;
    }

    th, td {
      border: 1px solid #e5e7eb !important;
      padding: 8px !important;
      text-align: left !important;
    }

    /* Page break controls */
    .page-break-before {
      page-break-before: always !important;
    }

    .page-break-after {
      page-break-after: always !important;
    }

    .page-break-inside-avoid {
      page-break-inside: avoid !important;
    }
  }
}
