import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Calendar,
  User,
  Building2,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  Users,
  Target,
  Settings,
  BookOpen,
  X,
  Printer
} from 'lucide-react';
import { formatDate } from '@/utils/dateUtils';
import {
  ChangeRequest,
  ImpactAssessment,
  ApprovalWorkflow,
  TrainingRollout
} from '@/types/changeManagement';

interface ChangeManagementDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  itemType: 'change-request' | 'impact-assessment' | 'approval-workflow' | 'training-rollout' | null;
  itemId: string | null;
  data: ChangeRequest | ImpactAssessment | ApprovalWorkflow | TrainingRollout | null;
}

const ChangeManagementDetailsModal: React.FC<ChangeManagementDetailsModalProps> = ({
  open,
  onOpenChange,
  itemType,
  itemId,
  data
}) => {
  const [loading, setLoading] = useState(false);

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
      case 'completed':
      case 'implemented':
      case 'closed':
        return 'default';
      case 'pending':
      case 'in progress':
      case 'scheduled':
        return 'secondary';
      case 'rejected':
      case 'cancelled':
        return 'destructive';
      case 'draft':
      case 'planned':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return 'destructive';
      case 'high':
        return 'destructive';
      case 'medium':
        return 'secondary';
      case 'low':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const renderChangeRequestDetails = (changeRequest: ChangeRequest) => (
    <div className="space-y-6">
      {/* Header Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Change Request Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">ID</label>
              <p className="font-mono text-sm">{changeRequest.maskId}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Status</label>
              <div className="mt-1">
                <Badge variant={getStatusBadgeVariant(changeRequest.status)}>
                  {changeRequest.status}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Type</label>
              <p>{changeRequest.changeType}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Priority</label>
              <div className="mt-1">
                <Badge variant={getPriorityBadgeVariant(changeRequest.priority)}>
                  {changeRequest.priority}
                </Badge>
              </div>
            </div>
          </div>
          
          <div>
            <label className="text-sm font-medium text-muted-foreground">Title</label>
            <p className="font-medium">{changeRequest.title}</p>
          </div>
          
          <div>
            <label className="text-sm font-medium text-muted-foreground">Description</label>
            <p className="text-sm">{changeRequest.description}</p>
          </div>
        </CardContent>
      </Card>

      {/* Requestor Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Requestor Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Requested By</label>
              <p className="font-medium">
                {changeRequest.requestedBy.firstName} {changeRequest.requestedBy.lastName}
              </p>
              <p className="text-sm text-muted-foreground">{changeRequest.requestedBy.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Department</label>
              <p>{changeRequest.requestedBy.department}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Request Date</label>
              <p>{formatDate(changeRequest.requestDate)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Proposed Implementation</label>
              <p>{formatDate(changeRequest.proposedImplementationDate)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Business Justification */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Business Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">Business Justification</label>
            <p className="text-sm">{changeRequest.businessJustification}</p>
          </div>
          
          <div>
            <label className="text-sm font-medium text-muted-foreground">Impact Description</label>
            <p className="text-sm">{changeRequest.impactDescription}</p>
          </div>
          
          <div>
            <label className="text-sm font-medium text-muted-foreground">Rollback Plan</label>
            <p className="text-sm">{changeRequest.rollbackPlan}</p>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Estimated Cost</label>
              <p>{changeRequest.estimatedCost ? `$${changeRequest.estimatedCost.toLocaleString()}` : 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Estimated Duration</label>
              <p>{changeRequest.estimatedDuration ? `${changeRequest.estimatedDuration} hours` : 'Not specified'}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Affected Areas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Affected Areas
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">Affected Systems</label>
            <div className="flex flex-wrap gap-2 mt-2">
              {changeRequest.affectedSystems.map((system, index) => (
                <Badge key={index} variant="outline">{system}</Badge>
              ))}
            </div>
          </div>
          
          <div>
            <label className="text-sm font-medium text-muted-foreground">Affected Departments</label>
            <div className="flex flex-wrap gap-2 mt-2">
              {changeRequest.affectedDepartments.map((dept, index) => (
                <Badge key={index} variant="outline">{dept}</Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderImpactAssessmentDetails = (assessment: ImpactAssessment) => (
    <div className="space-y-6">
      {/* Header Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Impact Assessment Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Assessment ID</label>
              <p className="font-mono text-sm">{assessment.maskId}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Status</label>
              <div className="mt-1">
                <Badge variant={getStatusBadgeVariant(assessment.status)}>
                  {assessment.status}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Risk Level</label>
              <div className="mt-1">
                <Badge variant={getPriorityBadgeVariant(assessment.riskLevel)}>
                  {assessment.riskLevel}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Assessment Date</label>
              <p>{formatDate(assessment.assessmentDate)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Assessor Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Assessor Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Assessed By</label>
              <p className="font-medium">
                {assessment.assessor.firstName} {assessment.assessor.lastName}
              </p>
              <p className="text-sm text-muted-foreground">{assessment.assessor.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Department</label>
              <p>{assessment.assessor.department}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Impact Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Impact Analysis
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Business Impact</label>
              <Badge variant={getPriorityBadgeVariant(assessment.businessImpact)}>
                {assessment.businessImpact}
              </Badge>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Technical Impact</label>
              <Badge variant={getPriorityBadgeVariant(assessment.technicalImpact)}>
                {assessment.technicalImpact}
              </Badge>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Operational Impact</label>
              <Badge variant={getPriorityBadgeVariant(assessment.operationalImpact)}>
                {assessment.operationalImpact}
              </Badge>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Safety Impact</label>
              <Badge variant={getPriorityBadgeVariant(assessment.safetyImpact)}>
                {assessment.safetyImpact}
              </Badge>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Environmental Impact</label>
              <Badge variant={getPriorityBadgeVariant(assessment.environmentalImpact)}>
                {assessment.environmentalImpact}
              </Badge>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Compliance Impact</label>
              <Badge variant={getPriorityBadgeVariant(assessment.complianceImpact)}>
                {assessment.complianceImpact}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Risk Mitigation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Risk Mitigation & Requirements
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">Risk Mitigation Measures</label>
            <p className="text-sm">{assessment.riskMitigationMeasures}</p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">Recommendations</label>
            <p className="text-sm">{assessment.recommendations}</p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">Additional Requirements</label>
            <p className="text-sm">{assessment.additionalRequirements}</p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">Resource Requirements</label>
            <p className="text-sm">{assessment.resourceRequirements}</p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">Testing Requirements</label>
            <p className="text-sm">{assessment.testingRequirements}</p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">Communication Plan</label>
            <p className="text-sm">{assessment.communicationPlan}</p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Estimated Downtime</label>
              <p>{assessment.estimatedDowntime ? `${assessment.estimatedDowntime} hours` : 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Approval Required</label>
              <Badge variant={assessment.approvalRequired ? 'default' : 'outline'}>
                {assessment.approvalRequired ? 'Yes' : 'No'}
              </Badge>
            </div>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">Stakeholders to Notify</label>
            <div className="flex flex-wrap gap-2 mt-2">
              {assessment.stakeholdersToNotify.map((stakeholder, index) => (
                <Badge key={index} variant="outline">{stakeholder}</Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderApprovalWorkflowDetails = (workflow: ApprovalWorkflow) => (
    <div className="space-y-6">
      {/* Header Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Approval Workflow Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Workflow ID</label>
              <p className="font-mono text-sm">{workflow.maskId}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Status</label>
              <div className="mt-1">
                <Badge variant={getStatusBadgeVariant(workflow.status)}>
                  {workflow.status}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Workflow Name</label>
              <p className="font-medium">{workflow.workflowName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Change Request ID</label>
              <p className="font-mono text-sm">{workflow.changeRequestId}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Progress Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Progress Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Current Stage</label>
              <p className="font-medium">{workflow.currentStage} of {workflow.totalStages}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Progress</label>
              <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                <div
                  className="bg-blue-600 h-2.5 rounded-full"
                  style={{ width: `${(workflow.currentStage / workflow.totalStages) * 100}%` }}
                ></div>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {Math.round((workflow.currentStage / workflow.totalStages) * 100)}% Complete
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Created</label>
              <p>{formatDate(workflow.created)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
              <p>{formatDate(workflow.updated)}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderTrainingRolloutDetails = (training: TrainingRollout) => (
    <div className="space-y-6">
      {/* Header Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Training Rollout Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Training ID</label>
              <p className="font-mono text-sm">{training.maskId}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Status</label>
              <div className="mt-1">
                <Badge variant={getStatusBadgeVariant(training.status)}>
                  {training.status}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Training Type</label>
              <p>{training.trainingType}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Priority</label>
              <div className="mt-1">
                <Badge variant={getPriorityBadgeVariant(training.priority)}>
                  {training.priority}
                </Badge>
              </div>
            </div>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">Training Title</label>
            <p className="font-medium">{training.trainingTitle}</p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">Description</label>
            <p className="text-sm">{training.trainingDescription}</p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">Change Request ID</label>
            <p className="font-mono text-sm">{training.changeRequestId}</p>
          </div>
        </CardContent>
      </Card>

      {/* Instructor Information */}
      {training.instructor && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Instructor Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Instructor</label>
                <p className="font-medium">
                  {training.instructor.firstName} {training.instructor.lastName}
                </p>
                <p className="text-sm text-muted-foreground">{training.instructor.email}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Training Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Training Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">Target Audience</label>
            <div className="flex flex-wrap gap-2 mt-2">
              {training.targetAudience.map((audience, index) => (
                <Badge key={index} variant="outline">{audience}</Badge>
              ))}
            </div>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">Affected Teams</label>
            <div className="flex flex-wrap gap-2 mt-2">
              {training.affectedTeams.map((team, index) => (
                <Badge key={index} variant="outline">{team}</Badge>
              ))}
            </div>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">Training Materials</label>
            <div className="flex flex-wrap gap-2 mt-2">
              {training.trainingMaterials.map((material, index) => (
                <Badge key={index} variant="secondary">{material}</Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderContent = () => {
    if (!data) {
      return (
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">No details available</p>
        </div>
      );
    }

    switch (itemType) {
      case 'change-request':
        return renderChangeRequestDetails(data as ChangeRequest);
      case 'impact-assessment':
        return renderImpactAssessmentDetails(data as ImpactAssessment);
      case 'approval-workflow':
        return renderApprovalWorkflowDetails(data as ApprovalWorkflow);
      case 'training-rollout':
        return renderTrainingRolloutDetails(data as TrainingRollout);
      default:
        return <div>Unknown item type</div>;
    }
  };

  const getModalTitle = () => {
    if (!data) return 'Details';

    switch (itemType) {
      case 'change-request':
        return `Change Request - ${(data as ChangeRequest).maskId}`;
      case 'impact-assessment':
        return `Impact Assessment - ${(data as ImpactAssessment).maskId}`;
      case 'approval-workflow':
        return `Approval Workflow - ${(data as ApprovalWorkflow).maskId}`;
      case 'training-rollout':
        return `Training Rollout - ${(data as TrainingRollout).maskId}`;
      default:
        return 'Details';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-2xl font-bold">
                {getModalTitle()}
              </DialogTitle>
              <DialogDescription className="text-base mt-1">
                Complete information about this item
              </DialogDescription>
            </div>
            <div className="flex items-center space-x-3 no-print">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.print()}
                className="bg-green-50 hover:bg-green-100 border-green-300 text-green-700"
              >
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            renderContent()
          )}
        </div>

        <div className="flex justify-end gap-3 pt-6 border-t mt-6">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ChangeManagementDetailsModal;
