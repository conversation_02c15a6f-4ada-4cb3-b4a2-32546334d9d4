import React, { useState, useEffect } from 'react';
import { hazardCategories, HazardItem } from '@/data/hazardCategories';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { X, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface HazardsIdentificationPanelProps {
  selectedHazards: HazardItem[];
  onHazardsChange: (hazards: HazardItem[]) => void;
  onComplete: () => void;
  isCompleted: boolean;
  isFinalized: boolean;
}

const HazardsIdentificationPanel: React.FC<HazardsIdentificationPanelProps> = ({
  selectedHazards,
  onHazardsChange,
  onComplete,
  isCompleted,
  isFinalized
}) => {
  const [activeCategory, setActiveCategory] = useState(hazardCategories[0].id);
  const [localSelectedHazards, setLocalSelectedHazards] = useState<HazardItem[]>(selectedHazards);

  // Update local state when prop changes
  useEffect(() => {
    setLocalSelectedHazards(selectedHazards);
  }, [selectedHazards]);

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    setActiveCategory(categoryId);
  };

  // Handle hazard selection
  const handleHazardToggle = (hazard: HazardItem, e?: React.MouseEvent | React.ChangeEvent) => {
    // Prevent default behavior to avoid form submission
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    let updatedHazards: HazardItem[];

    // Check if the hazard is already selected
    const isSelected = localSelectedHazards.some(h => h.id === hazard.id);

    if (isSelected) {
      // Remove the hazard if it's already selected
      updatedHazards = localSelectedHazards.filter(h => h.id !== hazard.id);
    } else {
      // Add the hazard if it's not already selected
      updatedHazards = [...localSelectedHazards, hazard];
    }

    setLocalSelectedHazards(updatedHazards);
  };

  // Handle removing a hazard from the selected list
  const handleRemoveHazard = (hazardId: string) => {
    const updatedHazards = localSelectedHazards.filter(h => h.id !== hazardId);
    setLocalSelectedHazards(updatedHazards);
  };

  // Save the selected hazards
  const handleSave = (e?: React.MouseEvent) => {
    // Prevent default behavior to avoid form submission
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Update parent component with selected hazards
    onHazardsChange(localSelectedHazards);

    // Mark as complete if hazards are selected
    if (localSelectedHazards.length > 0) {
      onComplete();
    }
  };

  // Get the active category's hazards
  const activeHazards = hazardCategories.find(cat => cat.id === activeCategory)?.hazards || [];

  return (
    <div className="space-y-4">
      <div className="flex">
        {/* Category tabs - vertical sidebar */}
        <div className="w-1/4 border-r pr-2">
          <div className="space-y-1">
            {hazardCategories.map((category) => (
              <button
                type="button"
                key={category.id}
                onClick={() => handleCategorySelect(category.id)}
                className={cn(
                  "w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors",
                  activeCategory === category.id
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                )}
                disabled={isFinalized}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>

        {/* Hazard items - main panel */}
        <div className="w-3/4 pl-4">
          <div className="grid grid-cols-2 gap-2">
            {activeHazards.map((hazard) => {
              const isSelected = localSelectedHazards.some(h => h.id === hazard.id);
              const IconComponent = hazard.icon;

              return (
                <div
                  key={hazard.id}
                  className={cn(
                    "flex items-center p-2 rounded-md border cursor-pointer transition-all",
                    isSelected ? "border-primary bg-primary/10" : "border-border hover:border-primary/50"
                  )}
                  onClick={(e) => !isFinalized && handleHazardToggle(hazard, e)}
                >
                  <div className="mr-2" onClick={(e) => e.stopPropagation()}>
                    <Checkbox
                      checked={isSelected}
                      onCheckedChange={(checked) => {
                        if (!isFinalized) {
                          // Create a synthetic event to pass to handleHazardToggle
                          const syntheticEvent = { preventDefault: () => {}, stopPropagation: () => {} };
                          handleHazardToggle(hazard, syntheticEvent as any);
                        }
                      }}
                      disabled={isFinalized}
                      className="pointer-events-auto"
                    />
                  </div>
                  <div className="mr-2 text-primary">
                    <IconComponent className="h-5 w-5" />
                  </div>
                  <div className="flex-1">{hazard.label}</div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Selected hazards display */}
      <div className="mt-4">
        <h4 className="text-sm font-medium mb-2">Selected Hazards:</h4>
        <div className="flex flex-wrap gap-2">
          {localSelectedHazards.length > 0 ? (
            localSelectedHazards.map((hazard) => {
              const IconComponent = hazard.icon;
              return (
                <Badge
                  key={hazard.id}
                  variant="outline"
                  className="flex items-center gap-1 py-1 px-2"
                >
                  <IconComponent className="h-3 w-3" />
                  {hazard.label}
                  {!isFinalized && (
                    <span
                      role="button"
                      tabIndex={0}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveHazard(hazard.id);
                      }}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === " ") {
                          e.preventDefault();
                          handleRemoveHazard(hazard.id);
                        }
                      }}
                      className="ml-1 text-muted-foreground hover:text-foreground cursor-pointer"
                    >
                      <X className="h-3 w-3" />
                    </span>
                  )}
                </Badge>
              );
            })
          ) : (
            <p className="text-sm text-muted-foreground">No hazards selected</p>
          )}
        </div>
      </div>

      {/* Save button and status */}
      <div className="flex justify-between items-center mt-4">
        <div>
          {isCompleted && (
            <Badge variant="outline" className={isFinalized ? "bg-green-100 text-green-800" : "bg-orange-100 text-orange-800"}>
              {isFinalized ? "Finalized" : "Amend"}
            </Badge>
          )}
        </div>
        {!isFinalized && (
          <Button
            type="button"
            onClick={(e) => handleSave(e)}
            disabled={localSelectedHazards.length === 0}
          >
            Save
          </Button>
        )}
      </div>
    </div>
  );
};

export default HazardsIdentificationPanel;
