// Define the type for a consequence item
export interface ConsequenceItem {
  id: string;
  impactOn: string; // System, User, Environment, etc.
  description: string;
  attachment?: File | null; // Optional image attachment
  attachmentUrl?: string; // URL for displaying the image
}

// Define the impact options
export const IMPACT_OPTIONS = [
  { label: 'System', value: 'system' },
  { label: 'User', value: 'user' },
  { label: 'Environment', value: 'environment' },
  { label: 'Equipment', value: 'equipment' },
  { label: 'Process', value: 'process' },
  { label: 'Personnel', value: 'personnel' },
  { label: 'Reputation', value: 'reputation' },
  { label: 'Financial', value: 'financial' },
  { label: 'Other', value: 'other' },
];
