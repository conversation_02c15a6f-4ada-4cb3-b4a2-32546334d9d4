import React from 'react';
import { Button } from '@/components/ui/button';

import { Observation } from '@/types/observation';
import { FileText, User, Download, Calendar, Tag, Briefcase } from 'lucide-react';
import { format } from 'date-fns';

interface ObservationExpandedViewProps {
  observation: Observation;
  onDownloadPdf: (observation: Observation) => void;
}

const ObservationExpandedView: React.FC<ObservationExpandedViewProps> = ({
  observation,
  onDownloadPdf
}) => {
  return (
    <div className="bg-white rounded-md p-4">
      <div className="flex justify-end mb-4">
        <Button
          variant="outline"
          size="sm"
          className="h-8 bg-blue-50 hover:bg-blue-100 border-blue-300 text-blue-700"
          onClick={() => onDownloadPdf(observation)}
        >
          <Download className="h-4 w-4 mr-1" /> Download PDF
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Column 1 - Reporter Info */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center">
              <User className="h-6 w-6 text-gray-500" />
            </div>
            <div>
              <h3 className="font-medium">{observation.reportedBy}</h3>
              <p className="text-sm text-muted-foreground">Reported by</p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-gray-500" />
              <div>
                <span className="text-muted-foreground">Reported Date:</span>{' '}
                <span>{format(observation.reportedDate, 'PPP')}</span>
              </div>
            </div>

            <div className="flex items-center gap-2 text-sm">
              <Tag className="h-4 w-4 text-gray-500" />
              <div>
                <span className="text-muted-foreground">Category:</span>{' '}
                <span>{observation.category}</span>
              </div>
            </div>

            <div className="flex items-center gap-2 text-sm">
              <Briefcase className="h-4 w-4 text-gray-500" />
              <div>
                <span className="text-muted-foreground">Type:</span>{' '}
                <span>{observation.type}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Column 2 - Full Location */}
        <div>
          {observation.fullLocation && (
            <div className="h-full">
              <h3 className="font-medium text-sm text-muted-foreground mb-3">LOCATION DETAILS </h3>
              <div className="text-sm space-y-2">
                <div className="flex items-center">
                  <span className="text-muted-foreground w-20">Country:</span>
                  <span className="font-medium">{observation.fullLocation.country}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-muted-foreground w-20">Region:</span>
                  <span className="font-medium">{observation.fullLocation.region}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-muted-foreground w-20">Site:</span>
                  <span className="font-medium">{observation.fullLocation.site}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-muted-foreground w-20">Level:</span>
                  <span className="font-medium">{observation.fullLocation.level}</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Column 3 - Description and Attachments */}
        <div className="space-y-5">
          {/* Description Section */}
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-3">DESCRIPTION</h3>
            <p className="text-sm">{observation.description}</p>
          </div>

          {/* Attachments Section */}
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-3">ATTACHMENTS</h3>

            {observation.attachments && observation.attachments.length > 0 ? (
              <div className="space-y-3">
                {observation.attachments.map((attachment, index) => (
                  <div key={index} className="flex items-center gap-3 p-2 border rounded-md">
                    {attachment.type.startsWith('image/') ? (
                      <div className="h-12 w-12 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
                        <img
                          src={attachment.url}
                          alt={attachment.name}
                          className="h-full w-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="h-12 w-12 bg-gray-100 rounded flex items-center justify-center">
                        <FileText className="h-5 w-5 text-gray-500" />
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{attachment.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {(attachment.size / 1024).toFixed(1)} KB
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No attachments</p>
            )}
          </div>

          {/* Action To Be Taken (if applicable) */}
          {observation.actionToBeTaken && (
            <div>
              <h3 className="font-medium text-sm text-muted-foreground mb-3">ACTION TO BE TAKEN</h3>
              <p className="text-sm">{observation.actionToBeTaken}</p>
            </div>
          )}
        </div>

        {/* Column 4 - Action Taken, Evidence Images */}
        <div className="space-y-5">

          {/* Action Taken Section */}
          {observation.actionTaken && (
            <div>
              <h3 className="font-medium text-sm text-muted-foreground mb-3">ACTION TAKEN</h3>
              <p className="text-sm">{observation.actionTaken}</p>
            </div>
          )}

          {/* Evidence Images Section */}
          {observation.evidenceImages && observation.evidenceImages.length > 0 && (
            <div>
              <h3 className="font-medium text-sm text-muted-foreground mb-3">EVIDENCE IMAGES</h3>
              <div className="grid grid-cols-2 gap-2">
                {observation.evidenceImages.map((image, index) => (
                  <div key={index} className="border rounded-md overflow-hidden">
                    <img
                      src={image.url}
                      alt={`Evidence ${index + 1}`}
                      className="w-full h-24 object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Action Assignee (if needed) */}
          {observation.actionAssignee && (
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-gray-500" />
              </div>
              <div>
                <p className="text-sm font-medium">{observation.actionAssignee}</p>
                <p className="text-xs text-muted-foreground">Action Assignee</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ObservationExpandedView;
