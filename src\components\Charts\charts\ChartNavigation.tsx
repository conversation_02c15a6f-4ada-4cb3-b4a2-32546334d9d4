import { useState, useMemo } from "react";
import MetricCard from "@/components/Charts/MetricCard";
import MetricDetails from "@/components/Charts/MetricDetails";
import ObservationAnalyticsCharts from "@/components/observations/ObservationAnalyticsCharts";
import CascadingLocationFilter, { LocationFilterState } from "@/components/Charts/CascadingLocationFilter";
import { Observation } from "@/types/observation";
import { calculateObservationMetrics, generateObservationTrends } from "@/utils/observationMetrics";

interface ChartNavigationProps {
  observations?: Observation[];
}

const ChartNavigation = ({ observations = [] }: ChartNavigationProps) => {
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});
  const [locationFilters, setLocationFilters] = useState<LocationFilterState>({
    country: "all",
    region: "all",
    site: "all"
  });

  // Calculate metrics from actual observation data
  const metrics = useMemo(() => calculateObservationMetrics(observations), [observations]);
  const trends = useMemo(() => generateObservationTrends(observations), [observations]);

  // Observation metrics data based on real data
  const overviewMetrics = [
    {
      id: "chart-observations-rectified-on-spot",
      title: "% of Observations Rectified On-the-Spot",
      value: metrics.rectifiedOnSpot.count.toString(),
      unit: `observations (${metrics.rectifiedOnSpot.percentage}%)`,
      target: metrics.rectifiedOnSpot.total,
      targetPercentage: 100 - metrics.rectifiedOnSpot.percentage,
      trend: trends.rectifiedOnSpot,
      isImproving: true
    },
    {
      id: "chart-observations-closed-on-time",
      title: "% of Observations Closed Within Stipulated Time",
      value: metrics.closedOnTime.count.toString(),
      unit: `observations (${metrics.closedOnTime.percentage}%)`,
      target: metrics.closedOnTime.total,
      targetPercentage: 100 - metrics.closedOnTime.percentage,
      trend: trends.closedOnTime,
      isImproving: true
    },
    {
      id: "chart-observations-overdue",
      title: "% of Observations Overdue for Closure",
      value: metrics.overdue.count.toString(),
      unit: `observations (${metrics.overdue.percentage}%)`,
      target: 0, // Target is 0 overdue observations
      targetPercentage: metrics.overdue.percentage,
      trend: trends.overdue,
      isImproving: false
    },
    {
      id: "chart-repetitive-observations",
      title: "Number of Repetitive Observations",
      value: metrics.repetitive.count.toString(),
      unit: "observations",
      target: 0, // Target is 0 repetitive observations
      targetPercentage: metrics.repetitive.total > 0 ? Math.round((metrics.repetitive.count / metrics.repetitive.total) * 100) : 0,
      trend: trends.repetitive,
      isImproving: false
    }
  ];



  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  const handleLocationFilterChange = (filterType: keyof LocationFilterState, value: string) => {
    setLocationFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  return (
    <div className="space-y-6">
      {/* Location Filters */}
      <CascadingLocationFilter
        filterState={locationFilters}
        onFilterChange={handleLocationFilterChange}
      />

      {/* Simplified Content - Only Metric Boxes */}
      <div className="space-y-6">
        {/* Headline Metrics - Only Overview Metrics */}
        <div className="grid grid-cols-4 gap-4">
          {overviewMetrics.map((metric) => (
            <MetricCard
              key={metric.id}
              {...metric}
              showDetails={openDetailsId === metric.id}
              onToggleDetails={() => handleToggleDetails(metric.id)}
              selectedPeriod={getSelectedPeriod(metric.id)}
              onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
            />
          ))}
        </div>

        {/* Details Section - Rendered below the grid when a card is selected */}
        {openDetailsId && (() => {
          const selectedMetric = overviewMetrics.find(m => m.id === openDetailsId);
          return selectedMetric ? (
            <MetricDetails
              title={selectedMetric.title}
              value={selectedMetric.value}
              unit={selectedMetric.unit}
              targetPercentage={selectedMetric.targetPercentage}
              trend={selectedMetric.trend}
              isImproving={selectedMetric.isImproving}
              selectedPeriod={getSelectedPeriod(openDetailsId)}
            />
          ) : null;
        })()}
      </div>

      {/* Additional Analytics Charts */}
      <ObservationAnalyticsCharts observations={observations} />
    </div>
  );
};

export default ChartNavigation;
