
import React from "react";
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ContentMode } from "@/types/draggable";

interface ModeToggleProps {
  activeMode: ContentMode;
  onChange: (mode: ContentMode) => void;
}

const ModeToggle: React.FC<ModeToggleProps> = ({ activeMode, onChange }) => {
  return (
    <div className="flex flex-col items-center w-full mt-3">
      <Tabs
        defaultValue={activeMode}
        className="w-full"
        onValueChange={(value) => onChange(value as ContentMode)}
      >
        <TabsList className="grid w-full grid-cols-2 p-1 bg-slate-100 dark:bg-slate-700">
          <TabsTrigger
            value="communicate"
            className="text-xs font-medium py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm"
          >
            Content
          </TabsTrigger>
          <TabsTrigger
            value="feedback"
            className="text-xs font-medium py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm"
          >
            Form
          </TabsTrigger>
        </TabsList>
      </Tabs>
      <p className="text-xs text-muted-foreground mt-2 text-center">Select component type</p>
    </div>
  );
};

export default ModeToggle;
