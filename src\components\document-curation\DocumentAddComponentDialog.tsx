import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription
} from "@/components/ui/dialog";
import { Command, CommandInput, CommandList, CommandEmpty, CommandGroup, CommandItem } from "@/components/ui/command";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Heading1,
  Heading2,
  Type,
  List,
  ListOrdered,
  Quote,
  Minus,
  Image,
  FileText,
  Link,
  Video
} from "lucide-react";

interface DocumentAddComponentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectComponent: (type: string) => void;
}

const DocumentAddComponentDialog: React.FC<DocumentAddComponentDialogProps> = ({
  open,
  onOpenChange,
  onSelectComponent,
}) => {
  const [searchQuery, setSearchQuery] = React.useState("");

  // Document components available for selection
  const allComponents = [
    // Text Components
    { type: 'document-header', icon: <Heading1 className="h-5 w-5" />, label: 'Document Header', description: 'Main document title' },
    { type: 'section-header', icon: <Heading2 className="h-5 w-5" />, label: 'Section Header', description: 'Section title' },
    { type: 'paragraph', icon: <Type className="h-5 w-5" />, label: 'Paragraph', description: 'Text content' },
    
    // List Components
    { type: 'bullet-list', icon: <List className="h-5 w-5" />, label: 'Bullet List', description: 'Unordered list' },
    { type: 'numbered-list', icon: <ListOrdered className="h-5 w-5" />, label: 'Numbered List', description: 'Ordered list' },
    
    // Content Components
    { type: 'quote', icon: <Quote className="h-5 w-5" />, label: 'Quote Block', description: 'Quoted text' },
    { type: 'separator', icon: <Minus className="h-5 w-5" />, label: 'Separator', description: 'Visual divider' },
    
    // Media Components
    { type: 'image', icon: <Image className="h-5 w-5" />, label: 'Image/File Upload', description: 'Upload images or files' },
    { type: 'video', icon: <Video className="h-5 w-5" />, label: 'Video Upload', description: 'Upload video files (max 20MB)' },
    { type: 'file-attachment', icon: <FileText className="h-5 w-5" />, label: 'File Attachment', description: 'Upload any file type' },
    
    // Interactive Components
    { type: 'link', icon: <Link className="h-5 w-5" />, label: 'Link', description: 'Hyperlink' },
  ];

  const filteredComponents = searchQuery
    ? allComponents.filter(comp =>
        comp.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
        comp.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : allComponents;

  const handleSelectComponent = (type: string) => {
    onSelectComponent(type);
    onOpenChange(false);
    setSearchQuery(""); // Reset search when closing
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add Component</DialogTitle>
          <DialogDescription>
            Select a component to add to your document.
          </DialogDescription>
        </DialogHeader>

        <Command className="rounded-lg border shadow-md">
          <CommandInput
            placeholder="Search components..."
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList>
            <CommandEmpty>No components found.</CommandEmpty>
            <ScrollArea className="h-[300px]">
              <CommandGroup>
                {filteredComponents.map(component => (
                  <CommandItem
                    key={component.type}
                    onSelect={() => handleSelectComponent(component.type)}
                    className="flex items-center space-x-3 p-2 cursor-pointer"
                  >
                    <div className="text-primary">{component.icon}</div>
                    <div>
                      <p className="font-medium text-sm">{component.label}</p>
                      <p className="text-xs text-muted-foreground">{component.description}</p>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </ScrollArea>
          </CommandList>
        </Command>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentAddComponentDialog;
