
import React from "react";
import { Card } from "@/components/ui/card";
import { DraggableItemProps } from "@/types/draggable";

const DraggableItem: React.FC<DraggableItemProps> = ({
  type,
  icon,
  label,
  description,
  onDragStart,
}) => {
  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData("componentType", type);
    onDragStart(type);
  };

  return (
    <Card
      draggable
      onDragStart={handleDragStart}
      className="component-item p-3 mb-3 cursor-grab flex items-center space-x-3 hover:bg-slate-50 dark:hover:bg-slate-800 transition-all duration-200 hover:shadow-md border-slate-200 dark:border-slate-700 group"
    >
      <div className="text-primary p-2 bg-primary/10 rounded-md group-hover:bg-primary/20 transition-colors duration-200">{icon}</div>
      <div>
        <h4 className="font-medium text-sm">{label}</h4>
        {description && <p className="text-xs text-muted-foreground">{description}</p>}
      </div>
    </Card>
  );
};

export default DraggableItem;
