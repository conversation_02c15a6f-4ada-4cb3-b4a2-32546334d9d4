import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  CheckCircle,
  Edit3
} from 'lucide-react';
import SignatureCanvas from 'react-signature-canvas';
import apiService from '@/services/apiService';
import { API_BASE_URL } from '@/constants/index';
import API from '@/services/axiosAPI';
import FileUploadComponent from '@/components/common/FileUploadComponent';

// API endpoints (following test.js pattern)
const SUBMIT_PERMIT_ACTION = (id: string) => `${API_BASE_URL}/permit-report-submit/${id}`;
const ACKNOWLEDGE_PERMIT_ACTION = (id: string) => `${API_BASE_URL}/permit-reports-acknowledge/${id}`;
const FILE_URL = `${API_BASE_URL}/files`;
const GET_USER_ROLE_BY_MODE = `${API_BASE_URL}/users/get_users`;

// Interfaces (following test.js pattern)
interface EPermitActionModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  action: ActionData | null;
  permitDetails: any; // Using any to match test.js applicationDetails
}

interface ActionData {
  id: string;
  maskId: string;
  actionToBeTaken: string;
  actionType?: string;
  applicationId?: string;
  submittedBy: {
    firstName: string;
    lastName?: string;
  };
  applicationDetails: {
    dueDate: string;
  };
  created: string;
}

interface UserOption {
  label: string;
  value: string;
}

const EPermitActionModal: React.FC<EPermitActionModalProps> = ({
  open,
  onOpenChange,
  action,
  permitDetails
}) => {
  // State variables (following test.js pattern)
  const signRef = useRef<SignatureCanvas>(null);
  const [apiStatus, setApiStatus] = useState('');
  const [signs, setSign] = useState('');
  const [signModal, setSignModal] = useState(false);
  const [comments, setComments] = useState('');
  const [showErrors, setShowErrors] = useState(false);
  const [assessor, setAssessor] = useState<UserOption[]>([]);
  const [assessorId, setAssessorId] = useState('');
  const [uploads, setUploads] = useState<string[]>([]);
  const { toast } = useToast();

  // Helper function to format date with time
  const formatDateTime = (dateString: string) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch {
      return dateString; // Return original string if parsing fails
    }
  };

  // Get crew list function (following test.js pattern)
  const getCrewList = useCallback(async (type: string) => {
    try {
      const response = await apiService.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: permitDetails?.locationOneId || '',
        locationTwoId: permitDetails?.locationTwoId || '',
        locationThreeId: permitDetails?.locationThreeId || '',
        locationFourId: permitDetails?.locationFourId || '',
        mode: type
      });

      if (response) {
        const data = response.map((item: { firstName: string; id: string }) => ({
          label: item.firstName,
          value: item.id
        }));
        setAssessor(data);
      }
    } catch (error) {
      console.error("Error fetching crew list:", error);
    }
  }, [permitDetails?.locationOneId, permitDetails?.locationTwoId, permitDetails?.locationThreeId, permitDetails?.locationFourId]);

  // useEffect to fetch crew list (following test.js pattern)
  useEffect(() => {
    if (action?.actionType === 'Review') {
      getCrewList('eptwAssessor');
    } else if (action?.actionType === 'Assess') {
      getCrewList('eptwApprover');
    }
  }, [action, getCrewList]);

  // Validation function (following test.js pattern)
  const isValid = () => {
    if (action?.actionType === 'Acknowledgement') {
      return signs !== '';
    } else {
      if (apiStatus === 'Approve') {
        return action?.actionType === 'Approve' ? comments !== '' && signs !== '' : signs !== '' && assessorId !== '';
      }
      if (apiStatus === 'Return') {
        return comments !== '';
      }
      return false;
    }
  };

  // Convert data URI to file (following test.js pattern)
  const dataURItoFile = (dataURI: string, filename: string) => {
    const byteString = atob(dataURI.split(",")[1]);
    const mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
    const ab = new ArrayBuffer(byteString.length);
    const dw = new DataView(ab);
    for (let i = 0; i < byteString.length; i++) {
      dw.setUint8(i, byteString.charCodeAt(i));
    }
    return new File([ab], filename, { type: mimeString });
  };

  // Upload signature function (following test.js pattern)
  const uploadSignature = async () => {
    const filename = `${new Date().getTime()}_captin_sign.png`;
    const formData = new FormData();
    const signatureFile = dataURItoFile(signs, filename);
    formData.append('file', signatureFile);

    try {
      const response = await apiService.post(FILE_URL, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });

      if (response) {
        return response.files[0].originalname;
      } else {
        throw new Error("File upload failed.");
      }
    } catch (error) {
      console.error("File upload error:", error);
      throw error;
    }
  };

  // Handle acknowledgement submit function (following test.js pattern)
  const handleAcknowledgeSubmit = async () => {
    setShowErrors(true);
    if (isValid()) {
      try {
        const signatureFileName = await uploadSignature();

        const payload = {
          acknowledgementStatus: {
            signature: signatureFileName,
            comments: '',
            uploads: uploads
          },
        };

        const response = await API.patch(ACKNOWLEDGE_PERMIT_ACTION(action!.id), payload);

        if (response.status === 204) {
          toast({
            title: "Success",
            description: "Permit Acknowledged Successfully",
          });
          onOpenChange(false);
        }
      } catch (error) {
        console.error("Error:", error);
        toast({
          title: "Error",
          description: "Failed to acknowledge permit. Please try again.",
          variant: "destructive"
        });
      }
    } else {
      toast({
        title: "Validation Error",
        description: "Please fill all the required fields",
        variant: "destructive"
      });
    }
  };

  // Handle submit function (following test.js pattern)
  const handleSubmit = async () => {
    setShowErrors(true);
    if (isValid()) {
      try {
        let payload: any;

        if (apiStatus === "Return") {
          payload = {
            comments: comments,
            status: "Returned"
          };
        } else {
          const signatureFileName = await uploadSignature();

          payload = {
            comments: comments,
            ...(action?.actionType === 'Review'
              ? { reviewerStatus: { signature: signatureFileName }, assessorId: assessorId }
              : action?.actionType === 'Assess'
                ? { assessorStatus: { signature: signatureFileName }, approverId: assessorId }
                : { approverStatus: { signature: signatureFileName } })
          };
        }

        const response = await API.patch(SUBMIT_PERMIT_ACTION(action!.id), payload);

        if (response.status === 204 || response.status === 200) {
          toast({
            title: "Success",
            description: "Submitted Successfully",
          });
          onOpenChange(false);
        }
      } catch (error) {
        console.error("Error:", error);
        toast({
          title: "Error",
          description: "Failed to submit action. Please try again.",
          variant: "destructive"
        });
      }
    } else {
      toast({
        title: "Validation Error",
        description: "Please fill all the required fields",
        variant: "destructive"
      });
    }
  };



  if (!action || !permitDetails) {
    return null;
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent 
          className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto"
          onInteractOutside={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              Permit to Work - Action {action.maskId}
            </DialogTitle>
            <div className="flex items-center gap-4 mt-2">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Permit:</span>
                <span className="text-sm">#{permitDetails.maskId || ''}</span>
                <Badge className="bg-blue-500 text-white">{permitDetails.status}</Badge>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Action Type:</span>
                <Badge variant="outline">{action.actionType || 'General'}</Badge>
              </div>
            </div>
          </DialogHeader>

          <div className="space-y-6">
            {/* Action Details */}
            <Card className="border border-blue-200 bg-blue-50">
              <CardContent className="p-4">
                <h3 className="text-lg font-semibold text-blue-900 mb-3">Action Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-blue-700 font-medium">Action ID</p>
                    <p className="text-blue-900 font-semibold">{action.maskId}</p>
                  </div>
                  <div>
                    <p className="text-blue-700 font-medium">Action Type</p>
                    <p className="text-blue-900 font-semibold">{action.actionType || 'General'}</p>
                  </div>
                  <div>
                    <p className="text-blue-700 font-medium">Application ID</p>
                    <p className="text-blue-900 font-semibold">{action.applicationId || 'N/A'}</p>
                  </div>
                  <div className="md:col-span-3">
                    <p className="text-blue-700 font-medium">Action Required</p>
                    <p className="text-blue-900">{action.actionToBeTaken}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Clean Permit Details */}
            <Card className="border border-gray-200">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Associated Permit Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Work Type</p>
                    <p className="font-medium">{permitDetails.permitWorkType || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Status</p>
                    <Badge variant="secondary">{permitDetails.status || 'N/A'}</Badge>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Start Date</p>
                    <p className="font-medium">{formatDateTime(permitDetails.permitStartDate)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">End Date</p>
                    <p className="font-medium">{formatDateTime(permitDetails.permitEndDate)}</p>
                  </div>
                  <div className="md:col-span-2">
                    <p className="text-sm text-gray-600">Description</p>
                    <p className="font-medium">{permitDetails.workDescription || 'N/A'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Acknowledgement File Upload Section */}
            {action.actionType === 'Acknowledgement' && (
              <Card className="border border-gray-200">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Add/Upload Evidence if any</h3>
                  <FileUploadComponent
                    onFileUpload={(fileNames) => setUploads(fileNames)}
                    fieldName="evidence"
                    description="Upload photos as evidence. You can select multiple files."
                    accept="image/*"
                    multiple={true}
                    maxFiles={10}
                    initialFiles={uploads}
                  />
                </CardContent>
              </Card>
            )}

            {/* Simple Decision Selection - Only for non-acknowledgement actions */}
            {action.actionType !== 'Acknowledgement' && (
              <Card className="border border-gray-200">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Make Your Decision</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        apiStatus === 'Approve'
                          ? 'border-green-500 bg-green-50'
                          : 'border-gray-200 hover:border-green-300'
                      }`}
                      onClick={() => setApiStatus('Approve')}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                          apiStatus === 'Approve'
                            ? 'border-green-500 bg-green-500'
                            : 'border-gray-300'
                        }`}>
                          {apiStatus === 'Approve' && (
                            <CheckCircle className="w-3 h-3 text-white" />
                          )}
                        </div>
                        <span className={`font-medium ${
                          apiStatus === 'Approve' ? 'text-green-700' : 'text-gray-700'
                        }`}>
                          Approve
                        </span>
                      </div>
                    </div>

                    <div
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        apiStatus === 'Return'
                          ? 'border-red-500 bg-red-50'
                          : 'border-gray-200 hover:border-red-300'
                      }`}
                      onClick={() => setApiStatus('Return')}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                          apiStatus === 'Return'
                            ? 'border-red-500 bg-red-500'
                            : 'border-gray-300'
                        }`}>
                          {apiStatus === 'Return' && (
                            <CheckCircle className="w-3 h-3 text-white" />
                          )}
                        </div>
                        <span className={`font-medium ${
                          apiStatus === 'Return' ? 'text-red-700' : 'text-gray-700'
                        }`}>
                          Return
                        </span>
                      </div>
                    </div>
                  </div>

                  {showErrors && !apiStatus && (
                    <p className="text-red-600 text-sm mt-3">Please select Approve or Return.</p>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Simple Comments Section - Only for non-acknowledgement actions */}
            {action.actionType !== 'Acknowledgement' && (
              <Card className="border border-gray-200">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Comments</h3>
                  <textarea
                    rows={4}
                    className={`w-full p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      showErrors && comments === ''
                        ? 'border-red-300 bg-red-50'
                        : 'border-gray-300'
                    }`}
                    placeholder="Enter your comments..."
                    value={comments}
                    onChange={(e) => setComments(e.target.value)}
                  />
                  {showErrors && comments === '' && (
                    <p className="text-red-600 text-sm mt-2">Comments are required.</p>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Simple Assessor/Approver Selection */}
            {apiStatus === 'Approve' && action.actionType !== 'Approve' && (
              <Card className="border border-gray-200">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Select {action.actionType === 'Review' ? 'Assessor' : 'Approver'}
                  </h3>
                  <select
                    value={assessorId}
                    onChange={(e) => setAssessorId(e.target.value)}
                    className={`w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      showErrors && assessorId === ''
                        ? 'border-red-300 bg-red-50'
                        : 'border-gray-300'
                    }`}
                  >
                    <option value="">
                      {action.actionType === 'Review' ? 'Select Assessor...' : 'Select Approver...'}
                    </option>
                    {assessor.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  {showErrors && assessorId === '' && (
                    <p className="text-red-600 text-sm mt-2">
                      {action.actionType === 'Review' ? 'Assessor' : 'Approver'} is required.
                    </p>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Simple Signature Section */}
            {(apiStatus === 'Approve' || action.actionType === 'Acknowledgement') && (
              <Card className="border border-gray-200">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Digital Signature</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    {action.actionType === 'Acknowledgement'
                      ? "I acknowledge that, to the best of my knowledge, the work has been completed as per the permit."
                      : "I acknowledge this permit application and confirm my approval."
                    }
                  </p>

                  {signs ? (
                    <div className="space-y-4">
                      <div className="border rounded-lg p-4 bg-gray-50">
                        <img src={signs} alt="Signature" className="h-20 mx-auto" />
                      </div>
                      <Button
                        variant="outline"
                        onClick={() => setSignModal(true)}
                        className="w-full"
                      >
                        <Edit3 className="h-4 w-4 mr-2" />
                        Update Signature
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div
                        className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-blue-400 transition-colors"
                        onClick={() => setSignModal(true)}
                      >
                        <Edit3 className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-gray-600">Click to sign</p>
                      </div>

                      {showErrors && (
                        <p className="text-red-600 text-sm">
                          {action.actionType === 'Acknowledgement'
                            ? "Signature is required for acknowledgement."
                            : "Signature is required for approval."
                          }
                        </p>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Simple Footer */}
          <div className="border-t pt-6 mt-6">
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              {action.actionType === 'Acknowledgement' ? (
                <Button
                  onClick={handleAcknowledgeSubmit}
                  disabled={!isValid()}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Permit CloseOut
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  disabled={!isValid()}
                  className={apiStatus === 'Return' ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'}
                >
                  {action.actionType === 'Review'
                    ? (apiStatus === 'Return' ? 'Return to Applicant' : 'Submit to Assessor')
                    : action.actionType === 'Assess'
                      ? (apiStatus === 'Return' ? 'Return to Applicant' : 'Submit to Approver')
                      : (apiStatus === 'Return' ? 'Return to Applicant' : 'Approve')
                  }
                </Button>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Simple Signature Modal */}
      <Dialog open={signModal} onOpenChange={setSignModal}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Digital Signature</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="border rounded-lg p-4 bg-gray-50">
              <SignatureCanvas
                ref={signRef}
                penColor="#1F3BB3"
                backgroundColor="white"
                canvasProps={{
                  width: 400,
                  height: 150,
                  className: "w-full border rounded",
                }}
              />
            </div>
            <p className="text-sm text-gray-600 text-center">
              Sign above using your mouse or touch screen
            </p>

            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => signRef.current?.clear()}
              >
                Clear
              </Button>
              <Button
                variant="outline"
                onClick={() => setSignModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  if (signRef.current) {
                    const canvas = signRef.current.getCanvas();
                    setSign(canvas.toDataURL("image/png"));
                    setSignModal(false);
                  }
                }}
              >
                Confirm
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default EPermitActionModal;
