import React, { useEffect, useState } from 'react';
import { format } from 'date-fns';
import { Download, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import apiService from '@/services/apiService';

// Import pdfMake with proper error handling
let pdfMake: any = null;

const initializePdfMake = async () => {
  if (!pdfMake) {
    try {
      // Dynamic import to avoid build issues
      const pdfMakeModule = await import('pdfmake/build/pdfmake');
      const pdfFontsModule = await import('pdfmake/build/vfs_fonts');

      pdfMake = pdfMakeModule.default || pdfMakeModule;

      // Set up fonts with proper error checking
      if (pdfFontsModule && (pdfFontsModule as any).vfs) {
        pdfMake.vfs = (pdfFontsModule as any).vfs;
      } else if (pdfFontsModule.default && (pdfFontsModule.default as any).vfs) {
        pdfMake.vfs = (pdfFontsModule.default as any).vfs;
      } else {
        console.warn('Could not load pdfMake fonts, using default fonts');
      }
    } catch (error) {
      console.error('Error initializing pdfMake:', error);
      throw new Error('Failed to initialize PDF generation');
    }
  }
  return pdfMake;
};

interface TeamMember {
  user: {
    firstName: string;
  };
}

interface Department {
  name: string;
}

interface WorkActivity {
  name: string;
}

interface TeamLeader {
  firstName: string;
}

interface RecommendationOption {
  label: string;
  value: string;
}

interface TaskItem {
  [key: number]: any;
}

interface PDFData {
  status: string;
  type: string;
  department?: Department;
  workActivity?: WorkActivity;
  nonRoutineWorkActivity?: string;
  teamLeader: TeamLeader;
  maskId: string;
  publishedDate?: string;
  updated: string;
  nextReviewDate?: string;
  raTeamMembers: TeamMember[];
  tasks: TaskItem[];
  overallRecommendationOne?: RecommendationOption;
  overallRecommendationTwo?: RecommendationOption;
  additonalRemarks?: string;
}

interface GenerateLandPdfProps {
  pdf: PDFData;
}

const GenerateLandPdf: React.FC<GenerateLandPdfProps> = ({ pdf }) => {
  const { toast } = useToast();
  const [logo, setLogo] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  useEffect(() => {
    getFetchLogo();
  }, []);

  const getFetchLogo = async () => {
    try {
      const logoName = localStorage.getItem('logo');
      if (logoName) {
        const logoUrl = await apiService.get(`/files/presigned-url/${logoName}`);
        setLogo(logoUrl);
      }
    } catch (error) {
      console.error('Error fetching logo:', error);
    }
  };

  // Enhanced color scheme for different risk levels
  const getRiskColor = (level: string | number): string => {
    const numLevel = typeof level === 'string' ? parseInt(level) : level;
    if (numLevel <= 4) return '#22c55e'; // Green - Low risk
    if (numLevel <= 9) return '#f59e0b'; // Amber - Medium risk
    if (numLevel <= 16) return '#ef4444'; // Red - High risk
    return '#dc2626'; // Dark red - Very high risk
  };

  const getRiskLabel = (level: string | number): string => {
    const numLevel = typeof level === 'string' ? parseInt(level) : level;
    if (numLevel <= 4) return 'Low';
    if (numLevel <= 9) return 'Medium';
    if (numLevel <= 16) return 'High';
    return 'Very High';
  };

  const renderRows = (rows: TaskItem[]): any[] => {
    const result: any[] = [];
    rows.forEach((item, index) => {
      const initialRisk = (item[4]?.likelyhood || 0) * (item[4]?.severity || 0);
      const residualRisk = (item[7]?.likelyhood || 0) * (item[7]?.severity || 0);

      result.push([
        {
          text: index + 1,
          alignment: 'center',
          style: 'tableCell'
        },
        {
          text: item[0]?.name ?? 'N/A',
          alignment: 'left',
          style: 'tableCell'
        },
        {
          ol: item[1]?.selected?.map((it: any) => it.name ?? 'N/A') || ['N/A'],
          style: 'tableCell'
        },
        {
          ol: item[2]?.option?.map((it: any) => {
            const currentType = it.current_type || 'N/A';
            const value = it.value || 'N/A';
            return `${currentType}: ${value}`;
          }) || ['N/A'],
          style: 'tableCell'
        },
        {
          ol: item[3]?.option?.map((it: any) => {
            const parts = [];
            if (it.current_type) parts.push(`Type: ${it.current_type}`);
            if (it.method) parts.push(`Method: ${it.method}`);
            if (it.value) parts.push(`Description: ${it.value}`);
            return parts.length > 0 ? parts.join('\n') : 'N/A';
          }) || ['N/A'],
          style: 'tableCell'
        },
        {
          text: item[4]?.severity || 'N/A',
          alignment: 'center',
          style: 'tableCell',
          fillColor: item[4]?.severity ? getRiskColor(item[4].severity) : '#f3f4f6',
          color: '#ffffff',
          bold: true
        },
        {
          text: item[4]?.likelyhood || 'N/A',
          alignment: 'center',
          style: 'tableCell',
          fillColor: item[4]?.likelyhood ? getRiskColor(item[4].likelyhood) : '#f3f4f6',
          color: '#ffffff',
          bold: true
        },
        {
          text: initialRisk || 'N/A',
          alignment: 'center',
          style: 'tableCell',
          fillColor: initialRisk ? getRiskColor(initialRisk) : '#f3f4f6',
          color: '#ffffff',
          bold: true
        },
        {
          ol: item[6]?.option?.map((it: any) => {
            const currentType = it.current_type || 'N/A';
            const value = it.value || 'N/A';
            return `${currentType}: ${value}`;
          }) || ['N/A'],
          style: 'tableCell'
        },
        {
          text: item[7]?.severity || "Nil",
          alignment: 'center',
          style: 'tableCell',
          fillColor: item[7]?.severity ? getRiskColor(item[7].severity) : '#f3f4f6',
          color: item[7]?.severity ? '#ffffff' : '#6b7280'
        },
        {
          text: item[7]?.likelyhood || "Nil",
          alignment: 'center',
          style: 'tableCell',
          fillColor: item[7]?.likelyhood ? getRiskColor(item[7].likelyhood) : '#f3f4f6',
          color: item[7]?.likelyhood ? '#ffffff' : '#6b7280'
        },
        {
          text: residualRisk || 'Nil',
          alignment: 'center',
          style: 'tableCell',
          fillColor: residualRisk ? getRiskColor(residualRisk) : '#f3f4f6',
          color: residualRisk ? '#ffffff' : '#6b7280'
        },
        {
          ol: item[6]?.option?.map((it: any) =>
            it.person?.name ? (it.person.firstName || it.person.name) : "Nil"
          ) || ['Nil'],
          style: 'tableCell'
        },
        {
          ol: item[6]?.option?.map((it: any) =>
            it.date ? format(new Date(it.date), 'dd/MM/yyyy') : "Nil"
          ) || ['Nil'],
          style: 'tableCell'
        },
        {
          text: '',
          alignment: 'center',
          style: 'tableCell'
        },
      ]);
    });
    return result;
  };

  const renderHazardAssessmentContent = (): any[] => {
    const content: any[] = [];

    // Enhanced Consequences Section
    const consequences = pdf.tasks[0]?.[0]?.option || [];
    if (consequences.length > 0) {
      content.push(
        {
          text: 'Potential Consequences',
          style: 'sectionHeader',
          margin: [0, 20, 0, 15],
          decoration: 'underline',
          decorationStyle: 'solid',
          decorationColor: '#f97316'
        },
        {
          text: 'Potential consequences of this Critical High Risk Activity on Personnel, Environment, Equipment/Property & Service due to the associated high-risk factors.',
          margin: [0, 0, 0, 15],
          style: 'sectionDescription'
        }
      );

      consequences.forEach((consequence: any, index: number) => {
        content.push({
          table: {
            widths: [40, '*'],
            body: [
              [
                {
                  text: `${index + 1}`,
                  bold: true,
                  alignment: 'center',
                  fillColor: '#f97316',
                  color: '#ffffff',
                  margin: [8, 8],
                  fontSize: 14
                },
                {
                  stack: [
                    {
                      text: `Impact on: ${consequence.current_type || 'Not Specified'}`,
                      bold: true,
                      color: '#ea580c',
                      fontSize: 12,
                      margin: [0, 0, 0, 5]
                    },
                    {
                      text: consequence.value || 'No description provided',
                      fontSize: 11,
                      lineHeight: 1.3,
                      color: '#374151'
                    }
                  ],
                  margin: [10, 8]
                }
              ]
            ]
          },
          layout: {
            hLineWidth: () => 1.5,
            vLineWidth: () => 1.5,
            hLineColor: () => '#fed7aa',
            vLineColor: () => '#fed7aa',
            fillColor: (rowIndex: number) => rowIndex === 0 ? '#fef3e2' : null
          },
          margin: [0, 0, 0, 8]
        });
      });
    }

    // Enhanced Controls Section
    const controls = pdf.tasks[0]?.[1]?.option || [];
    if (controls.length > 0) {
      content.push(
        {
          text: 'Necessary Controls',
          style: 'sectionHeader',
          margin: [0, 25, 0, 15],
          decoration: 'underline',
          decorationStyle: 'solid',
          decorationColor: '#3b82f6'
        },
        {
          text: 'Controls to manage the hazards, associated risks, and potential consequences of this Critical High Risk Activity. These controls will also reflect in the Permit to Work Applications when the work involves these High Risk Activities.',
          margin: [0, 0, 0, 15],
          style: 'sectionDescription'
        }
      );

      controls.forEach((control: any, index: number) => {
        const badges: string[] = [];
        if (control.current_type) badges.push(`Type: ${control.current_type}`);
        if (control.method) badges.push(`Method: ${control.method}`);

        const statusBadges: string[] = [];
        if (control.required) statusBadges.push('Required');
        if (control.validity) statusBadges.push('Valid');

        content.push({
          table: {
            widths: [40, '*'],
            body: [
              [
                {
                  text: `${index + 1}`,
                  bold: true,
                  alignment: 'center',
                  fillColor: '#3b82f6',
                  color: '#ffffff',
                  margin: [8, 8],
                  fontSize: 14
                },
                {
                  stack: [
                    {
                      text: badges.join(' | '),
                      bold: true,
                      color: '#1d4ed8',
                      fontSize: 12,
                      margin: [0, 0, 0, 3]
                    },
                    ...(statusBadges.length > 0 ? [{
                      text: statusBadges.join(' • '),
                      fontSize: 10,
                      color: '#059669',
                      bold: true,
                      margin: [0, 0, 0, 5]
                    }] : []),
                    {
                      text: control.value || 'No description provided',
                      fontSize: 11,
                      lineHeight: 1.3,
                      color: '#374151'
                    }
                  ],
                  margin: [10, 8]
                }
              ]
            ]
          },
          layout: {
            hLineWidth: () => 1.5,
            vLineWidth: () => 1.5,
            hLineColor: () => '#bfdbfe',
            vLineColor: () => '#bfdbfe',
            fillColor: (rowIndex: number) => rowIndex === 0 ? '#eff6ff' : null
          },
          margin: [0, 0, 0, 8]
        });
      });
    }

    return content;
  };

  const generatePdf = async () => {
    if (pdf.status !== 'Published') {
      toast({
        title: "PDF Generation Disabled",
        description: "The Risk Assessment needs to be affirmed by all team members before it can be exported to PDF!",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Initialize pdfMake
      const pdfMakeInstance = await initializePdfMake();
      // Enhanced header with logo and better styling
      const headerInfo = {
        table: {
          widths: ['25%', '25%', '25%', '25%'],
          body: [
            [
              ...(pdf.type === 'Routine'
                ? [
                    { text: 'Department:', style: 'headerLabel' },
                    { text: pdf.department?.name || 'N/A', style: 'headerValue' },
                    { text: 'Work Activity:', style: 'headerLabel' },
                    { text: pdf.workActivity?.name || 'N/A', style: 'headerValue' }
                  ]
                : pdf.type === 'High-Risk Hazard'
                ? [
                    { text: 'Hazard Name:', style: 'headerLabel' },
                    { text: pdf.nonRoutineWorkActivity || 'N/A', style: 'headerValue', colSpan: 3 },
                    {},
                    {}
                  ]
                : [
                    { text: 'Work Activity:', style: 'headerLabel' },
                    { text: pdf.nonRoutineWorkActivity || 'N/A', style: 'headerValue', colSpan: 3 },
                    {},
                    {}
                  ]
              ),
            ],
            [
              { text: 'Assessment Type:', style: 'headerLabel' },
              { text: pdf.type, style: 'headerValue' },
              { text: 'Team Leader:', style: 'headerLabel' },
              { text: pdf.teamLeader.firstName, style: 'headerValue' },
            ],
            [
              { text: 'RA ID:', style: 'headerLabel' },
              { text: pdf.maskId, style: 'headerValue' },
              { text: 'First Release:', style: 'headerLabel' },
              { text: pdf.publishedDate ? format(new Date(pdf.publishedDate), 'dd/MM/yyyy') : 'N/A', style: 'headerValue' },
            ],
            [
              { text: 'Last Updated:', style: 'headerLabel' },
              { text: format(new Date(pdf.updated), 'dd/MM/yyyy'), style: 'headerValue' },
              { text: 'Next Review:', style: 'headerLabel' },
              { text: pdf.nextReviewDate ? format(new Date(pdf.nextReviewDate), 'dd/MM/yyyy') : 'N/A', style: 'headerValue' },
            ],
            [
              { text: 'Team Members:', style: 'headerLabel' },
              {
                text: pdf.raTeamMembers?.map(item => item.user.firstName).join(', ') || 'N/A',
                style: 'headerValue',
                colSpan: 3
              },
              {}, {},
            ],
          ],
        },
        layout: {
          hLineWidth: () => 1,
          vLineWidth: () => 1,
          hLineColor: () => '#e5e7eb',
          vLineColor: () => '#e5e7eb',
          fillColor: (rowIndex: number) => rowIndex % 2 === 0 ? '#f9fafb' : '#ffffff'
        },
        margin: [0, 0, 0, 20]
      };

      // Enhanced document header with company branding
      let content: any[] = [
        ...(logo ? [{
          image: logo,
          width: 80,
          alignment: 'center',
          margin: [0, 0, 0, 20]
        }] : []),
        {
          text: 'RISK ASSESSMENT REPORT',
          style: 'documentTitle',
          alignment: 'center',
          margin: [0, 0, 0, 10]
        },
        {
          text: `Assessment Type: ${pdf.type}`,
          style: 'documentSubtitle',
          alignment: 'center',
          margin: [0, 0, 0, 20]
        },
        headerInfo
      ];

      if (pdf.type === 'High-Risk Hazard') {
        // Enhanced High-Risk Hazard Assessment Content
        content = content.concat(renderHazardAssessmentContent());

        // Enhanced Team Leader Declaration
        content.push(
          {
            text: 'TEAM LEADER DECLARATION',
            style: 'sectionHeader',
            margin: [0, 30, 0, 15],
            decoration: 'underline',
            decorationStyle: 'solid',
            decorationColor: '#dc2626'
          },
          {
            text: 'I declare that this Critical High Risk Activity has been thoroughly assessed and the necessary controls have been identified to manage the associated risks and potential consequences. All team members have been consulted and agree with this assessment.',
            margin: [0, 0, 0, 15],
            style: 'declarationText'
          },
          {
            table: {
              widths: ['40%', '60%'],
              body: [
                [
                  { text: 'Team Leader:', style: 'declarationLabel' },
                  { text: pdf.teamLeader.firstName, style: 'declarationValue' }
                ],
                [
                  { text: 'Signature:', style: 'declarationLabel' },
                  { text: '________________________', style: 'declarationValue' }
                ],
                [
                  { text: 'Date:', style: 'declarationLabel' },
                  { text: format(new Date(pdf.updated), 'dd/MM/yyyy'), style: 'declarationValue' }
                ]
              ]
            },
            layout: {
              hLineWidth: () => 1,
              vLineWidth: () => 1,
              hLineColor: () => '#e5e7eb',
              vLineColor: () => '#e5e7eb',
              fillColor: () => '#f9fafb'
            },
            margin: [0, 0, 0, 20]
          }
        );
      } else {
        // Enhanced Routine/Non-Routine Assessment Content
        content.push({
          table: {
            headerRows: 2,
            widths: [25, 'auto', '*', '*', '*', 20, 20, 25, '*', 20, 20, 25, 'auto', 'auto', 'auto'],
            body: [
              [
                { text: 'HAZARD IDENTIFICATION', style: 'tableHeader', alignment: 'center', colSpan: 4 }, {}, {}, {},
                { text: 'INITIAL RISK EVALUATION', style: 'tableHeader', alignment: 'center', colSpan: 4 }, {}, {}, {},
                { text: 'RISK CONTROL MEASURES', style: 'tableHeader', alignment: 'center', colSpan: 7 }, {}, {}, {}, {}, {}, {}
              ],
              [
                { text: 'Ref', style: 'tableSubHeader', alignment: 'center' },
                { text: 'Sub-Activity', style: 'tableSubHeader', alignment: 'center' },
                { text: 'Hazard', style: 'tableSubHeader', alignment: 'center' },
                { text: 'Consequence', style: 'tableSubHeader', alignment: 'center' },
                { text: 'Current Controls', style: 'tableSubHeader', alignment: 'center' },
                { text: 'S', style: 'tableSubHeader', alignment: 'center' },
                { text: 'L', style: 'tableSubHeader', alignment: 'center' },
                { text: 'Risk', style: 'tableSubHeader', alignment: 'center' },
                { text: 'Additional Controls', style: 'tableSubHeader', alignment: 'center' },
                { text: 'S', style: 'tableSubHeader', alignment: 'center' },
                { text: 'L', style: 'tableSubHeader', alignment: 'center' },
                { text: 'Risk', style: 'tableSubHeader', alignment: 'center' },
                { text: 'Responsible Person', style: 'tableSubHeader', alignment: 'center' },
                { text: 'Due Date', style: 'tableSubHeader', alignment: 'center' },
                { text: 'Remarks', style: 'tableSubHeader', alignment: 'center' },
              ],
              ...renderRows(pdf.tasks),
            ],
          },
          layout: {
            hLineWidth: () => 1,
            vLineWidth: () => 1,
            hLineColor: () => '#d1d5db',
            vLineColor: () => '#d1d5db',
            fillColor: (rowIndex: number, node: any) => {
              if (rowIndex === 0) return '#1f2937';
              if (rowIndex === 1) return '#374151';
              return rowIndex % 2 === 0 ? '#f9fafb' : '#ffffff';
            }
          },
          margin: [0, 0, 0, 20]
        });

        // Enhanced Recommendations Section
        content.push(
          {
            text: 'TEAM RECOMMENDATIONS',
            style: 'sectionHeader',
            margin: [0, 25, 0, 15],
            decoration: 'underline',
            decorationStyle: 'solid',
            decorationColor: '#059669'
          }
        );

        if (pdf.overallRecommendationOne?.label) {
          content.push({
            table: {
              widths: ['*'],
              body: [
                [{
                  text: `1. ${pdf.overallRecommendationOne.label}`,
                  style: 'recommendationText',
                  fillColor: pdf.overallRecommendationOne.value === "0" ? "#22c55e" :
                            pdf.overallRecommendationOne.value === "1" ? "#f59e0b" : "#ef4444",
                  color: '#ffffff',
                  margin: [10, 8]
                }]
              ]
            },
            layout: 'noBorders',
            margin: [0, 0, 0, 5]
          });
        }

        if (pdf.overallRecommendationTwo?.label) {
          content.push({
            table: {
              widths: ['*'],
              body: [
                [{
                  text: `2. ${pdf.overallRecommendationTwo.label}`,
                  style: 'recommendationText',
                  fillColor: '#6b7280',
                  color: '#ffffff',
                  margin: [10, 8]
                }]
              ]
            },
            layout: 'noBorders',
            margin: [0, 0, 0, 5]
          });
        }

        if (pdf.additonalRemarks) {
          content.push({
            table: {
              widths: ['*'],
              body: [
                [{
                  text: `3. Additional Remarks: ${pdf.additonalRemarks}`,
                  style: 'recommendationText',
                  fillColor: '#6b7280',
                  color: '#ffffff',
                  margin: [10, 8]
                }]
              ]
            },
            layout: 'noBorders',
            margin: [0, 0, 0, 20]
          });
        }
      }

      // Enhanced PDF document definition with comprehensive styling
      const dd = {
        content,
        pageSize: pdf.type === 'High-Risk Hazard' ? 'A4' : 'A3',
        pageOrientation: pdf.type === 'High-Risk Hazard' ? 'portrait' : 'landscape',
        pageMargins: pdf.type === 'High-Risk Hazard' ? [25, 30, 25, 40] : [20, 25, 20, 35],
        footer: function (currentPage: number, pageCount: number) {
          return {
            columns: [
              { text: `Generated on: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`, fontSize: 8, color: '#6b7280' },
              { text: `Page ${currentPage} of ${pageCount}`, fontSize: 8, color: '#6b7280', alignment: 'right' }
            ],
            margin: [25, 10]
          };
        },
        styles: {
          documentTitle: {
            fontSize: 24,
            bold: true,
            color: '#1f2937'
          },
          documentSubtitle: {
            fontSize: 14,
            color: '#6b7280',
            italics: true
          },
          sectionHeader: {
            fontSize: 16,
            bold: true,
            color: '#374151'
          },
          sectionDescription: {
            fontSize: 11,
            color: '#6b7280',
            italics: true,
            lineHeight: 1.3
          },
          headerLabel: {
            fontSize: 10,
            bold: true,
            color: '#374151'
          },
          headerValue: {
            fontSize: 10,
            color: '#1f2937'
          },
          tableHeader: {
            fontSize: 10,
            bold: true,
            color: '#ffffff',
            fillColor: '#1f2937',
            margin: [2, 4, 2, 4]
          },
          tableSubHeader: {
            fontSize: 9,
            bold: true,
            color: '#ffffff',
            fillColor: '#374151',
            margin: [2, 3, 2, 3]
          },
          tableCell: {
            fontSize: 8,
            color: '#374151',
            lineHeight: 1.1,
            margin: [2, 3, 2, 3]
          },
          declarationText: {
            fontSize: 11,
            color: '#374151',
            italics: true,
            lineHeight: 1.4
          },
          declarationLabel: {
            fontSize: 11,
            bold: true,
            color: '#374151'
          },
          declarationValue: {
            fontSize: 11,
            color: '#1f2937'
          },
          recommendationText: {
            fontSize: 11,
            bold: true,
            lineHeight: 1.3
          }
        },
      };

      // Generate and download PDF
      const pdfDoc = pdfMakeInstance.createPdf(dd);
      const fileName = `${pdf.maskId}_Risk_Assessment_${format(new Date(), 'yyyyMMdd_HHmm')}.pdf`;

      pdfDoc.download(fileName);

      toast({
        title: "PDF Generated Successfully",
        description: `Risk Assessment PDF has been downloaded as ${fileName}`,
        variant: "default",
      });

    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: "PDF Generation Failed",
        description: "An error occurred while generating the PDF. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Button
      variant={pdf.status === 'Published' ? 'outline' : 'secondary'}
      size="sm"
      onClick={generatePdf}
      disabled={isGenerating || pdf.status !== 'Published'}
      className={`relative transition-all duration-200 ${
        pdf.status === 'Published'
          ? 'bg-blue-50 hover:bg-blue-100 border-blue-300 text-blue-700 shadow-md hover:shadow-lg'
          : 'bg-gray-300 text-gray-500 cursor-not-allowed'
      }`}
    >
      {isGenerating ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Generating...
        </>
      ) : (
        <>
          <Download className="h-4 w-4 mr-2" />
          Export PDF
        </>
      )}
    </Button>
  );
};

export default GenerateLandPdf;
