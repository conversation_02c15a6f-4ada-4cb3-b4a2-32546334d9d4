import React, { useState, useEffect } from 'react';
import Gallery from "react-photo-gallery";
import { X, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';

// Define the photo type for react-photo-gallery
interface GalleryPhoto {
  src: string;
  width: number;
  height: number;
  key?: string;
}

// Define the photo type for our component props
interface Photo {
  src: string;
  width?: number;
  height?: number;
}

interface GalleryPageProps {
  photos: Photo[];
  alt?: string;
  width?: string;
}

const GalleryPage: React.FC<GalleryPageProps> = ({ photos }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [photoIndex, setPhotoIndex] = useState(0);

  // Handle keyboard events when modal is open
  useEffect(() => {
    if (isOpen) {
      // Handle keyboard events
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          setIsOpen(false);
        } else if (e.key === 'ArrowLeft' && photoIndex > 0) {
          setPhotoIndex(photoIndex - 1);
        } else if (e.key === 'ArrowRight' && photoIndex < photos.length - 1) {
          setPhotoIndex(photoIndex + 1);
        }
      };

      document.addEventListener('keydown', handleKeyDown);

      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isOpen, photoIndex, photos.length]);

  // Format photos for react-photo-gallery (requires width and height)
  // Use a fixed aspect ratio for better appearance
  const formattedPhotos: GalleryPhoto[] = photos.map((photo, index) => ({
    src: photo.src,
    width: photo.width || 4,  // 4:3 aspect ratio is common for images
    height: photo.height || 3,
    key: `photo-${index}`
  }));



  const openLightbox = (_event: React.MouseEvent, { index }: { photo: GalleryPhoto, index: number }) => {
    setPhotoIndex(index);
    setIsOpen(true);
  };

  const closeModal = () => {
    setIsOpen(false);
  };

  const nextImage = () => {
    if (photoIndex < photos.length - 1) {
      setPhotoIndex(photoIndex + 1);
    }
  };

  const prevImage = () => {
    if (photoIndex > 0) {
      setPhotoIndex(photoIndex - 1);
    }
  };

  return (
    <div className="gallery-wrapper">
      <div className="gallery-container">
        <Gallery
          photos={formattedPhotos}
          onClick={openLightbox}
          targetRowHeight={200}
          margin={5}
        />

        {/* Simple Image Modal using Dialog */}
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="max-w-[100vw] max-h-[100vh] w-full h-full p-0 border-0 bg-black">
            <div className="relative w-full h-full flex items-center justify-center">
              {/* Close Button */}
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-6 right-6 z-10 text-white hover:bg-white/20 rounded-full bg-black/50 h-12 w-12"
                onClick={closeModal}
              >
                <X className="h-8 w-8" />
              </Button>

              {/* Navigation Buttons */}
              {photos.length > 1 && (
                <>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute left-6 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20 rounded-full bg-black/50 h-12 w-12"
                    onClick={(e) => {
                      e.stopPropagation();
                      prevImage();
                    }}
                    disabled={photoIndex === 0}
                  >
                    <ChevronLeft className="h-8 w-8" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-6 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20 rounded-full bg-black/50 h-12 w-12"
                    onClick={(e) => {
                      e.stopPropagation();
                      nextImage();
                    }}
                    disabled={photoIndex === photos.length - 1}
                  >
                    <ChevronRight className="h-8 w-8" />
                  </Button>
                </>
              )}

              {/* Image Container */}
              <div className="relative w-full h-full flex items-center justify-center p-4">
                <img
                  src={photos[photoIndex].src}
                  alt={`Image ${photoIndex + 1}`}
                  className="w-auto h-auto object-contain"
                  style={{
                    maxWidth: 'calc(100vw - 8rem)',
                    maxHeight: 'calc(100vh - 8rem)',
                    minWidth: '60vw',
                    minHeight: '60vh'
                  }}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>

              {/* Image Counter */}
              {photos.length > 1 && (
                <div className="absolute bottom-8 left-1/2 -translate-x-1/2 text-white bg-black/70 px-6 py-3 rounded-full text-lg font-medium">
                  {photoIndex + 1} of {photos.length}
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default GalleryPage;
