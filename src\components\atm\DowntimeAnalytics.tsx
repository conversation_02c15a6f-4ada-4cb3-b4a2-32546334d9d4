import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import {
  TrendingDown,
  Clock,
  AlertTriangle,
  BarChart3,
  Download,
  Filter,
  Calendar,
  Wrench
} from 'lucide-react';
import { DowntimeRecord, DowntimeReason, DowntimeCategory, DowntimeImpact } from '@/types/atm';

const DowntimeAnalytics = () => {
  const [downtimeRecords, setDowntimeRecords] = useState<DowntimeRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadDowntimeRecords();
  }, []);

  const loadDowntimeRecords = async () => {
    try {
      setIsLoading(true);
      // Mock data for demo
      setDowntimeRecords(getMockDowntimeRecords());
      toast({
        title: "Demo Mode",
        description: "Using mock downtime data for demonstration",
        variant: "default"
      });
    } catch (error) {
      console.error('Error loading downtime records:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getMockDowntimeRecords = (): DowntimeRecord[] => [
    {
      id: '1',
      assetId: '1',
      startTime: '2024-02-01T10:00:00Z',
      endTime: '2024-02-01T14:00:00Z',
      duration: 240, // 4 hours
      reason: DowntimeReason.PLANNED_MAINTENANCE,
      category: DowntimeCategory.MECHANICAL,
      description: 'Scheduled filter replacement',
      impact: DowntimeImpact.LOW,
      correctiveActions: ['Replaced filters', 'Tested system'],
      reportedBy: 'maintenance_team',
      created: '2024-02-01T10:00:00Z',
      updated: '2024-02-01T14:30:00Z'
    },
    {
      id: '2',
      assetId: '2',
      startTime: '2024-02-05T08:30:00Z',
      endTime: '2024-02-05T16:00:00Z',
      duration: 450, // 7.5 hours
      reason: DowntimeReason.EQUIPMENT_FAILURE,
      category: DowntimeCategory.MECHANICAL,
      description: 'Pressure vessel safety valve failure',
      impact: DowntimeImpact.HIGH,
      rootCause: 'Valve seat corrosion due to chemical exposure',
      correctiveActions: ['Replaced safety valve', 'Inspected other valves', 'Updated maintenance schedule'],
      reportedBy: 'operator1',
      created: '2024-02-05T08:30:00Z',
      updated: '2024-02-05T16:30:00Z'
    },
    {
      id: '3',
      assetId: '3',
      startTime: '2024-02-10T13:00:00Z',
      endTime: '2024-02-10T18:30:00Z',
      duration: 330, // 5.5 hours
      reason: DowntimeReason.UNPLANNED_MAINTENANCE,
      category: DowntimeCategory.MECHANICAL,
      description: 'Pump bearing failure',
      impact: DowntimeImpact.MEDIUM,
      rootCause: 'Bearing overheating due to insufficient lubrication',
      correctiveActions: ['Replaced bearing', 'Improved lubrication schedule', 'Added vibration monitoring'],
      reportedBy: 'technician1',
      created: '2024-02-10T13:00:00Z',
      updated: '2024-02-10T19:00:00Z'
    }
  ];

  const getTotalDowntime = () => {
    return downtimeRecords.reduce((total, record) => total + (record.duration || 0), 0);
  };

  const getDowntimeByReason = () => {
    const reasonStats: { [key: string]: number } = {};
    downtimeRecords.forEach(record => {
      reasonStats[record.reason] = (reasonStats[record.reason] || 0) + (record.duration || 0);
    });
    return Object.entries(reasonStats).map(([reason, duration]) => ({
      reason,
      duration,
      percentage: (duration / getTotalDowntime()) * 100
    }));
  };

  const getDowntimeByCategory = () => {
    const categoryStats: { [key: string]: number } = {};
    downtimeRecords.forEach(record => {
      categoryStats[record.category] = (categoryStats[record.category] || 0) + (record.duration || 0);
    });
    return Object.entries(categoryStats).map(([category, duration]) => ({
      category,
      duration,
      percentage: (duration / getTotalDowntime()) * 100
    }));
  };

  const getImpactBadge = (impact: DowntimeImpact) => {
    const variants = {
      [DowntimeImpact.LOW]: 'outline',
      [DowntimeImpact.MEDIUM]: 'secondary',
      [DowntimeImpact.HIGH]: 'destructive',
      [DowntimeImpact.CRITICAL]: 'destructive'
    };
    return <Badge variant={variants[impact] as any}>{impact}</Badge>;
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const mtbf = 720; // Mock MTBF in hours
  const mttr = getTotalDowntime() / downtimeRecords.length / 60; // MTTR in hours

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
        <div className="text-sm text-muted-foreground">
          Last 30 days analysis
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Downtime</p>
                <p className="text-2xl font-bold">{formatDuration(getTotalDowntime())}</p>
                <p className="text-xs text-muted-foreground">
                  {downtimeRecords.length} incidents
                </p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">MTBF</p>
                <p className="text-2xl font-bold">{mtbf}h</p>
                <p className="text-xs text-muted-foreground">
                  Mean Time Between Failures
                </p>
              </div>
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">MTTR</p>
                <p className="text-2xl font-bold">{mttr.toFixed(1)}h</p>
                <p className="text-xs text-muted-foreground">
                  Mean Time To Repair
                </p>
              </div>
              <Wrench className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Availability</p>
                <p className="text-2xl font-bold">
                  {((mtbf / (mtbf + mttr)) * 100).toFixed(1)}%
                </p>
                <p className="text-xs text-muted-foreground">
                  System availability
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Downtime Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Downtime by Reason</CardTitle>
            <CardDescription>Distribution of downtime causes</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {getDowntimeByReason().map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>{item.reason.replace(/_/g, ' ')}</span>
                    <span className="font-medium">
                      {formatDuration(item.duration)} ({item.percentage.toFixed(1)}%)
                    </span>
                  </div>
                  <Progress value={item.percentage} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Downtime by Category</CardTitle>
            <CardDescription>Technical category breakdown</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {getDowntimeByCategory().map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>{item.category}</span>
                    <span className="font-medium">
                      {formatDuration(item.duration)} ({item.percentage.toFixed(1)}%)
                    </span>
                  </div>
                  <Progress value={item.percentage} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Downtime Events */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Downtime Events</CardTitle>
          <CardDescription>Latest equipment downtime incidents</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {downtimeRecords.map(record => (
              <div key={record.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="h-5 w-5 text-orange-600" />
                    <div>
                      <h4 className="font-medium">{record.description}</h4>
                      <p className="text-sm text-muted-foreground">
                        Asset ID: {record.assetId} • {record.reason.replace(/_/g, ' ')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getImpactBadge(record.impact)}
                    <Badge variant="outline">{record.category}</Badge>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Duration:</span> {formatDuration(record.duration || 0)}
                  </div>
                  <div>
                    <span className="font-medium">Start:</span> {new Date(record.startTime).toLocaleString()}
                  </div>
                  <div>
                    <span className="font-medium">End:</span> {record.endTime ? new Date(record.endTime).toLocaleString() : 'Ongoing'}
                  </div>
                </div>

                {record.rootCause && (
                  <div className="text-sm">
                    <span className="font-medium">Root Cause:</span> {record.rootCause}
                  </div>
                )}

                {record.correctiveActions.length > 0 && (
                  <div className="text-sm">
                    <span className="font-medium">Corrective Actions:</span>
                    <ul className="list-disc list-inside mt-1 text-muted-foreground">
                      {record.correctiveActions.map((action, index) => (
                        <li key={index}>{action}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DowntimeAnalytics;
