import React from 'react';

interface AcuiZenLogoProps {
  className?: string;
  animated?: boolean;
}

const AcuiZenLogo: React.FC<AcuiZenLogoProps> = ({ className = "", animated = true }) => {
  return (
    <div className={`flex flex-col items-center ${className} ${animated ? 'animate-fade-in' : ''}`}>
      <div className="flex items-baseline relative">
        <span className="text-[#0A5A8F] text-2xl font-bold transition-all duration-300 hover:scale-110">A</span>
        <span className="text-[#0A5A8F] text-xl transition-all duration-300">cui</span>
        <span className="text-[#D12027] text-2xl font-bold transition-all duration-300 hover:scale-110">Z</span>
        <span className="text-[#D12027] text-xl transition-all duration-300">en</span>
      </div>
      <span className="text-[#555555] text-[10px] tracking-wider font-medium mt-1 transition-all duration-300 hover:tracking-widest">
        THE SUPERAPP FOR WORK
      </span>
    </div>
  );
};

export default AcuiZenLogo;
