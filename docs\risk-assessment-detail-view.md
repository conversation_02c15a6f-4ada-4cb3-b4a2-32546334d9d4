# Risk Assessment Detail View

This document describes the implementation of the Risk Assessment Detail View dialog that opens when clicking on risk assessment IDs in the risk register.

## Overview

The Risk Assessment Detail View provides a comprehensive, read-only view of risk assessment data in a modern, tabbed interface. It's designed to give users quick access to all relevant information without navigating away from the main risk register page.

## Features

### 🎯 **Modern Dialog Interface**
- Large, responsive dialog (max-width: 7xl)
- Gradient header with status badges
- Clean, professional styling
- Smooth animations and transitions

### 📊 **Tabbed Content Organization**
The detail view is organized into four main tabs:

1. **Overview** - Basic information and activity configuration
2. **Risk Assessment** - Sub-activities and risk evaluation details
3. **Controls** - Recommendations and control measures
4. **Team & Signatures** - Team members and approval status

### 🎨 **Visual Design Elements**

#### **Header Section**
- Risk assessment title with type and status badges
- Creator information and timestamps
- Export PDF button
- Color-coded type badges:
  - 🔵 **Routine** - Blue theme
  - 🟠 **Non-Routine** - Orange theme
  - 🔴 **High-Risk Hazard** - Red theme

#### **Status Indicators**
- **Draft** - Gray with clock icon
- **Pending** - Yellow with clock icon
- **Published** - Green with checkmark icon

#### **Interactive Elements**
- Clickable ID in risk register table
- Hover effects on cards and buttons
- Responsive grid layouts
- Icon-enhanced sections

## Implementation Details

### **File Structure**
```
src/components/risk-assessment/
├── RiskAssessmentDetailDialog.tsx    # Main detail dialog component
└── ...

src/pages/
├── RiskAssessmentPage.tsx           # Updated with dialog integration
└── ...
```

### **Key Components**

#### **RiskAssessmentDetailDialog.tsx**
- Main dialog component with tabbed interface
- Handles data fetching and display
- Responsive design with modern UI elements
- Type-specific content rendering

#### **Integration in RiskAssessmentPage.tsx**
- Updated `handleRowClick` function to open detail dialog
- Added dialog state management
- Maintained existing functionality for other table types

### **Data Structure**
The component expects a `RiskAssessmentDetail` interface with the following structure:

```typescript
interface RiskAssessmentDetail {
  id: string;
  type: 'Routine' | 'Non-Routine' | 'High-Risk Hazard';
  title: string;
  status: 'Draft' | 'Pending' | 'Published';
  createdDate: string;
  lastUpdated: string;
  department?: { name: string };
  workActivity?: { name: string };
  nonRoutineActivity?: string;
  hazardName?: string;
  description: string;
  teamLeader: { firstName: string; lastName?: string };
  teamMembers: Array<{
    id: string;
    firstName: string;
    lastName?: string;
    signature?: string;
    signatureDate?: string;
  }>;
  // ... additional fields
}
```

## Usage

### **For Users**
1. Navigate to the Risk Assessment page
2. Go to the "Risk Register" tab
3. Click on any risk assessment ID in the table
4. The detail dialog will open with comprehensive information
5. Use the tabs to navigate between different sections
6. Close the dialog using the X button or clicking outside

### **For Developers**
The component is designed to be easily integrated with real API data:

```typescript
// Replace the mock data section with actual API call
const fetchRiskAssessmentDetails = async () => {
  setLoading(true);
  try {
    const response = await apiService.get(`/risk-assessments/${riskAssessmentId}`);
    setRiskAssessment(response.data);
  } catch (error) {
    console.error('Error fetching risk assessment details:', error);
  } finally {
    setLoading(false);
  }
};
```

## Customization

### **Styling**
The component uses Tailwind CSS classes and can be easily customized:
- Color schemes for different risk types
- Badge styles for status indicators
- Card layouts and spacing
- Typography and iconography

### **Content Sections**
Each tab can be extended with additional content:
- Add more fields to the overview section
- Include detailed risk matrices in the assessment tab
- Expand control measures with implementation timelines
- Add more signature fields or approval workflows

## Future Enhancements

### **Planned Features**
- **Edit Mode**: Allow inline editing of certain fields
- **Comments System**: Add commenting functionality for team collaboration
- **Version History**: Track changes and revisions
- **Export Options**: Multiple export formats (PDF, Excel, Word)
- **Print View**: Optimized printing layout
- **Audit Trail**: Track who viewed the assessment and when

### **Technical Improvements**
- **Caching**: Implement data caching for better performance
- **Offline Support**: Allow viewing of cached assessments offline
- **Real-time Updates**: WebSocket integration for live updates
- **Advanced Search**: Search within assessment content
- **Bulk Operations**: Select and perform actions on multiple assessments

## Accessibility

The component follows accessibility best practices:
- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- High contrast color schemes
- Focus management

## Browser Support

Compatible with all modern browsers:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Performance

Optimized for performance:
- Lazy loading of tab content
- Efficient re-rendering with React hooks
- Minimal bundle size impact
- Fast dialog open/close animations
