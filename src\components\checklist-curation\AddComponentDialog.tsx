
import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogDescription
} from "@/components/ui/dialog";
import { Command, CommandInput, CommandList, CommandEmpty, CommandGroup, CommandItem } from "@/components/ui/command";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Calendar,
  PenTool,
  Heading1,
  Heading2,
  Type,
  CheckCircle,
  ClipboardList,
  TextCursor
} from "lucide-react";

interface AddComponentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectComponent: (type: string) => void;
}

const AddComponentDialog: React.FC<AddComponentDialogProps> = ({
  open,
  onOpenChange,
  onSelectComponent,
}) => {
  const [searchQuery, setSearchQuery] = React.useState("");

  // Merged all components into a single array
  const allComponents = [
    // Content Components
    { type: 'header', icon: <Heading1 className="h-5 w-5" />, label: 'Header', description: 'Main page header' },
    { type: 'section-header', icon: <Heading2 className="h-5 w-5" />, label: 'Section Header', description: 'Section title' },
    { type: 'text-body', icon: <Type className="h-5 w-5" />, label: 'Text Body', description: 'Instructions & content' },

    // Form Components
    { type: 'sign', icon: <PenTool className="h-5 w-5" />, label: 'Sign', description: 'Signature pad' },
    { type: 'text-input', icon: <TextCursor className="h-5 w-5" />, label: 'Text Input', description: 'Text input field' },
    { type: 'checkpoint', icon: <CheckCircle className="h-5 w-5" />, label: 'Checkpoint', description: 'Single checkpoint item' },
    { type: 'checkpoint-group', icon: <ClipboardList className="h-5 w-5" />, label: 'Checkpoint Group', description: 'Group of checkpoints' },
    { type: 'date', icon: <Calendar className="h-5 w-5" />, label: 'Date', description: 'Date selector' },
  ];

  const filteredComponents = searchQuery
    ? allComponents.filter(comp =>
        comp.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
        comp.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : allComponents;

  const handleSelectComponent = (type: string) => {
    onSelectComponent(type);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add Component</DialogTitle>
          <DialogDescription>
            Select a component to add to your checklist.
          </DialogDescription>
        </DialogHeader>

        <Command className="rounded-lg border shadow-md">
          <CommandInput
            placeholder="Search components..."
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList>
            <CommandEmpty>No components found.</CommandEmpty>
            <ScrollArea className="h-[300px]">
              <CommandGroup>
                {filteredComponents.map(component => (
                  <CommandItem
                    key={component.type}
                    onSelect={() => handleSelectComponent(component.type)}
                    className="flex items-center space-x-3 p-2 cursor-pointer"
                  >
                    <div className="text-primary">{component.icon}</div>
                    <div>
                      <p className="font-medium text-sm">{component.label}</p>
                      <p className="text-xs text-muted-foreground">{component.description}</p>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </ScrollArea>
          </CommandList>
        </Command>
      </DialogContent>
    </Dialog>
  );
};

export default AddComponentDialog;
