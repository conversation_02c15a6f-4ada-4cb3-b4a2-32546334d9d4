import React from 'react';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { cn } from '@/lib/utils';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Observation } from '@/types/observation';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

// List of action owners (would come from API in a real app)
const actionOwners = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];

// Form schema
const formSchema = z.object({
  actionTaken: z.string().min(5, "Action Taken must be at least 5 characters"),
  dueDate: z.date({ required_error: "Due Date is required" }),
  actionOwner: z.string().min(1, "Action Owner is required"),
});

type FormValues = z.infer<typeof formSchema>;

interface ReviewObservationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  observation: Observation | null;
  onSubmit: (observationId: string, data: FormValues) => void;
}

const ReviewObservationModal: React.FC<ReviewObservationModalProps> = ({
  open,
  onOpenChange,
  observation,
  onSubmit
}) => {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      actionTaken: '',
      actionOwner: '',
    }
  });

  if (!observation) return null;

  const handleSubmit = (values: FormValues) => {
    onSubmit(observation.id, values);
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Review Observation</DialogTitle>
          <DialogDescription>
            Enter action details for observation {observation.id}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="space-y-4">
              {/* Display observation details */}
              <div className="bg-muted/50 p-4 rounded-md space-y-2">
                <div>
                  <span className="text-sm font-medium">ID:</span> {observation.id}
                </div>
                <div>
                  <span className="text-sm font-medium">Location:</span> {observation.location}
                </div>
                <div>
                  <span className="text-sm font-medium">Description:</span> {observation.description}
                </div>
                <div>
                  <span className="text-sm font-medium">Reported By:</span> {observation.reportedBy}
                </div>
                <div>
                  <span className="text-sm font-medium">Reported Date:</span> {format(observation.reportedDate, 'PPP')}
                </div>
              </div>

              {/* Action Taken */}
              <FormField
                control={form.control}
                name="actionTaken"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Action Taken</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe the action that was taken"
                        {...field}
                        className="min-h-[100px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Due Date */}
              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Due Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date()
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Action Owner */}
              <FormField
                control={form.control}
                name="actionOwner"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Action Owner</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select action owner" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {actionOwners.map(owner => (
                          <SelectItem key={owner} value={owner}>{owner}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit">Submit</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default ReviewObservationModal;
