
import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';

interface Tab {
  value: string;
  label: string;
  content: React.ReactNode;
  icon?: React.ReactNode;
}

interface TabsContainerProps {
  tabs: Tab[];
  defaultValue?: string;
  className?: string;
  onValueChange?: (value: string) => void;
}

const TabsContainer: React.FC<TabsContainerProps> = ({
  tabs,
  defaultValue = tabs[0]?.value,
  className = "",
  onValueChange
}) => {
  const [activeTab, setActiveTab] = useState(defaultValue);

  const handleValueChange = (value: string) => {
    setActiveTab(value);
    if (onValueChange) {
      onValueChange(value);
    }
  };

  return (
    <Tabs
      defaultValue={defaultValue}
      value={activeTab}
      onValueChange={handleValueChange}
      className={`w-full ${className}`}
    >
      <div className="border-b mb-6">
        <TabsList className="bg-transparent">
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.value}
              value={tab.value}
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none rounded-none px-4 py-3 data-[state=active]:bg-transparent"
            >
              <div className="flex items-center gap-2">
                {tab.icon}
                {tab.label}
              </div>
            </TabsTrigger>
          ))}
        </TabsList>
      </div>
      {tabs.map((tab) => (
        <TabsContent key={tab.value} value={tab.value} className="pt-2 animate-fade-in">
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  );
};

export default TabsContainer;
