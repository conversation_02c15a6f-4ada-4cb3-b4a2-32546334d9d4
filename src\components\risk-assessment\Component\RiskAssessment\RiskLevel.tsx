import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface TableData {
  id: string;
  severity: string;
  rare: string;
  unlikely: string;
  possible: string;
  likely: string;
  almostCertain: string;
}

interface TaskItem {
  severity: string | number;
  likelyhood: string | number;
  [key: number]: any;
}

interface RiskLevelProps {
  item: TaskItem[];
  tableData: TableData[];
  cellClassName: (value: number) => string;
  cellStyle: (data: any, field: string) => string;
}

const RiskLevel: React.FC<RiskLevelProps> = ({ 
  item, 
  tableData, 
  cellClassName, 
  cellStyle 
}) => {
  const [riskTable, setRiskTable] = useState<boolean>(false);

  const findMatrixValue = (idValue: string | number, columnValue: string | number): string => {
    // Map the numeric column value to the respective field name
    const columnMap: Record<number, keyof TableData> = {
      1: 'rare',
      2: 'unlikely',
      3: 'possible',
      4: 'likely',
      5: 'almostCertain'
    };

    // Get the actual column name based on the columnValue passed
    const columnKey = columnMap[Number(columnValue)];

    // Find the row that matches the given id value (e.g., "2(D)")
    const row = tableData.find(item => item.id.startsWith(`${idValue}(`));

    // If row and column exist, return the corresponding value
    if (row && row[columnKey]) {
      return row[columnKey] as string;
    }

    return '0'; // Return '0' if no match is found
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
          <div className="md:col-span-2">
            <h6 className="font-semibold">Risk Level</h6>
            <p className="text-sm text-muted-foreground italic">
              Risk Level is the measure of risk determined by evaluating both the severity of potential consequences and the likelihood of the event occurring.
            </p>
          </div>
          <div>
            <div className={`p-4 text-center font-bold rounded-lg border shadow-sm ${cellClassName(Number(item[4]?.severity) * Number(item[4]?.likelyhood))}`}>
              {findMatrixValue(item[4]?.severity, item[4]?.likelyhood)}
            </div>
          </div>
        </div>

        <Collapsible open={riskTable} onOpenChange={setRiskTable}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="mt-4 p-0 h-auto font-normal">
              Understand Risk Levels
              {riskTable ? (
                <ChevronUp className="ml-2 h-4 w-4" />
              ) : (
                <ChevronDown className="ml-2 h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-4">
            <div className="border rounded-lg overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-32"></TableHead>
                    <TableHead className="w-32"></TableHead>
                    <TableHead className="w-32 text-center">1 Rare</TableHead>
                    <TableHead className="w-32 text-center">2 Unlikely</TableHead>
                    <TableHead className="w-32 text-center">3 Possible</TableHead>
                    <TableHead className="w-32 text-center">4 Likely</TableHead>
                    <TableHead className="w-32 text-center">5 Almost Certain</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tableData.map((row, index) => (
                    <TableRow key={index}>
                      <TableCell className="text-center">{row.id}</TableCell>
                      <TableCell className="text-center">{row.severity}</TableCell>
                      <TableCell className={`text-center ${cellStyle(row, 'rare')}`}>{row.rare}</TableCell>
                      <TableCell className={`text-center ${cellStyle(row, 'unlikely')}`}>{row.unlikely}</TableCell>
                      <TableCell className={`text-center ${cellStyle(row, 'possible')}`}>{row.possible}</TableCell>
                      <TableCell className={`text-center ${cellStyle(row, 'likely')}`}>{row.likely}</TableCell>
                      <TableCell className={`text-center ${cellStyle(row, 'almostCertain')}`}>{row.almostCertain}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
};

export default RiskLevel;
